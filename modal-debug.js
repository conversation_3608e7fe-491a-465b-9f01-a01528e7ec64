/**
 * Modal Debug Script
 * 
 * Add this to your browser console to debug modal issues
 */

console.log('=== HAPD Modal Debug ===');

// Check if jQuery is loaded
if (typeof jQuery !== 'undefined') {
    console.log('✅ jQuery is loaded:', jQuery.fn.jquery);
} else {
    console.log('❌ jQuery is not loaded');
}

// Check if hapd_ajax is defined
if (typeof hapd_ajax !== 'undefined') {
    console.log('✅ hapd_ajax is defined:', hapd_ajax);
} else {
    console.log('❌ hapd_ajax is not defined');
}

// Check if HearingAidDisplay object exists
if (typeof HearingAidDisplay !== 'undefined') {
    console.log('✅ HearingAidDisplay object exists');
    console.log('Available methods:', Object.getOwnPropertyNames(HearingAidDisplay));
} else {
    console.log('❌ HearingAidDisplay object not found');
}

// Check for Learn More buttons
var learnMoreButtons = document.querySelectorAll('.hapd-learn-more-btn');
console.log('Found', learnMoreButtons.length, 'Learn More buttons');

if (learnMoreButtons.length > 0) {
    console.log('First button data:', {
        productId: learnMoreButtons[0].getAttribute('data-product-id'),
        className: learnMoreButtons[0].className
    });
}

// Test function to manually trigger modal
window.testModal = function(productId) {
    if (typeof HearingAidDisplay !== 'undefined' && HearingAidDisplay.openProductModal) {
        console.log('Testing modal for product ID:', productId);
        HearingAidDisplay.openProductModal(productId);
    } else {
        console.log('Cannot test modal - HearingAidDisplay.openProductModal not available');
    }
};

// Test function to check AJAX directly
window.testAjax = function(productId) {
    if (typeof hapd_ajax === 'undefined') {
        console.log('Cannot test AJAX - hapd_ajax not defined');
        return;
    }
    
    console.log('Testing AJAX for product ID:', productId);
    
    jQuery.ajax({
        url: hapd_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'hapd_get_product_modal',
            product_id: productId,
            nonce: hapd_ajax.nonce
        },
        success: function(response) {
            console.log('AJAX Success:', response);
        },
        error: function(xhr, status, error) {
            console.log('AJAX Error:', {
                status: status,
                error: error,
                responseText: xhr.responseText,
                statusCode: xhr.status
            });
        }
    });
};

console.log('=== Debug functions available ===');
console.log('testModal(productId) - Test modal opening');
console.log('testAjax(productId) - Test AJAX call directly');
console.log('Example: testModal(123) or testAjax(123)');
