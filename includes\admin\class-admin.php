<?php
/**
 * Admin Class
 *
 * @package HearingAidProductDisplay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin Class
 */
class HAPD_Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('admin_notices', array($this, 'admin_notices'));
        add_filter('manage_hearing_aid_product_posts_columns', array($this, 'add_custom_columns'));
        add_action('manage_hearing_aid_product_posts_custom_column', array($this, 'custom_column_content'), 10, 2);
        add_filter('manage_edit-hearing_aid_product_sortable_columns', array($this, 'sortable_columns'));
        add_action('pre_get_posts', array($this, 'custom_orderby'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Main menu page
        add_menu_page(
            __('Hearing Aid Products', 'hearing-aid-display'),
            __('Hearing Aids', 'hearing-aid-display'),
            'manage_options',
            'hearing-aid-products',
            array($this, 'admin_page'),
            'dashicons-admin-generic',
            30
        );
        
        // Submenu pages
        add_submenu_page(
            'hearing-aid-products',
            __('All Products', 'hearing-aid-display'),
            __('All Products', 'hearing-aid-display'),
            'edit_posts',
            'edit.php?post_type=hearing_aid_product'
        );
        
        add_submenu_page(
            'hearing-aid-products',
            __('Add New Product', 'hearing-aid-display'),
            __('Add New', 'hearing-aid-display'),
            'edit_posts',
            'post-new.php?post_type=hearing_aid_product'
        );
        
        add_submenu_page(
            'hearing-aid-products',
            __('Categories', 'hearing-aid-display'),
            __('Categories', 'hearing-aid-display'),
            'manage_categories',
            'edit-tags.php?taxonomy=hearing_aid_category&post_type=hearing_aid_product'
        );
        
        add_submenu_page(
            'hearing-aid-products',
            __('Brands', 'hearing-aid-display'),
            __('Brands', 'hearing-aid-display'),
            'manage_categories',
            'edit-tags.php?taxonomy=hearing_aid_brand&post_type=hearing_aid_product'
        );
        
        add_submenu_page(
            'hearing-aid-products',
            __('Settings', 'hearing-aid-display'),
            __('Settings', 'hearing-aid-display'),
            'manage_options',
            'hearing-aid-settings',
            array($this, 'settings_page')
        );
        
        add_submenu_page(
            'hearing-aid-products',
            __('Import/Export', 'hearing-aid-display'),
            __('Import/Export', 'hearing-aid-display'),
            'manage_options',
            'hearing-aid-import-export',
            array($this, 'import_export_page')
        );
    }
    
    /**
     * Admin initialization
     */
    public function admin_init() {
        // Register settings
        register_setting('hapd_settings', 'hapd_settings', array($this, 'sanitize_settings'));
        
        // Add settings sections
        add_settings_section(
            'hapd_general_settings',
            __('General Settings', 'hearing-aid-display'),
            array($this, 'general_settings_callback'),
            'hapd_settings'
        );
        
        add_settings_section(
            'hapd_display_settings',
            __('Display Settings', 'hearing-aid-display'),
            array($this, 'display_settings_callback'),
            'hapd_settings'
        );
        
        add_settings_section(
            'hapd_contact_settings',
            __('Contact Settings', 'hearing-aid-display'),
            array($this, 'contact_settings_callback'),
            'hapd_settings'
        );
        
        // Add settings fields
        $this->add_settings_fields();
    }
    
    /**
     * Add settings fields
     */
    private function add_settings_fields() {
        // General settings
        add_settings_field(
            'currency_symbol',
            __('Currency Symbol', 'hearing-aid-display'),
            array($this, 'currency_symbol_callback'),
            'hapd_settings',
            'hapd_general_settings'
        );
        
        add_settings_field(
            'currency_position',
            __('Currency Position', 'hearing-aid-display'),
            array($this, 'currency_position_callback'),
            'hapd_settings',
            'hapd_general_settings'
        );
        
        // Display settings
        add_settings_field(
            'default_layout',
            __('Default Layout', 'hearing-aid-display'),
            array($this, 'default_layout_callback'),
            'hapd_settings',
            'hapd_display_settings'
        );
        
        add_settings_field(
            'products_per_page',
            __('Products Per Page', 'hearing-aid-display'),
            array($this, 'products_per_page_callback'),
            'hapd_settings',
            'hapd_display_settings'
        );
        
        add_settings_field(
            'enable_zoom',
            __('Enable Image Zoom', 'hearing-aid-display'),
            array($this, 'enable_zoom_callback'),
            'hapd_settings',
            'hapd_display_settings'
        );
        
        // Contact settings
        add_settings_field(
            'contact_email',
            __('Contact Email', 'hearing-aid-display'),
            array($this, 'contact_email_callback'),
            'hapd_settings',
            'hapd_contact_settings'
        );
        
        add_settings_field(
            'contact_phone',
            __('Contact Phone', 'hearing-aid-display'),
            array($this, 'contact_phone_callback'),
            'hapd_settings',
            'hapd_contact_settings'
        );
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        global $post_type;
        
        // Only load on our plugin pages
        if ($post_type === 'hearing_aid_product' || strpos($hook, 'hearing-aid') !== false) {
            wp_enqueue_style(
                'hapd-admin',
                HAPD_PLUGIN_URL . 'assets/css/admin.css',
                array(),
                HAPD_VERSION
            );
            
            wp_enqueue_script(
                'hapd-admin',
                HAPD_PLUGIN_URL . 'assets/js/admin.js',
                array('jquery', 'wp-color-picker'),
                HAPD_VERSION,
                true
            );
            
            wp_enqueue_style('wp-color-picker');
            wp_enqueue_media();
            
            // Localize script
            wp_localize_script('hapd-admin', 'hapdAdmin', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('hapd_admin_nonce'),
                'strings' => array(
                    'confirm_delete' => __('Are you sure you want to delete this item?', 'hearing-aid-display'),
                    'saved' => __('Saved', 'hearing-aid-display'),
                    'error' => __('Error', 'hearing-aid-display'),
                ),
            ));
        }
    }
    
    /**
     * Admin notices
     */
    public function admin_notices() {
        // Check if products exist
        $product_count = wp_count_posts('hearing_aid_product');
        if ($product_count->publish == 0 && isset($_GET['page']) && $_GET['page'] === 'hearing-aid-products') {
            echo '<div class="notice notice-info is-dismissible">';
            echo '<p>' . sprintf(
                __('Welcome to Hearing Aid Product Display! <a href="%s">Create your first product</a> to get started.', 'hearing-aid-display'),
                admin_url('post-new.php?post_type=hearing_aid_product')
            ) . '</p>';
            echo '</div>';
        }
        
        // Check for missing settings
        $settings = get_option('hapd_settings', array());
        if (empty($settings['contact_email']) && isset($_GET['page']) && $_GET['page'] === 'hearing-aid-products') {
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p>' . sprintf(
                __('Please configure your <a href="%s">contact settings</a> to enable customer inquiries.', 'hearing-aid-display'),
                admin_url('admin.php?page=hearing-aid-settings')
            ) . '</p>';
            echo '</div>';
        }
    }
    
    /**
     * Main admin page
     */
    public function admin_page() {
        $stats = $this->get_dashboard_stats();
        
        echo '<div class="wrap">';
        echo '<div class="hapd-admin-header">';
        echo '<h1>' . __('Hearing Aid Products', 'hearing-aid-display') . '</h1>';
        echo '<a href="' . admin_url('post-new.php?post_type=hearing_aid_product') . '" class="button button-primary">';
        echo __('Add New Product', 'hearing-aid-display');
        echo '</a>';
        echo '</div>';
        
        // Dashboard stats
        echo '<div class="hapd-admin-stats">';
        foreach ($stats as $stat) {
            echo '<div class="hapd-stat-card">';
            echo '<div class="hapd-stat-number">' . $stat['number'] . '</div>';
            echo '<div class="hapd-stat-label">' . $stat['label'] . '</div>';
            echo '</div>';
        }
        echo '</div>';
        
        // Recent products
        $this->display_recent_products();
        
        // Quick actions
        echo '<div class="hapd-quick-actions">';
        echo '<h2>' . __('Quick Actions', 'hearing-aid-display') . '</h2>';
        echo '<div class="hapd-action-buttons">';
        echo '<a href="' . admin_url('post-new.php?post_type=hearing_aid_product') . '" class="button button-primary">' . __('Add Product', 'hearing-aid-display') . '</a>';
        echo '<a href="' . admin_url('edit-tags.php?taxonomy=hearing_aid_category&post_type=hearing_aid_product') . '" class="button">' . __('Manage Categories', 'hearing-aid-display') . '</a>';
        echo '<a href="' . admin_url('edit-tags.php?taxonomy=hearing_aid_brand&post_type=hearing_aid_product') . '" class="button">' . __('Manage Brands', 'hearing-aid-display') . '</a>';
        echo '<a href="' . admin_url('admin.php?page=hearing-aid-settings') . '" class="button">' . __('Settings', 'hearing-aid-display') . '</a>';
        echo '</div>';
        echo '</div>';
        
        echo '</div>';
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        echo '<div class="wrap">';
        echo '<h1>' . __('Hearing Aid Display Settings', 'hearing-aid-display') . '</h1>';
        echo '<form method="post" action="options.php">';
        
        settings_fields('hapd_settings');
        do_settings_sections('hapd_settings');
        submit_button();
        
        echo '</form>';
        echo '</div>';
    }
    
    /**
     * Import/Export page
     */
    public function import_export_page() {
        echo '<div class="wrap">';
        echo '<h1>' . __('Import/Export Products', 'hearing-aid-display') . '</h1>';

        // Handle form submissions
        if (isset($_POST['hapd_export'])) {
            $this->handle_export();
        }

        if (isset($_POST['hapd_import']) && isset($_FILES['import_file'])) {
            $this->handle_import();
        }

        echo '<div class="hapd-import-export-container">';

        // Export section
        echo '<div class="hapd-export-section">';
        echo '<h2>' . __('Export Products', 'hearing-aid-display') . '</h2>';
        echo '<p>' . __('Export all your hearing aid products to a CSV file.', 'hearing-aid-display') . '</p>';
        echo '<form method="post">';
        wp_nonce_field('hapd_export', 'hapd_export_nonce');
        echo '<input type="submit" name="hapd_export" class="button button-primary" value="' . __('Export Products', 'hearing-aid-display') . '">';
        echo '</form>';
        echo '</div>';

        // Excel Import section
        echo '<div class="hapd-excel-import-section">';
        echo '<h2>' . __('Import from Excel', 'hearing-aid-display') . '</h2>';
        echo '<p>' . __('Import products from an Excel file (.xlsx or .xls) with the following column structure:', 'hearing-aid-display') . '</p>';
        echo '<div class="hapd-excel-columns-info">';
        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead><tr><th>' . __('Column', 'hearing-aid-display') . '</th><th>' . __('Description', 'hearing-aid-display') . '</th><th>' . __('Required', 'hearing-aid-display') . '</th></tr></thead>';
        echo '<tbody>';
        echo '<tr><td><strong>A - No.</strong></td><td>' . __('Sequential number/ID', 'hearing-aid-display') . '</td><td>' . __('Optional', 'hearing-aid-display') . '</td></tr>';
        echo '<tr><td><strong>B - Model No.</strong></td><td>' . __('Product model number', 'hearing-aid-display') . '</td><td>' . __('Required', 'hearing-aid-display') . '</td></tr>';
        echo '<tr><td><strong>C - Photo</strong></td><td>' . __('Image filename or URL', 'hearing-aid-display') . '</td><td>' . __('Optional', 'hearing-aid-display') . '</td></tr>';
        echo '<tr><td><strong>D - Packaging</strong></td><td>' . __('Packaging information', 'hearing-aid-display') . '</td><td>' . __('Optional', 'hearing-aid-display') . '</td></tr>';
        echo '<tr><td><strong>E - Accessories</strong></td><td>' . __('Included accessories (separated by |)', 'hearing-aid-display') . '</td><td>' . __('Optional', 'hearing-aid-display') . '</td></tr>';
        echo '<tr><td><strong>F - Description</strong></td><td>' . __('Product description', 'hearing-aid-display') . '</td><td>' . __('Optional', 'hearing-aid-display') . '</td></tr>';
        echo '<tr><td><strong>G - Specification</strong></td><td>' . __('Technical specifications (Name: Value format)', 'hearing-aid-display') . '</td><td>' . __('Optional', 'hearing-aid-display') . '</td></tr>';
        echo '<tr><td><strong>H - Measurement</strong></td><td>' . __('Product dimensions/measurements', 'hearing-aid-display') . '</td><td>' . __('Optional', 'hearing-aid-display') . '</td></tr>';
        echo '<tr><td><strong>I - Price</strong></td><td>' . __('Product price (numbers only)', 'hearing-aid-display') . '</td><td>' . __('Optional', 'hearing-aid-display') . '</td></tr>';
        echo '</tbody>';
        echo '</table>';
        echo '</div>';

        echo '<div class="hapd-excel-import-form">';
        echo '<h3>' . __('Upload Excel File', 'hearing-aid-display') . '</h3>';
        echo '<p class="description">' . __('Select an Excel file (.xlsx or .xls) to import products. Maximum file size: 300MB. Images can be URLs or filenames in the uploads/hearing-aid-products/ directory.', 'hearing-aid-display') . '</p>';

        // Check and display upload limit warnings
        if (class_exists('HAPD_Excel_Importer')) {
            $importer = new HAPD_Excel_Importer();
            $warnings = $importer->check_upload_limits();
            if (!empty($warnings)) {
                echo '<div class="notice notice-warning inline">';
                echo '<p><strong>' . __('Upload Limit Warnings:', 'hearing-aid-display') . '</strong></p>';
                echo '<ul>';
                foreach ($warnings as $warning) {
                    echo '<li>' . esc_html($warning) . '</li>';
                }
                echo '</ul>';
                echo '<p><em>' . __('Contact your hosting provider or system administrator to increase these limits if needed.', 'hearing-aid-display') . '</em></p>';
                echo '</div>';
            }
        }

        echo '<p class="description"><strong>' . __('Large File Processing:', 'hearing-aid-display') . '</strong> ' . __('Files over 50MB may take several minutes to process. Please be patient and do not close the browser during import.', 'hearing-aid-display') . '</p>';
        echo '<input type="file" id="hapd-excel-file-input" accept=".xlsx,.xls" />';
        echo '<button type="button" id="hapd-excel-import-btn" class="button button-primary" disabled>' . __('Import from Excel', 'hearing-aid-display') . '</button>';
        echo '</div>';
        echo '</div>';

        // CSV Import section (legacy)
        echo '<div class="hapd-csv-import-section">';
        echo '<h2>' . __('Import from CSV (Legacy)', 'hearing-aid-display') . '</h2>';
        echo '<p>' . __('Import products from a CSV file. The file should have the following columns:', 'hearing-aid-display') . '</p>';
        echo '<ul>';
        echo '<li>' . __('title (required)', 'hearing-aid-display') . '</li>';
        echo '<li>' . __('description', 'hearing-aid-display') . '</li>';
        echo '<li>' . __('excerpt', 'hearing-aid-display') . '</li>';
        echo '<li>' . __('price', 'hearing-aid-display') . '</li>';
        echo '<li>' . __('model_number', 'hearing-aid-display') . '</li>';
        echo '<li>' . __('warranty', 'hearing-aid-display') . '</li>';
        echo '<li>' . __('availability', 'hearing-aid-display') . '</li>';
        echo '<li>' . __('features (separated by |)', 'hearing-aid-display') . '</li>';
        echo '<li>' . __('categories (separated by |)', 'hearing-aid-display') . '</li>';
        echo '<li>' . __('brands (separated by |)', 'hearing-aid-display') . '</li>';
        echo '</ul>';
        echo '<form method="post" enctype="multipart/form-data">';
        wp_nonce_field('hapd_import', 'hapd_import_nonce');
        echo '<input type="file" name="import_file" accept=".csv" required>';
        echo '<input type="submit" name="hapd_import" class="button button-primary" value="' . __('Import CSV', 'hearing-aid-display') . '">';
        echo '</form>';
        echo '</div>';

        echo '</div>';
        echo '</div>';
    }
    
    /**
     * Get dashboard statistics
     */
    private function get_dashboard_stats() {
        $product_count = wp_count_posts('hearing_aid_product');
        $category_count = wp_count_terms('hearing_aid_category');
        $brand_count = wp_count_terms('hearing_aid_brand');
        
        return array(
            array(
                'number' => $product_count->publish,
                'label' => __('Published Products', 'hearing-aid-display')
            ),
            array(
                'number' => $product_count->draft,
                'label' => __('Draft Products', 'hearing-aid-display')
            ),
            array(
                'number' => $category_count,
                'label' => __('Categories', 'hearing-aid-display')
            ),
            array(
                'number' => $brand_count,
                'label' => __('Brands', 'hearing-aid-display')
            ),
        );
    }
    
    /**
     * Display recent products
     */
    private function display_recent_products() {
        $products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => 'any',
            'numberposts' => 10,
            'orderby' => 'date',
            'order' => 'DESC',
        ));
        
        if (!empty($products)) {
            echo '<div class="hapd-products-table">';
            echo '<h2>' . __('Recent Products', 'hearing-aid-display') . '</h2>';
            echo '<table class="wp-list-table widefat fixed striped">';
            echo '<thead>';
            echo '<tr>';
            echo '<th>' . __('Product', 'hearing-aid-display') . '</th>';
            echo '<th>' . __('Price', 'hearing-aid-display') . '</th>';
            echo '<th>' . __('Status', 'hearing-aid-display') . '</th>';
            echo '<th>' . __('Date', 'hearing-aid-display') . '</th>';
            echo '<th>' . __('Actions', 'hearing-aid-display') . '</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';
            
            foreach ($products as $product) {
                $price = get_post_meta($product->ID, '_hapd_price', true);
                $status_labels = array(
                    'publish' => __('Published', 'hearing-aid-display'),
                    'draft' => __('Draft', 'hearing-aid-display'),
                    'private' => __('Private', 'hearing-aid-display'),
                    'pending' => __('Pending', 'hearing-aid-display'),
                );
                
                echo '<tr>';
                echo '<td><strong>' . esc_html($product->post_title) . '</strong></td>';
                echo '<td>' . esc_html($price) . '</td>';
                echo '<td>' . ($status_labels[$product->post_status] ?? $product->post_status) . '</td>';
                echo '<td>' . date_i18n(get_option('date_format'), strtotime($product->post_date)) . '</td>';
                echo '<td>';
                echo '<a href="' . get_edit_post_link($product->ID) . '">' . __('Edit', 'hearing-aid-display') . '</a> | ';
                echo '<a href="' . get_permalink($product->ID) . '" target="_blank">' . __('View', 'hearing-aid-display') . '</a>';
                echo '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
            echo '<p><a href="' . admin_url('edit.php?post_type=hearing_aid_product') . '">' . __('View All Products', 'hearing-aid-display') . '</a></p>';
            echo '</div>';
        }
    }
    
    /**
     * Add custom columns to product list
     */
    public function add_custom_columns($columns) {
        $new_columns = array();
        
        foreach ($columns as $key => $value) {
            $new_columns[$key] = $value;
            
            if ($key === 'title') {
                $new_columns['thumbnail'] = __('Image', 'hearing-aid-display');
                $new_columns['price'] = __('Price', 'hearing-aid-display');
                $new_columns['model'] = __('Model', 'hearing-aid-display');
                $new_columns['availability'] = __('Availability', 'hearing-aid-display');
            }
        }
        
        return $new_columns;
    }
    
    /**
     * Custom column content
     */
    public function custom_column_content($column, $post_id) {
        switch ($column) {
            case 'thumbnail':
                $thumbnail = get_the_post_thumbnail($post_id, array(50, 50));
                echo $thumbnail ?: '<span class="dashicons dashicons-format-image"></span>';
                break;
                
            case 'price':
                $price = get_post_meta($post_id, '_hapd_price', true);
                echo $price ? esc_html($price) : '—';
                break;
                
            case 'model':
                $model = get_post_meta($post_id, '_hapd_model_number', true);
                echo $model ? esc_html($model) : '—';
                break;
                
            case 'availability':
                $availability = get_post_meta($post_id, '_hapd_availability', true);
                $availability_labels = array(
                    'in_stock' => __('In Stock', 'hearing-aid-display'),
                    'out_of_stock' => __('Out of Stock', 'hearing-aid-display'),
                    'contact_for_availability' => __('Contact for Availability', 'hearing-aid-display'),
                );
                echo $availability_labels[$availability] ?? '—';
                break;
        }
    }
    
    /**
     * Make columns sortable
     */
    public function sortable_columns($columns) {
        $columns['price'] = 'price';
        $columns['model'] = 'model';
        $columns['availability'] = 'availability';
        return $columns;
    }
    
    /**
     * Custom orderby for sortable columns
     */
    public function custom_orderby($query) {
        if (!is_admin() || !$query->is_main_query()) {
            return;
        }
        
        $orderby = $query->get('orderby');
        
        if ($orderby === 'price') {
            $query->set('meta_key', '_hapd_price');
            $query->set('orderby', 'meta_value_num');
        } elseif ($orderby === 'model') {
            $query->set('meta_key', '_hapd_model_number');
            $query->set('orderby', 'meta_value');
        } elseif ($orderby === 'availability') {
            $query->set('meta_key', '_hapd_availability');
            $query->set('orderby', 'meta_value');
        }
    }
    
    /**
     * Settings callbacks
     */
    public function general_settings_callback() {
        echo '<p>' . __('Configure general plugin settings.', 'hearing-aid-display') . '</p>';
    }
    
    public function display_settings_callback() {
        echo '<p>' . __('Configure how products are displayed on your website.', 'hearing-aid-display') . '</p>';
    }
    
    public function contact_settings_callback() {
        echo '<p>' . __('Configure contact information for customer inquiries.', 'hearing-aid-display') . '</p>';
    }
    
    public function currency_symbol_callback() {
        $settings = get_option('hapd_settings', array());
        $value = $settings['currency_symbol'] ?? '$';
        echo '<input type="text" name="hapd_settings[currency_symbol]" value="' . esc_attr($value) . '" class="small-text">';
        echo '<p class="description">' . __('Symbol to display with prices.', 'hearing-aid-display') . '</p>';
    }
    
    public function currency_position_callback() {
        $settings = get_option('hapd_settings', array());
        $value = $settings['currency_position'] ?? 'before';
        echo '<select name="hapd_settings[currency_position]">';
        echo '<option value="before"' . selected($value, 'before', false) . '>' . __('Before ($100)', 'hearing-aid-display') . '</option>';
        echo '<option value="after"' . selected($value, 'after', false) . '>' . __('After (100$)', 'hearing-aid-display') . '</option>';
        echo '</select>';
    }
    
    public function default_layout_callback() {
        $settings = get_option('hapd_settings', array());
        $value = $settings['default_layout'] ?? 'grid';
        echo '<select name="hapd_settings[default_layout]">';
        echo '<option value="grid"' . selected($value, 'grid', false) . '>' . __('Grid', 'hearing-aid-display') . '</option>';
        echo '<option value="list"' . selected($value, 'list', false) . '>' . __('List', 'hearing-aid-display') . '</option>';
        echo '<option value="carousel"' . selected($value, 'carousel', false) . '>' . __('Carousel', 'hearing-aid-display') . '</option>';
        echo '</select>';
    }
    
    public function products_per_page_callback() {
        $settings = get_option('hapd_settings', array());
        $value = $settings['products_per_page'] ?? 12;
        echo '<input type="number" name="hapd_settings[products_per_page]" value="' . esc_attr($value) . '" class="small-text" min="1" max="100">';
        echo '<p class="description">' . __('Number of products to display per page.', 'hearing-aid-display') . '</p>';
    }
    
    public function enable_zoom_callback() {
        $settings = get_option('hapd_settings', array());
        $value = $settings['enable_zoom'] ?? true;
        echo '<input type="checkbox" name="hapd_settings[enable_zoom]" value="1"' . checked($value, true, false) . '>';
        echo '<label>' . __('Enable image zoom functionality', 'hearing-aid-display') . '</label>';
    }
    
    public function contact_email_callback() {
        $settings = get_option('hapd_settings', array());
        $value = $settings['contact_email'] ?? get_option('admin_email');
        echo '<input type="email" name="hapd_settings[contact_email]" value="' . esc_attr($value) . '" class="regular-text">';
        echo '<p class="description">' . __('Email address for customer inquiries.', 'hearing-aid-display') . '</p>';
    }
    
    public function contact_phone_callback() {
        $settings = get_option('hapd_settings', array());
        $value = $settings['contact_phone'] ?? '';
        echo '<input type="text" name="hapd_settings[contact_phone]" value="' . esc_attr($value) . '" class="regular-text">';
        echo '<p class="description">' . __('Phone number for customer inquiries.', 'hearing-aid-display') . '</p>';
    }
    
    /**
     * Sanitize settings
     */
    public function sanitize_settings($input) {
        $sanitized = array();
        
        if (isset($input['currency_symbol'])) {
            $sanitized['currency_symbol'] = sanitize_text_field($input['currency_symbol']);
        }
        
        if (isset($input['currency_position'])) {
            $sanitized['currency_position'] = in_array($input['currency_position'], array('before', 'after')) 
                ? $input['currency_position'] : 'before';
        }
        
        if (isset($input['default_layout'])) {
            $sanitized['default_layout'] = in_array($input['default_layout'], array('grid', 'list', 'carousel')) 
                ? $input['default_layout'] : 'grid';
        }
        
        if (isset($input['products_per_page'])) {
            $sanitized['products_per_page'] = absint($input['products_per_page']);
        }
        
        if (isset($input['enable_zoom'])) {
            $sanitized['enable_zoom'] = (bool) $input['enable_zoom'];
        }
        
        if (isset($input['contact_email'])) {
            $sanitized['contact_email'] = sanitize_email($input['contact_email']);
        }
        
        if (isset($input['contact_phone'])) {
            $sanitized['contact_phone'] = sanitize_text_field($input['contact_phone']);
        }
        
        return $sanitized;
    }
    
    /**
     * Handle export
     */
    private function handle_export() {
        if (!wp_verify_nonce($_POST['hapd_export_nonce'], 'hapd_export')) {
            wp_die(__('Security check failed', 'hearing-aid-display'));
        }
        
        $products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => 'any',
            'numberposts' => -1,
        ));
        
        $filename = 'hearing-aid-products-' . date('Y-m-d') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, array(
            'title', 'description', 'excerpt', 'price', 'model_number', 
            'warranty', 'availability', 'features', 'categories', 'brands'
        ));
        
        foreach ($products as $product) {
            $features = get_post_meta($product->ID, '_hapd_features', true);
            $categories = wp_get_post_terms($product->ID, 'hearing_aid_category', array('fields' => 'names'));
            $brands = wp_get_post_terms($product->ID, 'hearing_aid_brand', array('fields' => 'names'));
            
            fputcsv($output, array(
                $product->post_title,
                $product->post_content,
                $product->post_excerpt,
                get_post_meta($product->ID, '_hapd_price', true),
                get_post_meta($product->ID, '_hapd_model_number', true),
                get_post_meta($product->ID, '_hapd_warranty', true),
                get_post_meta($product->ID, '_hapd_availability', true),
                is_array($features) ? implode('|', $features) : '',
                is_array($categories) ? implode('|', $categories) : '',
                is_array($brands) ? implode('|', $brands) : '',
            ));
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Handle import
     */
    private function handle_import() {
        if (!wp_verify_nonce($_POST['hapd_import_nonce'], 'hapd_import')) {
            wp_die(__('Security check failed', 'hearing-aid-display'));
        }
        
        $file = $_FILES['import_file'];
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            add_settings_error('hapd_import', 'file_error', __('File upload error', 'hearing-aid-display'));
            return;
        }
        
        $handle = fopen($file['tmp_name'], 'r');
        if (!$handle) {
            add_settings_error('hapd_import', 'file_error', __('Could not read file', 'hearing-aid-display'));
            return;
        }
        
        $headers = fgetcsv($handle);
        $imported = 0;
        $errors = 0;
        
        while (($data = fgetcsv($handle)) !== FALSE) {
            $product_data = array_combine($headers, $data);
            
            if (empty($product_data['title'])) {
                $errors++;
                continue;
            }
            
            $post_id = wp_insert_post(array(
                'post_title' => sanitize_text_field($product_data['title']),
                'post_content' => wp_kses_post($product_data['description'] ?? ''),
                'post_excerpt' => sanitize_text_field($product_data['excerpt'] ?? ''),
                'post_type' => 'hearing_aid_product',
                'post_status' => 'draft',
            ));
            
            if ($post_id) {
                // Add meta data
                if (!empty($product_data['price'])) {
                    update_post_meta($post_id, '_hapd_price', sanitize_text_field($product_data['price']));
                }
                if (!empty($product_data['model_number'])) {
                    update_post_meta($post_id, '_hapd_model_number', sanitize_text_field($product_data['model_number']));
                }
                if (!empty($product_data['warranty'])) {
                    update_post_meta($post_id, '_hapd_warranty', sanitize_text_field($product_data['warranty']));
                }
                if (!empty($product_data['availability'])) {
                    update_post_meta($post_id, '_hapd_availability', sanitize_text_field($product_data['availability']));
                }
                if (!empty($product_data['features'])) {
                    $features = explode('|', $product_data['features']);
                    update_post_meta($post_id, '_hapd_features', array_map('sanitize_text_field', $features));
                }
                
                // Add taxonomies
                if (!empty($product_data['categories'])) {
                    $categories = explode('|', $product_data['categories']);
                    wp_set_post_terms($post_id, $categories, 'hearing_aid_category');
                }
                if (!empty($product_data['brands'])) {
                    $brands = explode('|', $product_data['brands']);
                    wp_set_post_terms($post_id, $brands, 'hearing_aid_brand');
                }
                
                $imported++;
            } else {
                $errors++;
            }
        }
        
        fclose($handle);
        
        add_settings_error('hapd_import', 'import_success', 
            sprintf(__('Import completed. %d products imported, %d errors.', 'hearing-aid-display'), $imported, $errors), 
            'updated'
        );
    }
}

// Initialize admin class
if (is_admin()) {
    new HAPD_Admin();
}
