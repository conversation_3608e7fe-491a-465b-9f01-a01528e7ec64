# 🔧 CSV Import Troubleshooting Guide

## 🎯 **Quick Fix Summary**

**Main Issues with CSV Import:**
1. **File format problems** - Wrong headers or file type
2. **Missing required data** - 'title' column is required
3. **File upload issues** - Size limits or permissions
4. **Products not visible** - Imported as draft status (now fixed)

**Immediate Solutions:**
1. Use the provided sample CSV file as template
2. Ensure 'title' column exists and has data
3. Check file is saved as .csv format
4. Verify WordPress upload limits

---

## 📋 **CSV File Requirements**

### **Required Format**
Your CSV file MUST have these exact column headers in the first row:

```csv
title,description,excerpt,price,model_number,warranty,availability,features,categories,brands
```

### **Column Details**

| Column | Required | Description | Example |
|--------|----------|-------------|---------|
| **title** | ✅ **YES** | Product name/title | "Advanced Digital Hearing Aid" |
| description | No | Full product description | "High-quality digital hearing aid..." |
| excerpt | No | Short summary | "Premium digital hearing aid" |
| price | No | Product price (numbers only) | 1299.99 |
| model_number | No | Product model | HA-2000 |
| warranty | No | Warranty information | "2 years" |
| availability | No | Stock status | in_stock |
| features | No | Features (separated by \|) | "Noise reduction\|Bluetooth\|Rechargeable" |
| categories | No | Categories (separated by \|) | "Digital\|Premium" |
| brands | No | Brands (separated by \|) | "TechHear\|Professional" |

---

## 🚨 **Common Issues & Solutions**

### **Issue 1: "No file uploaded" or Upload Fails**

**Symptoms:**
- Error message about file upload
- Form doesn't submit
- Page refreshes without importing

**Causes & Solutions:**
- **File too large**: Check WordPress upload limit (usually 2MB-64MB)
  - Solution: Reduce file size or increase `upload_max_filesize` in PHP
- **Wrong file type**: Must be .csv file
  - Solution: Save Excel file as CSV format
- **Browser issues**: Form submission problems
  - Solution: Try different browser or clear cache

### **Issue 2: "Could not read CSV headers"**

**Symptoms:**
- Error about reading headers
- Import fails immediately

**Causes & Solutions:**
- **Empty file**: CSV file has no content
  - Solution: Add headers and data to CSV
- **Wrong encoding**: File saved with wrong character encoding
  - Solution: Save as UTF-8 CSV
- **Corrupted file**: File damaged during transfer
  - Solution: Re-create CSV file

### **Issue 3: "CSV file must contain a 'title' column"**

**Symptoms:**
- Error about missing title column
- Import stops before processing

**Causes & Solutions:**
- **Wrong header name**: Column named "Title" instead of "title"
  - Solution: Use exact lowercase "title"
- **Missing header**: No title column in CSV
  - Solution: Add title column as first column
- **Extra spaces**: Header has spaces like " title "
  - Solution: Remove spaces around header names

### **Issue 4: "Column count mismatch"**

**Symptoms:**
- Error about column count
- Some rows fail to import

**Causes & Solutions:**
- **Inconsistent columns**: Some rows have more/fewer columns
  - Solution: Ensure all rows have same number of columns
- **Commas in data**: Data contains unescaped commas
  - Solution: Wrap data in quotes: "Product, with comma"
- **Line breaks in data**: Data contains line breaks
  - Solution: Remove line breaks or escape properly

### **Issue 5: Products Import but Not Visible**

**Symptoms:**
- Import shows success
- No products appear on frontend
- Products exist in admin but as drafts

**Causes & Solutions:**
- **Draft status**: Products imported as drafts (FIXED in latest version)
  - Solution: Products now import as "published" automatically
- **Cache issues**: Frontend cached
  - Solution: Clear cache plugins
- **Theme issues**: Theme doesn't support product display
  - Solution: Test with default theme

---

## 📝 **Step-by-Step Import Process**

### **Step 1: Prepare CSV File**
1. **Create new spreadsheet** in Excel/Google Sheets
2. **Add headers** in first row (exact names from table above)
3. **Add product data** in subsequent rows
4. **Save as CSV** (.csv format, not .xlsx)

### **Step 2: Validate CSV File**
1. **Open in text editor** to verify format
2. **Check headers** are exactly as required
3. **Verify title column** has data for all products
4. **Check for commas** in data (should be quoted)

### **Step 3: Import via WordPress**
1. **Go to WordPress Admin** → Hearing Aids → Import/Export
2. **Find "Import from CSV" section**
3. **Click "Choose File"** and select your CSV
4. **Click "Import CSV"** button
5. **Wait for completion** message

### **Step 4: Verify Import**
1. **Check import message** for success/error count
2. **Go to Hearing Aids → All Products** to see imported products
3. **Test frontend display** with `[hearing_aid_products]` shortcode
4. **Check individual products** for correct data

---

## 📊 **Sample CSV Template**

Here's a properly formatted CSV example:

```csv
title,description,excerpt,price,model_number,warranty,availability,features,categories,brands
"Advanced Digital Hearing Aid","High-quality digital hearing aid with noise reduction","Premium digital hearing aid",1299.99,HA-2000,"2 years",in_stock,"Noise reduction|Bluetooth|Rechargeable","Digital|Premium","TechHear|Professional"
"Behind-the-Ear Model","Comfortable BTE hearing aid","Reliable BTE hearing aid",899.99,BTE-Pro,"1 year",in_stock,"Adjustable volume|Water resistant","Behind-the-ear|Standard","ComfortHear|Reliable"
```

**Download Sample:** Use the CSV diagnostic tool to generate a sample file.

---

## 🔧 **Advanced Troubleshooting**

### **Check WordPress Error Logs**
1. Enable WordPress debugging in `wp-config.php`:
   ```php
   define('WP_DEBUG', true);
   define('WP_DEBUG_LOG', true);
   ```
2. Check `/wp-content/debug.log` for errors

### **Increase PHP Limits**
Add to `.htaccess` or `php.ini`:
```
upload_max_filesize = 64M
post_max_size = 64M
max_execution_time = 300
memory_limit = 256M
```

### **Test with Minimal Data**
Create a simple 2-row CSV to test:
```csv
title,description
"Test Product 1","This is a test product"
"Test Product 2","This is another test product"
```

---

## 🆚 **CSV vs Excel Import**

| Feature | CSV Import | Excel Import |
|---------|------------|--------------|
| **File Size** | Limited by PHP | Up to 300MB |
| **Progress Tracking** | Basic | Advanced with progress bar |
| **Error Handling** | Basic | Detailed with row numbers |
| **Image Support** | No | Yes (URLs and local files) |
| **Reliability** | Good for small files | Better for large files |
| **Ease of Use** | Simple | More user-friendly |

**Recommendation:** Use Excel import for better reliability and features.

---

## 📞 **Getting Help**

If CSV import still doesn't work:

1. **Use diagnostic tool**: `/csv-import-diagnostic.php`
2. **Try sample CSV**: Download and test with provided sample
3. **Check error logs**: Look for PHP errors
4. **Switch to Excel import**: More reliable alternative
5. **Contact support**: Provide specific error messages

---

## ✅ **Success Checklist**

Before importing, verify:
- [ ] CSV file has correct headers (case-sensitive)
- [ ] 'title' column exists and has data
- [ ] File saved as .csv format
- [ ] No extra commas or line breaks in data
- [ ] File size under WordPress upload limit
- [ ] WordPress plugin is active
- [ ] You have admin permissions

After importing:
- [ ] Check import success message
- [ ] Verify products in admin area
- [ ] Test frontend display
- [ ] Check product details are correct
