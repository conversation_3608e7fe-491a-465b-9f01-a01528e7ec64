<?php
/**
 * Check Specifications Data - Hearing Aid Product Display Plugin
 * 
 * Diagnostic script to check if specification data from Column G is being imported and stored correctly
 * 
 * @package HearingAidProductDisplay
 * @since 1.1.1
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // If not in WordPress, try to load WordPress
    $wp_load_paths = array(
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php',
    );
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress not found. Please run this from WordPress admin or ensure wp-load.php is accessible.');
    }
}

// Only allow admin users to run this check
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Specifications Data Check - Hearing Aid Display</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #0073aa; color: white; padding: 20px; margin: -20px -20px 20px -20px; border-radius: 8px 8px 0 0; }
        .status { padding: 5px 10px; border-radius: 3px; color: white; font-weight: bold; }
        .success { background: #46b450; }
        .warning { background: #ffb900; }
        .error { background: #dc3232; }
        .info { background: #00a0d2; }
        .product-card { border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin: 15px 0; background: #f9f9f9; }
        .product-title { font-weight: bold; margin-bottom: 10px; color: #0073aa; }
        .meta-data { background: #f0f0f1; padding: 10px; border-radius: 3px; margin: 5px 0; }
        .meta-label { font-weight: bold; color: #666; }
        .meta-value { margin-left: 10px; font-family: monospace; background: white; padding: 5px; border-radius: 2px; }
        .spec-item { background: #e8f4f8; padding: 8px; margin: 3px 0; border-radius: 3px; }
        .fix-button { background: #46b450; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px 5px 5px 0; }
        .fix-button:hover { background: #3e9b47; color: white; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .code-block { background: #f4f4f4; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
        .template-issue { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Specifications Data Check</h1>
            <p>Diagnosing Column G (Specification) data import and display issues</p>
        </div>

        <?php
        // Get all hearing aid products
        $products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => array('publish', 'draft', 'private'),
            'numberposts' => 10, // Limit to first 10 for detailed analysis
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        $total_products = count($products);
        $products_with_hapd_specs = 0;
        $products_with_theme_specs = 0;
        $products_with_no_specs = 0;
        $products_with_both_specs = 0;

        echo "<div class='section'>";
        echo "<h2>📊 Specification Data Analysis</h2>";
        
        if (empty($products)) {
            echo "<p><span class='status error'>NO PRODUCTS FOUND</span> No hearing aid products found in database.</p>";
            echo "<p><strong>Solution:</strong> Import your Excel file first using the Import/Export feature.</p>";
            echo "</div>";
            echo "</div></body></html>";
            exit;
        }

        echo "<p>Analyzing <strong>$total_products</strong> most recent hearing aid products...</p>";

        foreach ($products as $product) {
            $product_id = $product->ID;
            
            // Check for plugin specifications (_hapd_specifications)
            $hapd_specs = get_post_meta($product_id, '_hapd_specifications', true);
            
            // Check for theme specifications (_product_specifications)
            $theme_specs = get_post_meta($product_id, '_product_specifications', true);
            
            // Count different scenarios
            if (!empty($hapd_specs) && !empty($theme_specs)) {
                $products_with_both_specs++;
            } elseif (!empty($hapd_specs)) {
                $products_with_hapd_specs++;
            } elseif (!empty($theme_specs)) {
                $products_with_theme_specs++;
            } else {
                $products_with_no_specs++;
            }
        }

        // Display summary
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
        
        echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; text-align: center;'>";
        echo "<div style='font-size: 24px; font-weight: bold; color: #0073aa;'>$products_with_hapd_specs</div>";
        echo "<div style='font-size: 12px; color: #666; margin-top: 5px;'>Plugin Specs Only</div>";
        echo "</div>";
        
        echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; text-align: center;'>";
        echo "<div style='font-size: 24px; font-weight: bold; color: #0073aa;'>$products_with_theme_specs</div>";
        echo "<div style='font-size: 12px; color: #666; margin-top: 5px;'>Theme Specs Only</div>";
        echo "</div>";
        
        echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; text-align: center;'>";
        echo "<div style='font-size: 24px; font-weight: bold; color: #0073aa;'>$products_with_both_specs</div>";
        echo "<div style='font-size: 12px; color: #666; margin-top: 5px;'>Both Specs</div>";
        echo "</div>";
        
        echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; text-align: center;'>";
        echo "<div style='font-size: 24px; font-weight: bold; color: #dc3232;'>$products_with_no_specs</div>";
        echo "<div style='font-size: 12px; color: #666; margin-top: 5px;'>No Specs</div>";
        echo "</div>";
        
        echo "</div>";
        echo "</div>";

        // Issue identification
        echo "<div class='section'>";
        echo "<h2>🚨 Issue Identification</h2>";
        
        if ($products_with_hapd_specs > 0 && $products_with_theme_specs == 0) {
            echo "<div class='template-issue'>";
            echo "<h3>⚠️ TEMPLATE MISMATCH DETECTED</h3>";
            echo "<p><strong>Problem:</strong> Your Excel import is working correctly and storing specifications in <code>_hapd_specifications</code>, but your theme template is looking for <code>_product_specifications</code>.</p>";
            echo "<p><strong>Root Cause:</strong> The theme's <code>single-product.php</code> template uses different meta field names than the plugin.</p>";
            echo "<p><strong>Solution:</strong> We need to either:</p>";
            echo "<ul>";
            echo "<li>1. Add a content filter to display plugin specifications on single product pages</li>";
            echo "<li>2. Copy plugin specifications to theme-compatible meta fields</li>";
            echo "<li>3. Modify the theme template to use plugin meta fields</li>";
            echo "</ul>";
            echo "</div>";
        } elseif ($products_with_no_specs > 0) {
            echo "<div class='template-issue'>";
            echo "<h3>❌ IMPORT ISSUE DETECTED</h3>";
            echo "<p><strong>Problem:</strong> Specifications from Column G are not being imported or stored in the database.</p>";
            echo "<p><strong>Solution:</strong> Check the Excel import process and ensure Column G has data.</p>";
            echo "</div>";
        } elseif ($products_with_theme_specs > 0) {
            echo "<div class='template-issue'>";
            echo "<h3>✅ THEME COMPATIBILITY</h3>";
            echo "<p><strong>Status:</strong> Specifications are stored in theme-compatible format and should display correctly.</p>";
            echo "</div>";
        }
        echo "</div>";

        // Detailed product analysis
        echo "<div class='section'>";
        echo "<h2>📦 Detailed Product Analysis</h2>";
        
        foreach (array_slice($products, 0, 5) as $product) {
            $product_id = $product->ID;
            $model_no = get_post_meta($product_id, '_hapd_model_number', true);
            
            // Get all specification-related meta
            $hapd_specs = get_post_meta($product_id, '_hapd_specifications', true);
            $theme_specs = get_post_meta($product_id, '_product_specifications', true);
            $excel_row = get_post_meta($product_id, '_hapd_excel_row_number', true);
            
            echo "<div class='product-card'>";
            echo "<div class='product-title'>" . esc_html($product->post_title) . "</div>";
            
            if ($model_no) {
                echo "<p><strong>Model:</strong> " . esc_html($model_no) . "</p>";
            }
            
            if ($excel_row) {
                echo "<p><strong>Excel Row:</strong> " . esc_html($excel_row) . "</p>";
            }
            
            echo "<div class='meta-data'>";
            echo "<div class='meta-label'>Plugin Specifications (_hapd_specifications):</div>";
            if (!empty($hapd_specs)) {
                echo "<div class='meta-value'>";
                if (is_array($hapd_specs)) {
                    foreach ($hapd_specs as $spec) {
                        echo "<div class='spec-item'>";
                        echo "<strong>" . esc_html($spec['name']) . ":</strong> " . esc_html($spec['value']);
                        echo "</div>";
                    }
                } else {
                    echo esc_html($hapd_specs);
                }
                echo "</div>";
            } else {
                echo "<div class='meta-value'><em>No data</em></div>";
            }
            echo "</div>";
            
            echo "<div class='meta-data'>";
            echo "<div class='meta-label'>Theme Specifications (_product_specifications):</div>";
            if (!empty($theme_specs)) {
                echo "<div class='meta-value'>" . esc_html($theme_specs) . "</div>";
            } else {
                echo "<div class='meta-value'><em>No data</em></div>";
            }
            echo "</div>";
            
            // Show raw meta for debugging
            $all_meta = get_post_meta($product_id);
            $spec_related_meta = array();
            foreach ($all_meta as $key => $value) {
                if (strpos($key, 'spec') !== false || strpos($key, 'hapd') !== false) {
                    $spec_related_meta[$key] = $value[0];
                }
            }
            
            if (!empty($spec_related_meta)) {
                echo "<details style='margin-top: 10px;'>";
                echo "<summary style='cursor: pointer; color: #0073aa;'>🔍 View All Related Meta Fields</summary>";
                echo "<div class='code-block'>";
                foreach ($spec_related_meta as $key => $value) {
                    echo "<strong>" . esc_html($key) . ":</strong> ";
                    if (is_array($value) || is_object($value)) {
                        echo "<pre>" . esc_html(print_r($value, true)) . "</pre>";
                    } else {
                        echo esc_html($value) . "<br>";
                    }
                }
                echo "</div>";
                echo "</details>";
            }
            
            echo "</div>";
        }
        echo "</div>";

        // Solution section
        echo "<div class='section'>";
        echo "<h2>🛠️ Recommended Solutions</h2>";
        
        if ($products_with_hapd_specs > 0) {
            echo "<h3>✅ Data Import is Working</h3>";
            echo "<p>Your Excel import is correctly processing Column G and storing specifications in the database.</p>";
            
            echo "<h3>🔧 Display Fix Needed</h3>";
            echo "<p>The issue is that your theme template doesn't know how to display the plugin's specification data.</p>";
            
            echo "<p><strong>Available Solutions:</strong></p>";
            echo "<p>";
            echo "<a href='#' onclick='copySpecsToTheme()' class='fix-button'>🔄 Copy Plugin Specs to Theme Format</a>";
            echo "<a href='#' onclick='addContentFilter()' class='fix-button'>🎨 Add Content Filter</a>";
            echo "<a href='#' onclick='showTemplateCode()' class='fix-button'>📝 Show Template Code</a>";
            echo "</p>";
            
            echo "<div id='solution-details' style='margin-top: 20px;'></div>";
            
        } elseif ($products_with_no_specs > 0) {
            echo "<h3>❌ Import Issue</h3>";
            echo "<p>Specifications are not being imported from Column G. This could be due to:</p>";
            echo "<ul>";
            echo "<li>Empty Column G in your Excel file</li>";
            echo "<li>Excel file format issues</li>";
            echo "<li>Import process errors</li>";
            echo "</ul>";
            
            echo "<p><strong>Recommended Actions:</strong></p>";
            echo "<p>";
            echo "<a href='diagnose-imported-products.php' class='fix-button'>🔍 Run Full Diagnostic</a>";
            echo "<a href='" . admin_url('admin.php?page=hearing-aid-import-export') . "' class='fix-button'>📊 Re-import Excel File</a>";
            echo "</p>";
        }
        
        echo "</div>";
        ?>

        <script>
        function copySpecsToTheme() {
            document.getElementById('solution-details').innerHTML = `
                <div class="template-issue">
                    <h4>🔄 Copy Plugin Specs to Theme Format</h4>
                    <p>This solution will copy all plugin specifications to theme-compatible meta fields.</p>
                    <p><strong>Action:</strong> <a href="fix-specifications-display.php" class="fix-button">Run Specification Fix Script</a></p>
                </div>
            `;
        }
        
        function addContentFilter() {
            document.getElementById('solution-details').innerHTML = `
                <div class="template-issue">
                    <h4>🎨 Add Content Filter</h4>
                    <p>This solution adds a WordPress filter to automatically display specifications on single product pages.</p>
                    <div class="code-block">
// Add this to your theme's functions.php or plugin
add_filter('the_content', 'display_hapd_specifications');
function display_hapd_specifications($content) {
    if (is_singular('hearing_aid_product')) {
        $specs = get_post_meta(get_the_ID(), '_hapd_specifications', true);
        if (!empty($specs)) {
            $specs_html = '<div class="hapd-specifications"><h3>Technical Specifications</h3><table>';
            foreach ($specs as $spec) {
                $specs_html .= '<tr><td><strong>' . esc_html($spec['name']) . '</strong></td>';
                $specs_html .= '<td>' . esc_html($spec['value']) . '</td></tr>';
            }
            $specs_html .= '</table></div>';
            $content .= $specs_html;
        }
    }
    return $content;
}
                    </div>
                </div>
            `;
        }
        
        function showTemplateCode() {
            document.getElementById('solution-details').innerHTML = `
                <div class="template-issue">
                    <h4>📝 Template Modification</h4>
                    <p>Modify your theme's single-product.php template to display plugin specifications.</p>
                    <div class="code-block">
// Replace this line in single-product.php:
$product_specifications = get_post_meta(get_the_ID(), '_product_specifications', true);

// With this:
$product_specifications = get_post_meta(get_the_ID(), '_hapd_specifications', true);

// And update the display code to handle the array format:
if (!empty($product_specifications)) {
    echo '<div class="product-specifications">';
    echo '<h3>Technical Specifications</h3>';
    echo '<table class="specs-table">';
    foreach ($product_specifications as $spec) {
        echo '<tr>';
        echo '<td><strong>' . esc_html($spec['name']) . '</strong></td>';
        echo '<td>' . esc_html($spec['value']) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    echo '</div>';
}
                    </div>
                </div>
            `;
        }
        </script>
    </div>
</body>
</html>
