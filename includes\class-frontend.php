<?php
/**
 * Frontend Display Class
 *
 * @package HearingAidProductDisplay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Frontend Display Class
 */
class HAPD_Frontend {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_assets'));
        add_shortcode('hearing_aid_products', array($this, 'products_shortcode'));

        // Add content filter to display specifications on single product pages
        add_filter('the_content', array($this, 'add_specifications_to_content'), 20);

        // Add template redirect for single product pages
        add_action('template_redirect', array($this, 'single_product_template_redirect'));
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_assets() {
        // Only enqueue on pages that need it
        if ($this->should_enqueue_assets()) {
            wp_enqueue_style(
                'hapd-frontend',
                HAPD_PLUGIN_URL . 'assets/css/frontend.css',
                array(),
                HAPD_VERSION
            );
            
            wp_enqueue_script(
                'hapd-frontend',
                HAPD_PLUGIN_URL . 'assets/js/frontend.js',
                array('jquery'),
                HAPD_VERSION,
                true
            );
            
            // Enqueue zoom library if enabled
            $settings = get_option('hapd_settings', array());
            if ($settings['enable_zoom'] ?? true) {
                wp_enqueue_script(
                    'hapd-zoom',
                    HAPD_PLUGIN_URL . 'assets/js/zoom.js',
                    array('jquery'),
                    HAPD_VERSION,
                    true
                );
            }
        }
    }
    
    /**
     * Check if assets should be enqueued
     */
    private function should_enqueue_assets() {
        global $post;
        
        // Check if current post/page contains the block or shortcode
        if (is_a($post, 'WP_Post')) {
            if (has_block('hapd/hearing-aid-products', $post) || 
                has_shortcode($post->post_content, 'hearing_aid_products')) {
                return true;
            }
        }
        
        // Check if it's a hearing aid product page
        if (is_singular('hearing_aid_product') || is_post_type_archive('hearing_aid_product')) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Render products block
     */
    public function render_products_block($attributes) {
        $defaults = array(
            'selectedProducts' => array(),
            'layout' => 'grid',
            'columns' => 3,
            'showPrice' => true,
            'showSpecs' => true,
            'showFeatures' => true,
            'showExcerpt' => true,
        );
        
        $attributes = wp_parse_args($attributes, $defaults);
        
        // Get products
        $products = $this->get_products($attributes);
        
        if (empty($products)) {
            return '<div class="hapd-no-products">' . __('No hearing aid products found.', 'hearing-aid-display') . '</div>';
        }
        
        // Generate output
        $output = '<div class="hapd-products-container hapd-layout-' . esc_attr($attributes['layout']) . '">';
        
        if ($attributes['layout'] === 'grid') {
            $output .= $this->render_grid_layout($products, $attributes);
        } elseif ($attributes['layout'] === 'list') {
            $output .= $this->render_list_layout($products, $attributes);
        } elseif ($attributes['layout'] === 'carousel') {
            $output .= $this->render_carousel_layout($products, $attributes);
        }
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Get products based on attributes
     */
    private function get_products($attributes) {
        $args = array(
            'post_type' => 'hearing_aid_product',
            'post_status' => 'publish',
            'posts_per_page' => -1,
        );
        
        // If specific products are selected
        if (!empty($attributes['selectedProducts'])) {
            $args['post__in'] = $attributes['selectedProducts'];
            $args['orderby'] = 'post__in';
        }
        
        $query = new WP_Query($args);
        return $query->posts;
    }
    
    /**
     * Render grid layout
     */
    private function render_grid_layout($products, $attributes) {
        $columns = max(1, min(6, intval($attributes['columns'])));
        $output = '<div class="hapd-products-grid hapd-columns-' . $columns . '">';
        
        foreach ($products as $product) {
            $output .= $this->render_product_card($product, $attributes);
        }
        
        $output .= '</div>';
        return $output;
    }
    
    /**
     * Render list layout
     */
    private function render_list_layout($products, $attributes) {
        $output = '<div class="hapd-products-list">';
        
        foreach ($products as $product) {
            $output .= $this->render_product_list_item($product, $attributes);
        }
        
        $output .= '</div>';
        return $output;
    }
    
    /**
     * Render carousel layout
     */
    private function render_carousel_layout($products, $attributes) {
        $output = '<div class="hapd-products-carousel">';
        $output .= '<div class="hapd-carousel-container">';
        $output .= '<div class="hapd-carousel-track">';
        
        foreach ($products as $product) {
            $output .= '<div class="hapd-carousel-slide">';
            $output .= $this->render_product_card($product, $attributes);
            $output .= '</div>';
        }
        
        $output .= '</div>';
        $output .= '<button class="hapd-carousel-prev" aria-label="' . __('Previous', 'hearing-aid-display') . '">‹</button>';
        $output .= '<button class="hapd-carousel-next" aria-label="' . __('Next', 'hearing-aid-display') . '">›</button>';
        $output .= '</div>';
        $output .= '<div class="hapd-carousel-dots"></div>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Render product card
     */
    private function render_product_card($product, $attributes) {
        $product_id = $product->ID;
        $title = get_the_title($product_id);
        $excerpt = get_the_excerpt($product_id);
        $thumbnail = get_the_post_thumbnail($product_id, 'hapd-product-medium');
        $price = get_post_meta($product_id, '_hapd_price', true);
        $features = get_post_meta($product_id, '_hapd_features', true);
        $specifications = get_post_meta($product_id, '_hapd_specifications', true);
        $availability = get_post_meta($product_id, '_hapd_availability', true);
        
        $output = '<div class="hapd-product-card" data-product-id="' . $product_id . '">';
        
        // Product image
        if ($thumbnail) {
            $output .= '<div class="hapd-product-image">';
            $output .= '<a href="' . get_permalink($product_id) . '">';
            $output .= $thumbnail;
            $output .= '</a>';
            
            // Availability badge
            if ($availability) {
                $availability_class = 'hapd-availability-' . str_replace('_', '-', $availability);
                $availability_text = $this->get_availability_text($availability);
                $output .= '<span class="hapd-availability-badge ' . $availability_class . '">';
                $output .= $availability_text;
                $output .= '</span>';
            }
            
            $output .= '</div>';
        }
        
        // Product content
        $output .= '<div class="hapd-product-content">';
        
        // Title
        $output .= '<h3 class="hapd-product-title">';
        $output .= '<a href="' . get_permalink($product_id) . '">' . esc_html($title) . '</a>';
        $output .= '</h3>';
        
        // Price
        if ($attributes['showPrice'] && $price) {
            $output .= '<div class="hapd-product-price">';
            $output .= $this->format_price($price);
            $output .= '</div>';
        }
        
        // Excerpt
        if ($attributes['showExcerpt'] && $excerpt) {
            $output .= '<div class="hapd-product-excerpt">';
            $output .= '<p>' . esc_html($excerpt) . '</p>';
            $output .= '</div>';
        }
        
        // Features
        if ($attributes['showFeatures'] && !empty($features)) {
            $output .= '<div class="hapd-product-features">';
            $output .= '<h4>' . __('Key Features:', 'hearing-aid-display') . '</h4>';
            $output .= '<ul>';
            foreach (array_slice($features, 0, 3) as $feature) {
                $output .= '<li>' . esc_html($feature) . '</li>';
            }
            $output .= '</ul>';
            $output .= '</div>';
        }
        
        // Specifications (limited)
        if ($attributes['showSpecs'] && !empty($specifications)) {
            $output .= '<div class="hapd-product-specs-preview">';
            $output .= '<h4>' . __('Specifications:', 'hearing-aid-display') . '</h4>';
            $output .= '<ul>';
            foreach (array_slice($specifications, 0, 2) as $spec) {
                $output .= '<li><strong>' . esc_html($spec['name']) . ':</strong> ' . esc_html($spec['value']) . '</li>';
            }
            $output .= '</ul>';
            $output .= '</div>';
        }
        
        // Actions
        $output .= '<div class="hapd-product-actions">';
        $output .= '<a href="' . get_permalink($product_id) . '" class="hapd-btn hapd-btn-primary">';
        $output .= __('Learn More', 'hearing-aid-display');
        $output .= '</a>';
        $output .= $this->get_contact_button($product_id);
        $output .= '</div>';
        
        $output .= '</div>'; // .hapd-product-content
        $output .= '</div>'; // .hapd-product-card
        
        return $output;
    }
    
    /**
     * Render product list item
     */
    private function render_product_list_item($product, $attributes) {
        $product_id = $product->ID;
        $title = get_the_title($product_id);
        $excerpt = get_the_excerpt($product_id);
        $thumbnail = get_the_post_thumbnail($product_id, 'hapd-product-thumbnail');
        $price = get_post_meta($product_id, '_hapd_price', true);
        $features = get_post_meta($product_id, '_hapd_features', true);
        $specifications = get_post_meta($product_id, '_hapd_specifications', true);
        
        $output = '<div class="hapd-product-list-item" data-product-id="' . $product_id . '">';
        
        // Product image
        if ($thumbnail) {
            $output .= '<div class="hapd-product-image">';
            $output .= '<a href="' . get_permalink($product_id) . '">';
            $output .= $thumbnail;
            $output .= '</a>';
            $output .= '</div>';
        }
        
        // Product content
        $output .= '<div class="hapd-product-content">';
        
        // Header with title and price
        $output .= '<div class="hapd-product-header">';
        $output .= '<h3 class="hapd-product-title">';
        $output .= '<a href="' . get_permalink($product_id) . '">' . esc_html($title) . '</a>';
        $output .= '</h3>';
        
        if ($attributes['showPrice'] && $price) {
            $output .= '<div class="hapd-product-price">';
            $output .= $this->format_price($price);
            $output .= '</div>';
        }
        $output .= '</div>';
        
        // Excerpt
        if ($attributes['showExcerpt'] && $excerpt) {
            $output .= '<div class="hapd-product-excerpt">';
            $output .= '<p>' . esc_html($excerpt) . '</p>';
            $output .= '</div>';
        }
        
        // Features and specs in columns
        $output .= '<div class="hapd-product-details">';
        
        if ($attributes['showFeatures'] && !empty($features)) {
            $output .= '<div class="hapd-product-features">';
            $output .= '<h4>' . __('Key Features:', 'hearing-aid-display') . '</h4>';
            $output .= '<ul>';
            foreach ($features as $feature) {
                $output .= '<li>' . esc_html($feature) . '</li>';
            }
            $output .= '</ul>';
            $output .= '</div>';
        }
        
        if ($attributes['showSpecs'] && !empty($specifications)) {
            $output .= '<div class="hapd-product-specs">';
            $output .= '<h4>' . __('Specifications:', 'hearing-aid-display') . '</h4>';
            $output .= '<ul>';
            foreach ($specifications as $spec) {
                $output .= '<li><strong>' . esc_html($spec['name']) . ':</strong> ' . esc_html($spec['value']) . '</li>';
            }
            $output .= '</ul>';
            $output .= '</div>';
        }
        
        $output .= '</div>';
        
        // Actions
        $output .= '<div class="hapd-product-actions">';
        $output .= '<a href="' . get_permalink($product_id) . '" class="hapd-btn hapd-btn-primary">';
        $output .= __('Learn More', 'hearing-aid-display');
        $output .= '</a>';
        $output .= $this->get_contact_button($product_id);
        $output .= '</div>';
        
        $output .= '</div>'; // .hapd-product-content
        $output .= '</div>'; // .hapd-product-list-item
        
        return $output;
    }
    
    /**
     * Format price
     */
    private function format_price($price) {
        $settings = get_option('hapd_settings', array());
        $currency_symbol = $settings['currency_symbol'] ?? '$';
        $currency_position = $settings['currency_position'] ?? 'before';
        
        if ($currency_position === 'before') {
            return '<span class="hapd-price">' . $currency_symbol . $price . '</span>';
        } else {
            return '<span class="hapd-price">' . $price . $currency_symbol . '</span>';
        }
    }
    
    /**
     * Get availability text
     */
    private function get_availability_text($availability) {
        switch ($availability) {
            case 'in_stock':
                return __('In Stock', 'hearing-aid-display');
            case 'out_of_stock':
                return __('Out of Stock', 'hearing-aid-display');
            case 'contact_for_availability':
                return __('Contact for Availability', 'hearing-aid-display');
            default:
                return '';
        }
    }
    
    /**
     * Get contact button
     */
    private function get_contact_button($product_id) {
        $settings = get_option('hapd_settings', array());
        $contact_email = $settings['contact_email'] ?? get_option('admin_email');
        $contact_phone = $settings['contact_phone'] ?? '';
        
        $output = '<div class="hapd-contact-buttons">';
        
        if ($contact_email) {
            $subject = sprintf(__('Inquiry about %s', 'hearing-aid-display'), get_the_title($product_id));
            $mailto = 'mailto:' . $contact_email . '?subject=' . urlencode($subject);
            $output .= '<a href="' . $mailto . '" class="hapd-btn hapd-btn-secondary hapd-contact-email">';
            $output .= __('Email Us', 'hearing-aid-display');
            $output .= '</a>';
        }
        
        if ($contact_phone) {
            $output .= '<a href="tel:' . $contact_phone . '" class="hapd-btn hapd-btn-secondary hapd-contact-phone">';
            $output .= __('Call Us', 'hearing-aid-display');
            $output .= '</a>';
        }
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Products shortcode
     */
    public function products_shortcode($atts) {
        $attributes = shortcode_atts(array(
            'ids' => '',
            'category' => '',
            'brand' => '',
            'layout' => 'grid',
            'columns' => 3,
            'show_price' => 'true',
            'show_specs' => 'true',
            'show_features' => 'true',
            'show_excerpt' => 'true',
        ), $atts);
        
        // Convert string values to appropriate types
        $attributes['selectedProducts'] = !empty($attributes['ids']) ? 
            array_map('intval', explode(',', $attributes['ids'])) : array();
        $attributes['columns'] = intval($attributes['columns']);
        $attributes['showPrice'] = $attributes['show_price'] === 'true';
        $attributes['showSpecs'] = $attributes['show_specs'] === 'true';
        $attributes['showFeatures'] = $attributes['show_features'] === 'true';
        $attributes['showExcerpt'] = $attributes['show_excerpt'] === 'true';
        
        // Handle category and brand filtering
        if (!empty($attributes['category']) || !empty($attributes['brand'])) {
            $args = array(
                'post_type' => 'hearing_aid_product',
                'post_status' => 'publish',
                'posts_per_page' => -1,
            );
            
            $tax_query = array();
            
            if (!empty($attributes['category'])) {
                $tax_query[] = array(
                    'taxonomy' => 'hearing_aid_category',
                    'field' => 'slug',
                    'terms' => explode(',', $attributes['category']),
                );
            }
            
            if (!empty($attributes['brand'])) {
                $tax_query[] = array(
                    'taxonomy' => 'hearing_aid_brand',
                    'field' => 'slug',
                    'terms' => explode(',', $attributes['brand']),
                );
            }
            
            if (!empty($tax_query)) {
                $args['tax_query'] = $tax_query;
            }
            
            $query = new WP_Query($args);
            $attributes['selectedProducts'] = wp_list_pluck($query->posts, 'ID');
        }
        
        return $this->render_products_block($attributes);
    }
    
    /**
     * Render single product page
     */
    public function render_single_product($product_id) {
        $product = get_post($product_id);
        if (!$product || $product->post_type !== 'hearing_aid_product') {
            return '';
        }
        
        $title = get_the_title($product_id);
        $content = apply_filters('the_content', $product->post_content);
        $price = get_post_meta($product_id, '_hapd_price', true);
        $model_number = get_post_meta($product_id, '_hapd_model_number', true);
        $warranty = get_post_meta($product_id, '_hapd_warranty', true);
        $availability = get_post_meta($product_id, '_hapd_availability', true);
        $features = get_post_meta($product_id, '_hapd_features', true);
        $specifications = get_post_meta($product_id, '_hapd_specifications', true);
        $gallery_ids = get_post_meta($product_id, '_hapd_gallery_ids', true);
        
        $output = '<div class="hapd-single-product" data-product-id="' . $product_id . '">';
        
        // Product gallery
        $output .= '<div class="hapd-product-gallery">';
        if (!empty($gallery_ids)) {
            $output .= $this->render_product_gallery($gallery_ids);
        } else {
            $thumbnail = get_the_post_thumbnail($product_id, 'hapd-product-large');
            if ($thumbnail) {
                $output .= '<div class="hapd-single-image">' . $thumbnail . '</div>';
            }
        }
        $output .= '</div>';
        
        // Product info
        $output .= '<div class="hapd-product-info">';
        
        // Title and price
        $output .= '<div class="hapd-product-header">';
        $output .= '<h1 class="hapd-product-title">' . esc_html($title) . '</h1>';
        if ($price) {
            $output .= '<div class="hapd-product-price">' . $this->format_price($price) . '</div>';
        }
        $output .= '</div>';
        
        // Product details
        if ($model_number || $warranty || $availability) {
            $output .= '<div class="hapd-product-details">';
            if ($model_number) {
                $output .= '<p><strong>' . __('Model:', 'hearing-aid-display') . '</strong> ' . esc_html($model_number) . '</p>';
            }
            if ($warranty) {
                $output .= '<p><strong>' . __('Warranty:', 'hearing-aid-display') . '</strong> ' . esc_html($warranty) . '</p>';
            }
            if ($availability) {
                $availability_text = $this->get_availability_text($availability);
                $availability_class = 'hapd-availability-' . str_replace('_', '-', $availability);
                $output .= '<p class="' . $availability_class . '"><strong>' . __('Availability:', 'hearing-aid-display') . '</strong> ' . $availability_text . '</p>';
            }
            $output .= '</div>';
        }
        
        // Content
        if ($content) {
            $output .= '<div class="hapd-product-description">' . $content . '</div>';
        }
        
        // Features
        if (!empty($features)) {
            $output .= '<div class="hapd-product-features">';
            $output .= '<h3>' . __('Key Features', 'hearing-aid-display') . '</h3>';
            $output .= '<ul>';
            foreach ($features as $feature) {
                $output .= '<li>' . esc_html($feature) . '</li>';
            }
            $output .= '</ul>';
            $output .= '</div>';
        }
        
        // Specifications
        if (!empty($specifications)) {
            $output .= '<div class="hapd-product-specifications">';
            $output .= '<h3>' . __('Technical Specifications', 'hearing-aid-display') . '</h3>';
            $output .= '<table class="hapd-specs-table">';
            foreach ($specifications as $spec) {
                $output .= '<tr>';
                $output .= '<td><strong>' . esc_html($spec['name']) . '</strong></td>';
                $output .= '<td>' . esc_html($spec['value']) . '</td>';
                $output .= '</tr>';
            }
            $output .= '</table>';
            $output .= '</div>';
        }
        
        // Contact section
        $output .= '<div class="hapd-contact-section">';
        $output .= '<h3>' . __('Interested in this product?', 'hearing-aid-display') . '</h3>';
        $output .= '<p>' . __('Contact us for more information, pricing, or to schedule a consultation.', 'hearing-aid-display') . '</p>';
        $output .= $this->get_contact_button($product_id);
        $output .= '</div>';
        
        $output .= '</div>'; // .hapd-product-info
        $output .= '</div>'; // .hapd-single-product
        
        return $output;
    }
    
    /**
     * Render product gallery
     */
    private function render_product_gallery($gallery_ids) {
        if (empty($gallery_ids)) {
            return '';
        }
        
        $output = '<div class="hapd-gallery-container">';
        
        // Main image
        $main_image_id = $gallery_ids[0];
        $main_image = wp_get_attachment_image($main_image_id, 'hapd-product-large', false, array(
            'class' => 'hapd-main-image',
            'data-zoom-image' => wp_get_attachment_image_url($main_image_id, 'full')
        ));
        
        $output .= '<div class="hapd-main-gallery">' . $main_image . '</div>';
        
        // Thumbnails
        if (count($gallery_ids) > 1) {
            $output .= '<div class="hapd-gallery-thumbnails">';
            foreach ($gallery_ids as $index => $attachment_id) {
                $thumbnail = wp_get_attachment_image($attachment_id, 'hapd-product-thumbnail', false, array(
                    'class' => 'hapd-gallery-thumb' . ($index === 0 ? ' active' : ''),
                    'data-large-image' => wp_get_attachment_image_url($attachment_id, 'hapd-product-large'),
                    'data-zoom-image' => wp_get_attachment_image_url($attachment_id, 'full')
                ));
                $output .= '<div class="hapd-thumb-container">' . $thumbnail . '</div>';
            }
            $output .= '</div>';
        }
        
        $output .= '</div>';
        
        return $output;
    }

    /**
     * Add specifications to single product content
     */
    public function add_specifications_to_content($content) {
        // Only add to single hearing aid product pages
        if (!is_singular('hearing_aid_product')) {
            return $content;
        }

        $specifications = get_post_meta(get_the_ID(), '_hapd_specifications', true);

        if (empty($specifications) || !is_array($specifications)) {
            return $content;
        }

        // Build specifications HTML
        $specs_html = '<div class="hapd-specifications-section">';
        $specs_html .= '<h3>' . __('Technical Specifications', 'hearing-aid-display') . '</h3>';
        $specs_html .= '<table class="hapd-specs-table">';

        foreach ($specifications as $spec) {
            if (isset($spec['name']) && isset($spec['value'])) {
                $specs_html .= '<tr>';
                $specs_html .= '<td class="spec-name"><strong>' . esc_html($spec['name']) . '</strong></td>';
                $specs_html .= '<td class="spec-value">' . esc_html($spec['value']) . '</td>';
                $specs_html .= '</tr>';
            }
        }

        $specs_html .= '</table>';
        $specs_html .= '</div>';

        // Add specifications after the main content
        $content .= $specs_html;

        return $content;
    }

    /**
     * Handle single product template redirect
     */
    public function single_product_template_redirect() {
        if (is_singular('hearing_aid_product')) {
            // Add CSS for specifications display
            add_action('wp_head', array($this, 'add_specifications_css'));

            // Ensure specifications are displayed even if theme doesn't support them
            add_action('wp_footer', array($this, 'ensure_specifications_display'));
        }
    }

    /**
     * Add CSS for specifications display
     */
    public function add_specifications_css() {
        echo '<style>
        .hapd-specifications-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .hapd-specifications-section h3 {
            color: #0073aa;
            margin-bottom: 15px;
            font-size: 1.2em;
            border-bottom: 2px solid #0073aa;
            padding-bottom: 10px;
        }
        .hapd-specs-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .hapd-specs-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }
        .hapd-specs-table .spec-name {
            font-weight: bold;
            width: 30%;
            color: #333;
        }
        .hapd-specs-table .spec-value {
            color: #666;
        }
        .hapd-specs-table tr:hover {
            background-color: #f5f5f5;
        }
        .hapd-specs-table tr:last-child td {
            border-bottom: none;
        }
        </style>';
    }

    /**
     * Ensure specifications display (fallback)
     */
    public function ensure_specifications_display() {
        // This is a fallback in case the content filter doesn't work
        // Could be used to inject specifications via JavaScript if needed
    }
}
