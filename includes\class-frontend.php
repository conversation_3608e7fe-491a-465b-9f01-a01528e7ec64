<?php
/**
 * Frontend Display Class
 *
 * @package HearingAidProductDisplay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Frontend Display Class
 */
class HAPD_Frontend {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_assets'));
        add_shortcode('hearing_aid_products', array($this, 'products_shortcode'));

        // Add content filter to display specifications on single product pages
        add_filter('the_content', array($this, 'add_specifications_to_content'), 20);

        // Add template redirect for single product pages
        add_action('template_redirect', array($this, 'single_product_template_redirect'));

        // AJAX handlers for modal functionality
        add_action('wp_ajax_hapd_get_product_modal', array($this, 'ajax_get_product_modal'));
        add_action('wp_ajax_nopriv_hapd_get_product_modal', array($this, 'ajax_get_product_modal'));
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_assets() {
        // Only enqueue on pages that need it
        if ($this->should_enqueue_assets()) {
            wp_enqueue_style(
                'hapd-frontend',
                HAPD_PLUGIN_URL . 'assets/css/frontend.css',
                array(),
                HAPD_VERSION
            );
            
            wp_enqueue_script(
                'hapd-frontend',
                HAPD_PLUGIN_URL . 'assets/js/frontend.js',
                array('jquery'),
                HAPD_VERSION,
                true
            );

            // Localize script for AJAX
            wp_localize_script('hapd-frontend', 'hapd_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('hapd_ajax_nonce')
            ));

            // Enqueue zoom library if enabled
            $settings = get_option('hapd_settings', array());
            if ($settings['enable_zoom'] ?? true) {
                wp_enqueue_script(
                    'hapd-zoom',
                    HAPD_PLUGIN_URL . 'assets/js/zoom.js',
                    array('jquery'),
                    HAPD_VERSION,
                    true
                );
            }
        }
    }
    
    /**
     * Check if assets should be enqueued
     */
    private function should_enqueue_assets() {
        global $post;
        
        // Check if current post/page contains the block or shortcode
        if (is_a($post, 'WP_Post')) {
            if (has_block('hapd/hearing-aid-products', $post) || 
                has_shortcode($post->post_content, 'hearing_aid_products')) {
                return true;
            }
        }
        
        // Check if it's a hearing aid product page
        if (is_singular('hearing_aid_product') || is_post_type_archive('hearing_aid_product')) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Render products block
     */
    public function render_products_block($attributes) {
        $defaults = array(
            'selectedProducts' => array(),
            'layout' => 'grid',
            'columns' => 3,
            'showPrice' => true,
            'showSpecs' => true,
            'showFeatures' => true,
            'showExcerpt' => true,
        );
        
        $attributes = wp_parse_args($attributes, $defaults);
        
        // Get products
        $products = $this->get_products($attributes);
        
        if (empty($products)) {
            return '<div class="hapd-no-products">' . __('No hearing aid products found.', 'hearing-aid-display') . '</div>';
        }
        
        // Generate output
        $output = '<div class="hapd-products-container hapd-layout-' . esc_attr($attributes['layout']) . '">';
        
        if ($attributes['layout'] === 'grid') {
            $output .= $this->render_grid_layout($products, $attributes);
        } elseif ($attributes['layout'] === 'list') {
            $output .= $this->render_list_layout($products, $attributes);
        } elseif ($attributes['layout'] === 'carousel') {
            $output .= $this->render_carousel_layout($products, $attributes);
        }
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Get products based on attributes
     */
    private function get_products($attributes) {
        $args = array(
            'post_type' => 'hearing_aid_product',
            'post_status' => 'publish',
            'posts_per_page' => -1,
        );
        
        // If specific products are selected
        if (!empty($attributes['selectedProducts'])) {
            $args['post__in'] = $attributes['selectedProducts'];
            $args['orderby'] = 'post__in';
        }
        
        $query = new WP_Query($args);
        return $query->posts;
    }
    
    /**
     * Render grid layout
     */
    private function render_grid_layout($products, $attributes) {
        $columns = max(1, min(6, intval($attributes['columns'])));
        $output = '<div class="hapd-products-grid hapd-columns-' . $columns . '">';
        
        foreach ($products as $product) {
            $output .= $this->render_product_card($product, $attributes);
        }
        
        $output .= '</div>';
        return $output;
    }
    
    /**
     * Render list layout
     */
    private function render_list_layout($products, $attributes) {
        $output = '<div class="hapd-products-list">';
        
        foreach ($products as $product) {
            $output .= $this->render_product_list_item($product, $attributes);
        }
        
        $output .= '</div>';
        return $output;
    }

    /**
     * Get product details for modal
     */
    public function get_product_details_for_modal($product_id) {
        $product = get_post($product_id);
        if (!$product || $product->post_type !== 'hearing_aid_product') {
            return false;
        }

        $title = get_the_title($product_id);
        $content = apply_filters('the_content', $product->post_content);
        $excerpt = get_the_excerpt($product_id);
        $description = !empty($content) ? $content : $excerpt;
        $thumbnail = get_the_post_thumbnail($product_id, 'hapd-product-large');
        $price = get_post_meta($product_id, '_hapd_price', true);
        $model_number = get_post_meta($product_id, '_hapd_model_number', true);
        $warranty = get_post_meta($product_id, '_hapd_warranty', true);
        $availability = get_post_meta($product_id, '_hapd_availability', true);
        $features = get_post_meta($product_id, '_hapd_features', true);
        $specifications = get_post_meta($product_id, '_hapd_specifications', true);
        $accessories = get_post_meta($product_id, '_hapd_accessories', true);

        // Get accessories from Excel import data if not in meta
        if (empty($accessories)) {
            // Check if this was imported from Excel and has accessories data
            $excel_accessories = get_post_meta($product_id, '_hapd_excel_accessories', true);
            if (!empty($excel_accessories)) {
                $accessories = is_array($excel_accessories) ? $excel_accessories : explode('|', $excel_accessories);
            }
        }

        return array(
            'id' => $product_id,
            'title' => $title,
            'description' => $description,
            'thumbnail' => $thumbnail,
            'price' => $price,
            'model_number' => $model_number,
            'warranty' => $warranty,
            'availability' => $availability,
            'features' => $features,
            'specifications' => $specifications,
            'accessories' => $accessories,
            'permalink' => get_permalink($product_id)
        );
    }

    /**
     * Render carousel layout
     */
    private function render_carousel_layout($products, $attributes) {
        $output = '<div class="hapd-products-carousel">';
        $output .= '<div class="hapd-carousel-container">';
        $output .= '<div class="hapd-carousel-track">';
        
        foreach ($products as $product) {
            $output .= '<div class="hapd-carousel-slide">';
            $output .= $this->render_product_card($product, $attributes);
            $output .= '</div>';
        }
        
        $output .= '</div>';
        $output .= '<button class="hapd-carousel-prev" aria-label="' . __('Previous', 'hearing-aid-display') . '">‹</button>';
        $output .= '<button class="hapd-carousel-next" aria-label="' . __('Next', 'hearing-aid-display') . '">›</button>';
        $output .= '</div>';
        $output .= '<div class="hapd-carousel-dots"></div>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Render product card
     */
    private function render_product_card($product, $attributes) {
        $product_id = $product->ID;
        $title = get_the_title($product_id);
        $excerpt = get_the_excerpt($product_id);
        $content = get_the_content(null, false, $product);
        $description = !empty($content) ? $content : $excerpt;
        $thumbnail = get_the_post_thumbnail($product_id, 'hapd-product-medium');
        $availability = get_post_meta($product_id, '_hapd_availability', true);

        $output = '<div class="hapd-product-card" data-product-id="' . $product_id . '">';

        // Product image
        if ($thumbnail) {
            $output .= '<div class="hapd-product-image">';
            $output .= '<a href="' . get_permalink($product_id) . '">';
            $output .= $thumbnail;
            $output .= '</a>';

            // Availability badge
            if ($availability) {
                $availability_class = 'hapd-availability-' . str_replace('_', '-', $availability);
                $availability_text = $this->get_availability_text($availability);
                $output .= '<span class="hapd-availability-badge ' . $availability_class . '">';
                $output .= $availability_text;
                $output .= '</span>';
            }

            $output .= '</div>';
        }

        // Product content
        $output .= '<div class="hapd-product-content">';

        // Title
        $output .= '<h3 class="hapd-product-title">';
        $output .= '<a href="' . get_permalink($product_id) . '">' . esc_html($title) . '</a>';
        $output .= '</h3>';

        // Description only (no price, features, or specs in main display)
        if ($description) {
            $output .= '<div class="hapd-product-description">';
            $output .= '<p>' . wp_trim_words(strip_tags($description), 25, '...') . '</p>';
            $output .= '</div>';
        }

        // Actions
        $output .= '<div class="hapd-product-actions">';
        $output .= '<button type="button" class="hapd-btn hapd-btn-primary hapd-learn-more-btn" data-product-id="' . $product_id . '">';
        $output .= __('Learn More', 'hearing-aid-display');
        $output .= '</button>';
        $output .= $this->get_contact_button($product_id);
        $output .= '</div>';

        $output .= '</div>'; // .hapd-product-content
        $output .= '</div>'; // .hapd-product-card

        return $output;
    }
    
    /**
     * Render product list item
     */
    private function render_product_list_item($product, $attributes) {
        $product_id = $product->ID;
        $title = get_the_title($product_id);
        $excerpt = get_the_excerpt($product_id);
        $content = get_the_content(null, false, $product);
        $description = !empty($content) ? $content : $excerpt;
        $thumbnail = get_the_post_thumbnail($product_id, 'hapd-product-thumbnail');

        $output = '<div class="hapd-product-list-item" data-product-id="' . $product_id . '">';

        // Product image
        if ($thumbnail) {
            $output .= '<div class="hapd-product-image">';
            $output .= '<a href="' . get_permalink($product_id) . '">';
            $output .= $thumbnail;
            $output .= '</a>';
            $output .= '</div>';
        }

        // Product content
        $output .= '<div class="hapd-product-content">';

        // Header with title only
        $output .= '<div class="hapd-product-header">';
        $output .= '<h3 class="hapd-product-title">';
        $output .= '<a href="' . get_permalink($product_id) . '">' . esc_html($title) . '</a>';
        $output .= '</h3>';
        $output .= '</div>';

        // Description only (no price, features, or specs in main display)
        if ($description) {
            $output .= '<div class="hapd-product-description">';
            $output .= '<p>' . wp_trim_words(strip_tags($description), 35, '...') . '</p>';
            $output .= '</div>';
        }

        // Actions
        $output .= '<div class="hapd-product-actions">';
        $output .= '<button type="button" class="hapd-btn hapd-btn-primary hapd-learn-more-btn" data-product-id="' . $product_id . '">';
        $output .= __('Learn More', 'hearing-aid-display');
        $output .= '</button>';
        $output .= $this->get_contact_button($product_id);
        $output .= '</div>';

        $output .= '</div>'; // .hapd-product-content
        $output .= '</div>'; // .hapd-product-list-item

        return $output;
    }
    
    /**
     * Format price
     */
    private function format_price($price) {
        $settings = get_option('hapd_settings', array());
        $currency_symbol = $settings['currency_symbol'] ?? '$';
        $currency_position = $settings['currency_position'] ?? 'before';
        
        if ($currency_position === 'before') {
            return '<span class="hapd-price">' . $currency_symbol . $price . '</span>';
        } else {
            return '<span class="hapd-price">' . $price . $currency_symbol . '</span>';
        }
    }
    
    /**
     * Get availability text
     */
    private function get_availability_text($availability) {
        switch ($availability) {
            case 'in_stock':
                return __('In Stock', 'hearing-aid-display');
            case 'out_of_stock':
                return __('Out of Stock', 'hearing-aid-display');
            case 'contact_for_availability':
                return __('Contact for Availability', 'hearing-aid-display');
            default:
                return '';
        }
    }
    
    /**
     * Get contact button
     */
    private function get_contact_button($product_id) {
        $settings = get_option('hapd_settings', array());
        $contact_email = $settings['contact_email'] ?? get_option('admin_email');
        $contact_phone = $settings['contact_phone'] ?? '';
        
        $output = '<div class="hapd-contact-buttons">';
        
        if ($contact_email) {
            $subject = sprintf(__('Inquiry about %s', 'hearing-aid-display'), get_the_title($product_id));
            $mailto = 'mailto:' . $contact_email . '?subject=' . urlencode($subject);
            $output .= '<a href="' . $mailto . '" class="hapd-btn hapd-btn-secondary hapd-contact-email">';
            $output .= __('Email Us', 'hearing-aid-display');
            $output .= '</a>';
        }
        
        if ($contact_phone) {
            $output .= '<a href="tel:' . $contact_phone . '" class="hapd-btn hapd-btn-secondary hapd-contact-phone">';
            $output .= __('Call Us', 'hearing-aid-display');
            $output .= '</a>';
        }
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Products shortcode
     */
    public function products_shortcode($atts) {
        $attributes = shortcode_atts(array(
            'ids' => '',
            'category' => '',
            'brand' => '',
            'layout' => 'grid',
            'columns' => 3,
            'show_price' => 'true',
            'show_specs' => 'true',
            'show_features' => 'true',
            'show_excerpt' => 'true',
        ), $atts);
        
        // Convert string values to appropriate types
        $attributes['selectedProducts'] = !empty($attributes['ids']) ? 
            array_map('intval', explode(',', $attributes['ids'])) : array();
        $attributes['columns'] = intval($attributes['columns']);
        $attributes['showPrice'] = $attributes['show_price'] === 'true';
        $attributes['showSpecs'] = $attributes['show_specs'] === 'true';
        $attributes['showFeatures'] = $attributes['show_features'] === 'true';
        $attributes['showExcerpt'] = $attributes['show_excerpt'] === 'true';
        
        // Handle category and brand filtering
        if (!empty($attributes['category']) || !empty($attributes['brand'])) {
            $args = array(
                'post_type' => 'hearing_aid_product',
                'post_status' => 'publish',
                'posts_per_page' => -1,
            );
            
            $tax_query = array();
            
            if (!empty($attributes['category'])) {
                $tax_query[] = array(
                    'taxonomy' => 'hearing_aid_category',
                    'field' => 'slug',
                    'terms' => explode(',', $attributes['category']),
                );
            }
            
            if (!empty($attributes['brand'])) {
                $tax_query[] = array(
                    'taxonomy' => 'hearing_aid_brand',
                    'field' => 'slug',
                    'terms' => explode(',', $attributes['brand']),
                );
            }
            
            if (!empty($tax_query)) {
                $args['tax_query'] = $tax_query;
            }
            
            $query = new WP_Query($args);
            $attributes['selectedProducts'] = wp_list_pluck($query->posts, 'ID');
        }
        
        return $this->render_products_block($attributes);
    }
    
    /**
     * Render single product page
     */
    public function render_single_product($product_id) {
        $product = get_post($product_id);
        if (!$product || $product->post_type !== 'hearing_aid_product') {
            return '';
        }
        
        $title = get_the_title($product_id);
        $content = apply_filters('the_content', $product->post_content);
        $price = get_post_meta($product_id, '_hapd_price', true);
        $model_number = get_post_meta($product_id, '_hapd_model_number', true);
        $warranty = get_post_meta($product_id, '_hapd_warranty', true);
        $availability = get_post_meta($product_id, '_hapd_availability', true);
        $features = get_post_meta($product_id, '_hapd_features', true);
        $specifications = get_post_meta($product_id, '_hapd_specifications', true);
        $gallery_ids = get_post_meta($product_id, '_hapd_gallery_ids', true);
        
        $output = '<div class="hapd-single-product" data-product-id="' . $product_id . '">';
        
        // Product gallery
        $output .= '<div class="hapd-product-gallery">';
        if (!empty($gallery_ids)) {
            $output .= $this->render_product_gallery($gallery_ids);
        } else {
            $thumbnail = get_the_post_thumbnail($product_id, 'hapd-product-large');
            if ($thumbnail) {
                $output .= '<div class="hapd-single-image">' . $thumbnail . '</div>';
            }
        }
        $output .= '</div>';
        
        // Product info
        $output .= '<div class="hapd-product-info">';
        
        // Title and price
        $output .= '<div class="hapd-product-header">';
        $output .= '<h1 class="hapd-product-title">' . esc_html($title) . '</h1>';
        if ($price) {
            $output .= '<div class="hapd-product-price">' . $this->format_price($price) . '</div>';
        }
        $output .= '</div>';
        
        // Product details
        if ($model_number || $warranty || $availability) {
            $output .= '<div class="hapd-product-details">';
            if ($model_number) {
                $output .= '<p><strong>' . __('Model:', 'hearing-aid-display') . '</strong> ' . esc_html($model_number) . '</p>';
            }
            if ($warranty) {
                $output .= '<p><strong>' . __('Warranty:', 'hearing-aid-display') . '</strong> ' . esc_html($warranty) . '</p>';
            }
            if ($availability) {
                $availability_text = $this->get_availability_text($availability);
                $availability_class = 'hapd-availability-' . str_replace('_', '-', $availability);
                $output .= '<p class="' . $availability_class . '"><strong>' . __('Availability:', 'hearing-aid-display') . '</strong> ' . $availability_text . '</p>';
            }
            $output .= '</div>';
        }
        
        // Content
        if ($content) {
            $output .= '<div class="hapd-product-description">' . $content . '</div>';
        }
        
        // Features
        if (!empty($features)) {
            $output .= '<div class="hapd-product-features">';
            $output .= '<h3>' . __('Key Features', 'hearing-aid-display') . '</h3>';
            $output .= '<ul>';
            foreach ($features as $feature) {
                $output .= '<li>' . esc_html($feature) . '</li>';
            }
            $output .= '</ul>';
            $output .= '</div>';
        }
        
        // Specifications
        if (!empty($specifications)) {
            $output .= '<div class="hapd-product-specifications">';
            $output .= '<h3>' . __('Technical Specifications', 'hearing-aid-display') . '</h3>';
            $output .= '<table class="hapd-specs-table">';
            foreach ($specifications as $spec) {
                $output .= '<tr>';
                $output .= '<td><strong>' . esc_html($spec['name']) . '</strong></td>';
                $output .= '<td>' . esc_html($spec['value']) . '</td>';
                $output .= '</tr>';
            }
            $output .= '</table>';
            $output .= '</div>';
        }
        
        // Contact section
        $output .= '<div class="hapd-contact-section">';
        $output .= '<h3>' . __('Interested in this product?', 'hearing-aid-display') . '</h3>';
        $output .= '<p>' . __('Contact us for more information, pricing, or to schedule a consultation.', 'hearing-aid-display') . '</p>';
        $output .= $this->get_contact_button($product_id);
        $output .= '</div>';
        
        $output .= '</div>'; // .hapd-product-info
        $output .= '</div>'; // .hapd-single-product
        
        return $output;
    }
    
    /**
     * Render product gallery
     */
    private function render_product_gallery($gallery_ids) {
        if (empty($gallery_ids)) {
            return '';
        }
        
        $output = '<div class="hapd-gallery-container">';
        
        // Main image
        $main_image_id = $gallery_ids[0];
        $main_image = wp_get_attachment_image($main_image_id, 'hapd-product-large', false, array(
            'class' => 'hapd-main-image',
            'data-zoom-image' => wp_get_attachment_image_url($main_image_id, 'full')
        ));
        
        $output .= '<div class="hapd-main-gallery">' . $main_image . '</div>';
        
        // Thumbnails
        if (count($gallery_ids) > 1) {
            $output .= '<div class="hapd-gallery-thumbnails">';
            foreach ($gallery_ids as $index => $attachment_id) {
                $thumbnail = wp_get_attachment_image($attachment_id, 'hapd-product-thumbnail', false, array(
                    'class' => 'hapd-gallery-thumb' . ($index === 0 ? ' active' : ''),
                    'data-large-image' => wp_get_attachment_image_url($attachment_id, 'hapd-product-large'),
                    'data-zoom-image' => wp_get_attachment_image_url($attachment_id, 'full')
                ));
                $output .= '<div class="hapd-thumb-container">' . $thumbnail . '</div>';
            }
            $output .= '</div>';
        }
        
        $output .= '</div>';
        
        return $output;
    }

    /**
     * Add specifications to single product content
     */
    public function add_specifications_to_content($content) {
        // Only add to single hearing aid product pages
        if (!is_singular('hearing_aid_product')) {
            return $content;
        }

        $specifications = get_post_meta(get_the_ID(), '_hapd_specifications', true);

        if (empty($specifications) || !is_array($specifications)) {
            return $content;
        }

        // Build specifications HTML
        $specs_html = '<div class="hapd-specifications-section">';
        $specs_html .= '<h3>' . __('Technical Specifications', 'hearing-aid-display') . '</h3>';
        $specs_html .= '<table class="hapd-specs-table">';

        foreach ($specifications as $spec) {
            if (isset($spec['name']) && isset($spec['value'])) {
                $specs_html .= '<tr>';
                $specs_html .= '<td class="spec-name"><strong>' . esc_html($spec['name']) . '</strong></td>';
                $specs_html .= '<td class="spec-value">' . esc_html($spec['value']) . '</td>';
                $specs_html .= '</tr>';
            }
        }

        $specs_html .= '</table>';
        $specs_html .= '</div>';

        // Add specifications after the main content
        $content .= $specs_html;

        return $content;
    }

    /**
     * Handle single product template redirect
     */
    public function single_product_template_redirect() {
        if (is_singular('hearing_aid_product')) {
            // Add CSS for specifications display
            add_action('wp_head', array($this, 'add_specifications_css'));

            // Ensure specifications are displayed even if theme doesn't support them
            add_action('wp_footer', array($this, 'ensure_specifications_display'));
        }
    }

    /**
     * Add CSS for specifications display
     */
    public function add_specifications_css() {
        echo '<style>
        .hapd-specifications-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .hapd-specifications-section h3 {
            color: #0073aa;
            margin-bottom: 15px;
            font-size: 1.2em;
            border-bottom: 2px solid #0073aa;
            padding-bottom: 10px;
        }
        .hapd-specs-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .hapd-specs-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }
        .hapd-specs-table .spec-name {
            font-weight: bold;
            width: 30%;
            color: #333;
        }
        .hapd-specs-table .spec-value {
            color: #666;
        }
        .hapd-specs-table tr:hover {
            background-color: #f5f5f5;
        }
        .hapd-specs-table tr:last-child td {
            border-bottom: none;
        }
        </style>';
    }

    /**
     * Ensure specifications display (fallback)
     */
    public function ensure_specifications_display() {
        // This is a fallback in case the content filter doesn't work
        // Could be used to inject specifications via JavaScript if needed
    }

    /**
     * AJAX handler for product modal
     */
    public function ajax_get_product_modal() {
        // Debug logging
        error_log('HAPD Modal AJAX Handler Called');
        error_log('POST Data: ' . print_r($_POST, true));

        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'hapd_ajax_nonce')) {
            error_log('HAPD Modal: Nonce verification failed');
            wp_send_json_error('Security check failed');
        }

        $product_id = intval($_POST['product_id'] ?? 0);
        if (!$product_id) {
            error_log('HAPD Modal: Invalid product ID');
            wp_send_json_error('Invalid product ID');
        }

        error_log('HAPD Modal: Getting product data for ID: ' . $product_id);
        $product_data = $this->get_product_details_for_modal($product_id);
        if (!$product_data) {
            error_log('HAPD Modal: Product not found for ID: ' . $product_id);
            wp_send_json_error('Product not found');
        }

        // Debug product data
        error_log('HAPD Modal: Product data - Title: ' . $product_data['title']);
        error_log('HAPD Modal: Product data - Price: ' . ($product_data['price'] ? $product_data['price'] : 'NO PRICE'));
        error_log('HAPD Modal: Product data - Has thumbnail: ' . ($product_data['thumbnail'] ? 'YES' : 'NO'));

        // Render modal content
        error_log('HAPD Modal: Rendering modal content');
        $modal_content = $this->render_product_modal_content($product_data);

        error_log('HAPD Modal: Sending success response');
        wp_send_json_success(array(
            'content' => $modal_content,
            'title' => $product_data['title']
        ));
    }

    /**
     * Render product modal content
     */
    private function render_product_modal_content($product_data) {
        $output = '<div class="hapd-modal-content">';

        // Product image
        if ($product_data['thumbnail']) {
            $output .= '<div class="hapd-modal-image">';
            $output .= $product_data['thumbnail'];
            $output .= '</div>';
        }

        // Product info
        $output .= '<div class="hapd-modal-info">';

        // Title
        $output .= '<div class="hapd-modal-product-header">';
        $output .= '<h2 class="hapd-modal-product-title">' . esc_html($product_data['title']) . '</h2>';
        $output .= '</div>';

        // Price - Make it prominent
        if (!empty($product_data['price'])) {
            $output .= '<div class="hapd-modal-price-section">';
            $output .= '<div class="hapd-modal-price">' . $this->format_price($product_data['price']) . '</div>';
            $output .= '</div>';
        } else {
            // Debug: Show if price is missing
            $output .= '<div class="hapd-modal-price-section">';
            $output .= '<div class="hapd-modal-price-missing">Price not available</div>';
            $output .= '</div>';
        }

        // Product details
        if ($product_data['model_number'] || $product_data['warranty'] || $product_data['availability']) {
            $output .= '<div class="hapd-modal-details">';
            if ($product_data['model_number']) {
                $output .= '<p><strong>' . __('Model:', 'hearing-aid-display') . '</strong> ' . esc_html($product_data['model_number']) . '</p>';
            }
            if ($product_data['warranty']) {
                $output .= '<p><strong>' . __('Warranty:', 'hearing-aid-display') . '</strong> ' . esc_html($product_data['warranty']) . '</p>';
            }
            if ($product_data['availability']) {
                $availability_text = $this->get_availability_text($product_data['availability']);
                $availability_class = 'hapd-availability-' . str_replace('_', '-', $product_data['availability']);
                $output .= '<p class="' . $availability_class . '"><strong>' . __('Availability:', 'hearing-aid-display') . '</strong> ' . $availability_text . '</p>';
            }
            $output .= '</div>';
        }

        // Description
        if ($product_data['description']) {
            $output .= '<div class="hapd-modal-description">';
            $output .= '<h3>' . __('Description', 'hearing-aid-display') . '</h3>';
            $output .= '<div>' . wp_kses_post($product_data['description']) . '</div>';
            $output .= '</div>';
        }

        // Specifications
        if (!empty($product_data['specifications'])) {
            $output .= '<div class="hapd-modal-specifications">';
            $output .= '<h3>' . __('Technical Specifications', 'hearing-aid-display') . '</h3>';
            $output .= '<table class="hapd-specs-table">';
            foreach ($product_data['specifications'] as $spec) {
                if (isset($spec['name']) && isset($spec['value'])) {
                    $output .= '<tr>';
                    $output .= '<td><strong>' . esc_html($spec['name']) . '</strong></td>';
                    $output .= '<td>' . esc_html($spec['value']) . '</td>';
                    $output .= '</tr>';
                }
            }
            $output .= '</table>';
            $output .= '</div>';
        }

        // Accessories
        if (!empty($product_data['accessories'])) {
            $output .= '<div class="hapd-modal-accessories">';
            $output .= '<h3>' . __('Included Accessories', 'hearing-aid-display') . '</h3>';
            $output .= '<ul>';
            $accessories = is_array($product_data['accessories']) ? $product_data['accessories'] : explode('|', $product_data['accessories']);
            foreach ($accessories as $accessory) {
                $accessory = trim($accessory);
                if (!empty($accessory)) {
                    $output .= '<li>' . esc_html($accessory) . '</li>';
                }
            }
            $output .= '</ul>';
            $output .= '</div>';
        }

        // Features
        if (!empty($product_data['features'])) {
            $output .= '<div class="hapd-modal-features">';
            $output .= '<h3>' . __('Key Features', 'hearing-aid-display') . '</h3>';
            $output .= '<ul>';
            foreach ($product_data['features'] as $feature) {
                $output .= '<li>' . esc_html($feature) . '</li>';
            }
            $output .= '</ul>';
            $output .= '</div>';
        }

        // Contact section
        $output .= '<div class="hapd-modal-contact">';
        $output .= '<h3>' . __('Interested in this product?', 'hearing-aid-display') . '</h3>';
        $output .= '<p>' . __('Contact us for more information, pricing, or to schedule a consultation.', 'hearing-aid-display') . '</p>';
        $output .= $this->get_contact_button($product_data['id']);
        $output .= '</div>';

        // View full page link
        $output .= '<div class="hapd-modal-actions">';
        $output .= '<a href="' . $product_data['permalink'] . '" class="hapd-btn hapd-btn-primary">';
        $output .= __('View Full Details', 'hearing-aid-display');
        $output .= '</a>';
        $output .= '</div>';

        $output .= '</div>'; // .hapd-modal-info
        $output .= '</div>'; // .hapd-modal-content

        return $output;
    }
}
