<?php
/**
 * Import Testing Suite
 * 
 * Comprehensive testing tool for Excel import functionality
 * Tests both sample template and user's Excel files
 */

// WordPress Bootstrap
require_once('../../../wp-config.php');

// Set content type
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Import Testing Suite - ইমপোর্ট টেস্টিং স্যুট</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 5px; }
        .success { color: #008000; }
        .error { color: #cc0000; }
        .warning { color: #ff8800; }
        .info { color: #0073aa; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h2 { color: #23282d; border-bottom: 2px solid #0073aa; padding-bottom: 5px; }
        h3 { color: #32373c; }
        .test-button { background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px; }
        .test-button:hover { background: #005a87; }
        .test-button.success { background: #28a745; }
        .test-button.warning { background: #ffc107; color: #212529; }
        .test-button.danger { background: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f9f9f9; padding: 10px; border-left: 4px solid #0073aa; overflow-x: auto; }
        .bangla { background: #e8f4fd; padding: 15px; border-left: 4px solid #0073aa; margin: 10px 0; }
        .test-result { padding: 15px; border-radius: 5px; margin: 10px 0; }
        .test-passed { background: #d4edda; border: 1px solid #c3e6cb; }
        .test-failed { background: #f8d7da; border: 1px solid #f5c6cb; }
        .test-warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        .progress-bar { width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: #0073aa; transition: width 0.3s ease; }
        .download-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .download-card { padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Import Testing Suite - ইমপোর্ট টেস্টিং স্যুট</h1>
        
        <div class="bangla">
            <h3>🇧🇩 এই টেস্টিং স্যুট কি করে:</h3>
            <p><strong>উদ্দেশ্য:</strong> Excel import functionality সম্পূর্ণভাবে test করে</p>
            <p><strong>বৈশিষ্ট্য:</strong> Sample template এবং আপনার Excel file দুটোই test করতে পারে</p>
            <p><strong>ফলাফল:</strong> Import করার আগেই সব সমস্যা identify করে</p>
        </div>

        <div class="download-grid">
            <div class="download-card">
                <h3>📊 Sample Excel Template</h3>
                <p><strong>10 Products</strong> with perfect specifications</p>
                <p><a href="create-excel-template.php" class="test-button success" download>Download Excel (.xls)</a></p>
                <p><a href="comprehensive-hearing-aid-template.csv" class="test-button success" download>Download CSV (.csv)</a></p>
            </div>
            <div class="download-card">
                <h3>🔍 Validation Tools</h3>
                <p>Test your specification format</p>
                <p><a href="specification-format-validator.php" class="test-button info">Format Validator</a></p>
                <p><a href="specification-diagnostic.php" class="test-button info">Diagnostic Tool</a></p>
            </div>
        </div>
        
        <?php if (isset($_GET['run_tests'])): ?>
        <div class="section">
            <h2>🧪 Running Comprehensive Tests - সম্পূর্ণ টেস্ট চালানো হচ্ছে</h2>
            
            <div class="progress-bar">
                <div class="progress-fill" style="width: 100%;"></div>
            </div>
            
            <?php
            $test_results = array();
            $total_tests = 0;
            $passed_tests = 0;
            
            // Test 1: Plugin Status
            echo "<h3>Test 1: Plugin Status Check</h3>";
            if (is_plugin_active('hearing-aid-product-display/hearing-aid-product-display.php')) {
                echo "<div class='test-result test-passed'>";
                echo "<p class='success'>✅ Plugin is active and ready</p>";
                echo "</div>";
                $passed_tests++;
            } else {
                echo "<div class='test-result test-failed'>";
                echo "<p class='error'>❌ Plugin is NOT active</p>";
                echo "</div>";
            }
            $total_tests++;
            
            // Test 2: Excel Importer Class
            echo "<h3>Test 2: Excel Importer Class Check</h3>";
            if (class_exists('HAPD_Excel_Importer')) {
                echo "<div class='test-result test-passed'>";
                echo "<p class='success'>✅ HAPD_Excel_Importer class exists</p>";
                echo "</div>";
                $passed_tests++;
            } else {
                echo "<div class='test-result test-failed'>";
                echo "<p class='error'>❌ HAPD_Excel_Importer class NOT found</p>";
                echo "</div>";
            }
            $total_tests++;
            
            // Test 3: Specification Parsing
            echo "<h3>Test 3: Specification Parsing Test</h3>";
            $test_specs = array(
                'Frequency: 100Hz-8kHz|Battery: 24 hours|Channels: 20|Weight: 2.3g',
                "Frequency: 200Hz-6kHz\nBattery: 120 hours\nChannels: 16",
                'Advanced digital hearing aid with noise reduction'
            );
            
            $parsing_success = 0;
            foreach ($test_specs as $index => $test_spec) {
                echo "<h4>Parsing Test " . ($index + 1) . ":</h4>";
                echo "<p><strong>Input:</strong> " . htmlspecialchars($test_spec) . "</p>";
                
                // Simulate parsing
                $specifications = array();
                $spec_data = trim($test_spec);
                
                if (strpos($spec_data, '|') !== false) {
                    $specs = explode('|', $spec_data);
                    foreach ($specs as $spec) {
                        $spec = trim($spec);
                        if (!empty($spec) && strpos($spec, ':') !== false) {
                            $parts = explode(':', $spec, 2);
                            if (count($parts) >= 2) {
                                $specifications[] = array(
                                    'name' => trim($parts[0]),
                                    'value' => trim($parts[1])
                                );
                            }
                        }
                    }
                } elseif (strpos($spec_data, "\n") !== false) {
                    $lines = explode("\n", $spec_data);
                    foreach ($lines as $line) {
                        $line = trim($line);
                        if (!empty($line) && strpos($line, ':') !== false) {
                            $parts = explode(':', $line, 2);
                            if (count($parts) >= 2) {
                                $specifications[] = array(
                                    'name' => trim($parts[0]),
                                    'value' => trim($parts[1])
                                );
                            }
                        }
                    }
                } else {
                    $specifications[] = array(
                        'name' => 'Specification',
                        'value' => $spec_data
                    );
                }
                
                if (!empty($specifications)) {
                    echo "<div class='test-result test-passed'>";
                    echo "<p class='success'>✅ Parsed " . count($specifications) . " specifications successfully</p>";
                    echo "<ul>";
                    foreach ($specifications as $spec) {
                        echo "<li><strong>" . htmlspecialchars($spec['name']) . ":</strong> " . htmlspecialchars($spec['value']) . "</li>";
                    }
                    echo "</ul>";
                    echo "</div>";
                    $parsing_success++;
                } else {
                    echo "<div class='test-result test-failed'>";
                    echo "<p class='error'>❌ Failed to parse specifications</p>";
                    echo "</div>";
                }
            }
            
            if ($parsing_success == count($test_specs)) {
                $passed_tests++;
            }
            $total_tests++;
            
            // Test 4: Database Connection
            echo "<h3>Test 4: Database Connection Test</h3>";
            global $wpdb;
            $test_query = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'hearing_aid_product'");
            if ($test_query !== null) {
                echo "<div class='test-result test-passed'>";
                echo "<p class='success'>✅ Database connection working. Found $test_query hearing aid products</p>";
                echo "</div>";
                $passed_tests++;
            } else {
                echo "<div class='test-result test-failed'>";
                echo "<p class='error'>❌ Database connection failed</p>";
                echo "</div>";
            }
            $total_tests++;
            
            // Test 5: Meta Field Storage Test
            echo "<h3>Test 5: Meta Field Storage Test</h3>";
            $recent_products = get_posts(array(
                'post_type' => 'hearing_aid_product',
                'numberposts' => 5,
                'post_status' => array('publish', 'draft')
            ));
            
            $products_with_specs = 0;
            foreach ($recent_products as $product) {
                $specs = get_post_meta($product->ID, '_hapd_specifications', true);
                if (!empty($specs)) {
                    $products_with_specs++;
                }
            }
            
            if (count($recent_products) > 0) {
                $spec_percentage = ($products_with_specs / count($recent_products)) * 100;
                if ($spec_percentage >= 50) {
                    echo "<div class='test-result test-passed'>";
                    echo "<p class='success'>✅ Meta field storage working. " . round($spec_percentage) . "% of products have specifications</p>";
                    echo "</div>";
                    $passed_tests++;
                } else {
                    echo "<div class='test-result test-warning'>";
                    echo "<p class='warning'>⚠️ Only " . round($spec_percentage) . "% of products have specifications</p>";
                    echo "</div>";
                }
            } else {
                echo "<div class='test-result test-warning'>";
                echo "<p class='warning'>⚠️ No products found to test meta field storage</p>";
                echo "</div>";
            }
            $total_tests++;
            
            // Test Summary
            echo "<h3>📊 Test Summary - টেস্ট সারসংক্ষেপ</h3>";
            $success_rate = ($passed_tests / $total_tests) * 100;
            
            if ($success_rate >= 80) {
                echo "<div class='test-result test-passed'>";
                echo "<p class='success'><strong>✅ EXCELLENT!</strong> " . $passed_tests . "/" . $total_tests . " tests passed (" . round($success_rate) . "%)</p>";
                echo "<p>Your system is ready for Excel import with specifications!</p>";
                echo "</div>";
            } elseif ($success_rate >= 60) {
                echo "<div class='test-result test-warning'>";
                echo "<p class='warning'><strong>⚠️ GOOD</strong> " . $passed_tests . "/" . $total_tests . " tests passed (" . round($success_rate) . "%)</p>";
                echo "<p>Most features working, but some issues need attention.</p>";
                echo "</div>";
            } else {
                echo "<div class='test-result test-failed'>";
                echo "<p class='error'><strong>❌ NEEDS ATTENTION</strong> " . $passed_tests . "/" . $total_tests . " tests passed (" . round($success_rate) . "%)</p>";
                echo "<p>Several issues need to be resolved before importing.</p>";
                echo "</div>";
            }
            
            echo "<div class='bangla'>";
            echo "<h4>🎯 পরবর্তী পদক্ষেপ:</h4>";
            if ($success_rate >= 80) {
                echo "<p class='success'><strong>চমৎকার!</strong> আপনার system Excel import এর জন্য ready। এখন:</p>";
                echo "<ol>";
                echo "<li>Sample template download করুন এবং test করুন</li>";
                echo "<li>আপনার Excel file এর format check করুন</li>";
                echo "<li>Import করুন এবং results verify করুন</li>";
                echo "</ol>";
            } else {
                echo "<p class='warning'><strong>সতর্কতা!</strong> কিছু সমস্যা আছে। প্রথমে:</p>";
                echo "<ol>";
                echo "<li>Plugin activate করুন যদি inactive থাকে</li>";
                echo "<li>WordPress error logs check করুন</li>";
                echo "<li>Database connection verify করুন</li>";
                echo "<li>আবার test run করুন</li>";
                echo "</ol>";
            }
            echo "</div>";
            ?>
        </div>
        <?php endif; ?>
        
        <div class="section">
            <h2>🚀 Quick Start Testing - দ্রুত টেস্ট শুরু</h2>
            
            <div class="bangla">
                <h3>🎯 আপনার Excel file test করার জন্য:</h3>
                <ol>
                    <li><strong>System test করুন:</strong> নিচের button এ click করুন</li>
                    <li><strong>Sample download করুন:</strong> Reference হিসেবে আমার template নিন</li>
                    <li><strong>Format validate করুন:</strong> আপনার Column G data check করুন</li>
                    <li><strong>Import test করুন:</strong> WordPress admin এ গিয়ে import করুন</li>
                </ol>
            </div>
            
            <p><a href="?run_tests=1" class="test-button">🧪 Run Complete System Test</a></p>
            <p><a href="specification-format-validator.php" class="test-button info">🔍 Validate Your Specification Format</a></p>
            <p><a href="<?php echo admin_url('admin.php?page=hearing-aid-import-export'); ?>" class="test-button success">📊 Go to Import Page</a></p>
        </div>
        
        <div class="section">
            <h2>📋 Sample Template Details - নমুনা টেমপ্লেট বিবরণ</h2>
            
            <h3>✅ What's Included in the 10-Row Template:</h3>
            
            <table>
                <tr><th>Product Type</th><th>Model</th><th>Key Specifications</th><th>Price Range</th></tr>
                <tr><td>Premium BTE</td><td>HA-PRO-2024</td><td>20 channels, Bluetooth 5.2, IP68, AI noise reduction</td><td>$2,499</td></tr>
                <tr><td>Comfort BTE</td><td>BTE-COMFORT-MAX</td><td>16 channels, 120h battery, feedback cancellation</td><td>$1,899</td></tr>
                <tr><td>Discrete ITC</td><td>ITC-DISCRETE-PLUS</td><td>12 channels, custom fit, wind noise reduction</td><td>$1,599</td></tr>
                <tr><td>Invisible CIC</td><td>CIC-INVISIBLE-PRO</td><td>8 channels, deep canal fit, nearly invisible</td><td>$1,299</td></tr>
                <tr><td>Smart RIC</td><td>RIC-SMART-CONNECT</td><td>24 channels, app control, machine learning</td><td>$2,199</td></tr>
                <tr><td>Power BTE</td><td>POWER-BTE-ULTRA</td><td>16 channels, 140dB max output, military grade</td><td>$2,799</td></tr>
                <tr><td>Mini BTE</td><td>MINI-BTE-LIFESTYLE</td><td>14 channels, 8 color options, quick charge</td><td>$1,699</td></tr>
                <tr><td>Tinnitus Relief</td><td>TINNITUS-RELIEF-PRO</td><td>18 channels, 12 therapy programs, sleep mode</td><td>$2,399</td></tr>
                <tr><td>Sport Active</td><td>SPORT-ACTIVE-PLUS</td><td>16 channels, IP68, shock resistant, fitness sync</td><td>$1,999</td></tr>
                <tr><td>Budget Basic</td><td>BUDGET-ESSENTIAL-BASIC</td><td>6 channels, 200h battery, basic controls</td><td>$599</td></tr>
            </table>
            
            <div class="bangla">
                <h4>🌟 Template এর বিশেষত্ব:</h4>
                <ul>
                    <li><strong>বৈচিত্র্যময়:</strong> সব ধরনের hearing aids (BTE, ITC, CIC, RIC)</li>
                    <li><strong>বাস্তবসম্মত:</strong> Real-world specifications এবং prices</li>
                    <li><strong>সঠিক ফরম্যাট:</strong> সব Column G data perfect format এ</li>
                    <li><strong>Complete data:</strong> সব columns এ proper data আছে</li>
                    <li><strong>Ready to use:</strong> Direct import করা যাবে</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>🔧 Troubleshooting Guide - সমস্যা সমাধান গাইড</h2>
            
            <div class="bangla">
                <h3>🚨 সাধারণ সমস্যা ও সমাধান:</h3>
                
                <h4>সমস্যা 1: Specifications import হচ্ছে না</h4>
                <p><strong>কারণ:</strong> Column G এ ভুল format</p>
                <p><strong>সমাধান:</strong> "Name: Value|Name: Value" format ব্যবহার করুন</p>
                
                <h4>সমস্যা 2: Partial specifications পাচ্ছি</h4>
                <p><strong>কারণ:</strong> Colon (:) missing বা inconsistent format</p>
                <p><strong>সমাধান:</strong> সব specifications এ colon ব্যবহার করুন</p>
                
                <h4>সমস্যা 3: Frontend এ দেখাচ্ছে না</h4>
                <p><strong>কারণ:</strong> Theme compatibility বা cache issue</p>
                <p><strong>সমাধান:</strong> Cache clear করুন, default theme test করুন</p>
                
                <h4>সমস্যা 4: Import error পাচ্ছি</h4>
                <p><strong>কারণ:</strong> File format বা size issue</p>
                <p><strong>সমাধান:</strong> .xlsx format ব্যবহার করুন, file size check করুন</p>
            </div>
        </div>
    </div>
</body>
</html>
