# 🔧 Hearing Aid Products Display Troubleshooting Guide

## 🎯 **Quick Fix Summary**

**Main Issue:** Imported products are set to "draft" status and won't display on frontend.

**Immediate Solution:**
1. Visit: `http://localhost/wordpress/wp-content/plugins/hearing-aid-product-display/quick-diagnosis.php`
2. Click "Publish All Draft Products" button
3. Test shortcode: `[hearing_aid_products]` on any page

---

## 📋 **Step-by-Step Troubleshooting**

### **Step 1: Check Import Status**
```
WordPress Admin → Hearing Aids → All Products
```
- Look for products with "Draft" status
- These won't show on frontend

### **Step 2: Publish Draft Products**

**Method A - Bulk Edit (Recommended):**
1. Go to **Hearing Aids → All Products**
2. Select all draft products (checkbox)
3. **Bulk Actions → Edit**
4. **Status → Published**
5. Click **Update**

**Method B - Quick Diagnosis Tool:**
1. Visit: `quick-diagnosis.php` in plugin folder
2. Click "Publish All Draft Products"

**Method C - Database Query (Advanced):**
```sql
UPDATE wp_posts 
SET post_status = 'publish' 
WHERE post_type = 'hearing_aid_product' 
AND post_status = 'draft';
```

### **Step 3: Test Frontend Display**

**Shortcode Test:**
```
[hearing_aid_products]
[hearing_aid_products layout="grid" columns="3"]
[hearing_aid_products show_price="true" show_features="true"]
```

**Gutenberg Block Test:**
1. Edit any page
2. Add Block → Search "Hearing Aid Products"
3. Configure options and publish

### **Step 4: Verify Plugin Components**

**Check Plugin Status:**
- Plugin must be active
- Post type `hearing_aid_product` must exist
- Shortcode `[hearing_aid_products]` must be registered

**Check Frontend Classes:**
- `HAPD_Frontend` class loaded
- `HAPD_Blocks` class loaded
- CSS/JS files enqueued

---

## 🚨 **Common Issues & Solutions**

### **Issue 1: No Products Showing**
**Cause:** Products in draft status
**Solution:** Publish products (see Step 2)

### **Issue 2: Shortcode Not Working**
**Cause:** Plugin not active or shortcode not registered
**Solution:** 
- Reactivate plugin
- Check for PHP errors in error log

### **Issue 3: Gutenberg Block Missing**
**Cause:** Block not registered properly
**Solution:**
- Clear browser cache
- Check JavaScript console for errors

### **Issue 4: Styling Issues**
**Cause:** Theme CSS conflicts
**Solution:**
- Switch to default theme temporarily
- Add custom CSS to fix conflicts

### **Issue 5: Images Not Showing**
**Cause:** Wrong image paths or missing files
**Solution:**
- Check uploads directory permissions
- Verify image files exist

---

## 🔍 **Diagnostic Commands**

**Check Products in Database:**
```php
$products = get_posts(array(
    'post_type' => 'hearing_aid_product',
    'post_status' => array('publish', 'draft'),
    'numberposts' => -1
));
echo count($products) . ' products found';
```

**Test Shortcode Output:**
```php
echo do_shortcode('[hearing_aid_products]');
```

**Check REST API:**
Visit: `/wp-json/wp/v2/hearing_aid_product`

---

## ⚡ **Performance Tips**

1. **Limit Products:** Use `numberposts` parameter
2. **Optimize Images:** Compress product images
3. **Cache Results:** Enable object caching
4. **Lazy Loading:** Enable for product images

---

## 🛠 **Future Import Settings**

The plugin has been updated to import products as "published" by default.
For future imports, products will be immediately visible.

**Import Status Changed:**
- **Before:** `'post_status' => 'draft'`
- **After:** `'post_status' => 'publish'`

---

## 📞 **Support Checklist**

Before seeking help, verify:
- [ ] Products exist in database
- [ ] Products are published (not draft)
- [ ] Plugin is active
- [ ] No PHP errors in error log
- [ ] Theme compatibility tested
- [ ] Caching disabled for testing
- [ ] Browser console checked for JS errors

---

## 🔗 **Useful URLs**

- **Admin Products:** `/wp-admin/edit.php?post_type=hearing_aid_product`
- **Plugin Settings:** `/wp-admin/admin.php?page=hearing-aid-settings`
- **Import/Export:** `/wp-admin/admin.php?page=hearing-aid-import-export`
- **REST API:** `/wp-json/wp/v2/hearing_aid_product`
- **Quick Diagnosis:** `/wp-content/plugins/hearing-aid-product-display/quick-diagnosis.php`
