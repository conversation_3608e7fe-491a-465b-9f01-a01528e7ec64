<?php
/**
 * Meta Boxes Class
 *
 * @package HearingAidProductDisplay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Meta Boxes Class
 */
class HAPD_Meta_Boxes {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_meta_boxes'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        add_meta_box(
            'hapd-product-details',
            __('Product Details', 'hearing-aid-display'),
            array($this, 'product_details_meta_box'),
            'hearing_aid_product',
            'normal',
            'high'
        );
        
        add_meta_box(
            'hapd-product-specifications',
            __('Technical Specifications', 'hearing-aid-display'),
            array($this, 'product_specifications_meta_box'),
            'hearing_aid_product',
            'normal',
            'high'
        );
        
        add_meta_box(
            'hapd-product-features',
            __('Key Features', 'hearing-aid-display'),
            array($this, 'product_features_meta_box'),
            'hearing_aid_product',
            'normal',
            'high'
        );
        
        add_meta_box(
            'hapd-product-gallery',
            __('Product Gallery', 'hearing-aid-display'),
            array($this, 'product_gallery_meta_box'),
            'hearing_aid_product',
            'side',
            'default'
        );
        
        add_meta_box(
            'hapd-product-seo',
            __('SEO & Marketing', 'hearing-aid-display'),
            array($this, 'product_seo_meta_box'),
            'hearing_aid_product',
            'side',
            'default'
        );
    }
    
    /**
     * Enqueue scripts for meta boxes
     */
    public function enqueue_scripts($hook) {
        global $post_type;
        
        if ($post_type === 'hearing_aid_product' && in_array($hook, array('post.php', 'post-new.php'))) {
            wp_enqueue_script('jquery-ui-sortable');
            wp_enqueue_media();
        }
    }
    
    /**
     * Product details meta box
     */
    public function product_details_meta_box($post) {
        wp_nonce_field('hapd_product_details', 'hapd_product_details_nonce');
        
        $price = get_post_meta($post->ID, '_hapd_price', true);
        $model_number = get_post_meta($post->ID, '_hapd_model_number', true);
        $warranty = get_post_meta($post->ID, '_hapd_warranty', true);
        $availability = get_post_meta($post->ID, '_hapd_availability', true);
        $sku = get_post_meta($post->ID, '_hapd_sku', true);
        $weight = get_post_meta($post->ID, '_hapd_weight', true);
        $dimensions = get_post_meta($post->ID, '_hapd_dimensions', true);
        $color = get_post_meta($post->ID, '_hapd_color', true);
        
        echo '<div class="hapd-meta-box">';
        echo '<table class="form-table">';
        
        // Price
        echo '<tr>';
        echo '<th><label for="hapd_price">' . __('Price', 'hearing-aid-display') . '</label></th>';
        echo '<td>';
        echo '<input type="text" id="hapd_price" name="hapd_price" value="' . esc_attr($price) . '" class="regular-text hapd-required" placeholder="0.00">';
        echo '<p class="description">' . __('Enter the product price (numbers only).', 'hearing-aid-display') . '</p>';
        echo '</td>';
        echo '</tr>';
        
        // Model Number
        echo '<tr>';
        echo '<th><label for="hapd_model_number">' . __('Model Number', 'hearing-aid-display') . '</label></th>';
        echo '<td>';
        echo '<input type="text" id="hapd_model_number" name="hapd_model_number" value="' . esc_attr($model_number) . '" class="regular-text">';
        echo '<p class="description">' . __('Manufacturer model number or identifier.', 'hearing-aid-display') . '</p>';
        echo '</td>';
        echo '</tr>';
        
        // SKU
        echo '<tr>';
        echo '<th><label for="hapd_sku">' . __('SKU', 'hearing-aid-display') . '</label></th>';
        echo '<td>';
        echo '<input type="text" id="hapd_sku" name="hapd_sku" value="' . esc_attr($sku) . '" class="regular-text">';
        echo '<p class="description">' . __('Stock Keeping Unit for inventory management.', 'hearing-aid-display') . '</p>';
        echo '</td>';
        echo '</tr>';
        
        // Warranty
        echo '<tr>';
        echo '<th><label for="hapd_warranty">' . __('Warranty', 'hearing-aid-display') . '</label></th>';
        echo '<td>';
        echo '<input type="text" id="hapd_warranty" name="hapd_warranty" value="' . esc_attr($warranty) . '" class="regular-text" placeholder="e.g., 2 years">';
        echo '<p class="description">' . __('Warranty period and terms.', 'hearing-aid-display') . '</p>';
        echo '</td>';
        echo '</tr>';
        
        // Availability
        echo '<tr>';
        echo '<th><label for="hapd_availability">' . __('Availability', 'hearing-aid-display') . '</label></th>';
        echo '<td>';
        echo '<select id="hapd_availability" name="hapd_availability">';
        echo '<option value="in_stock"' . selected($availability, 'in_stock', false) . '>' . __('In Stock', 'hearing-aid-display') . '</option>';
        echo '<option value="out_of_stock"' . selected($availability, 'out_of_stock', false) . '>' . __('Out of Stock', 'hearing-aid-display') . '</option>';
        echo '<option value="contact_for_availability"' . selected($availability, 'contact_for_availability', false) . '>' . __('Contact for Availability', 'hearing-aid-display') . '</option>';
        echo '<option value="discontinued"' . selected($availability, 'discontinued', false) . '>' . __('Discontinued', 'hearing-aid-display') . '</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        
        // Weight
        echo '<tr>';
        echo '<th><label for="hapd_weight">' . __('Weight', 'hearing-aid-display') . '</label></th>';
        echo '<td>';
        echo '<input type="text" id="hapd_weight" name="hapd_weight" value="' . esc_attr($weight) . '" class="regular-text" placeholder="e.g., 2.5g">';
        echo '<p class="description">' . __('Product weight with unit.', 'hearing-aid-display') . '</p>';
        echo '</td>';
        echo '</tr>';
        
        // Dimensions
        echo '<tr>';
        echo '<th><label for="hapd_dimensions">' . __('Dimensions', 'hearing-aid-display') . '</label></th>';
        echo '<td>';
        echo '<input type="text" id="hapd_dimensions" name="hapd_dimensions" value="' . esc_attr($dimensions) . '" class="regular-text" placeholder="e.g., 15 x 10 x 8 mm">';
        echo '<p class="description">' . __('Product dimensions (L x W x H).', 'hearing-aid-display') . '</p>';
        echo '</td>';
        echo '</tr>';
        
        // Color
        echo '<tr>';
        echo '<th><label for="hapd_color">' . __('Color', 'hearing-aid-display') . '</label></th>';
        echo '<td>';
        echo '<input type="text" id="hapd_color" name="hapd_color" value="' . esc_attr($color) . '" class="regular-text" placeholder="e.g., Beige, Black">';
        echo '<p class="description">' . __('Available colors or finish options.', 'hearing-aid-display') . '</p>';
        echo '</td>';
        echo '</tr>';
        
        echo '</table>';
        echo '</div>';
    }
    
    /**
     * Product specifications meta box
     */
    public function product_specifications_meta_box($post) {
        wp_nonce_field('hapd_product_specs', 'hapd_product_specs_nonce');
        
        $specifications = get_post_meta($post->ID, '_hapd_specifications', true);
        if (!is_array($specifications)) {
            $specifications = array();
        }
        
        echo '<div id="hapd-specifications-container">';
        echo '<div class="hapd-specs-header">';
        echo '<button type="button" class="button hapd-add-spec">' . __('Add Specification', 'hearing-aid-display') . '</button>';
        echo '<p class="description">' . __('Add technical specifications that will be displayed in a table format.', 'hearing-aid-display') . '</p>';
        echo '</div>';
        echo '<div class="hapd-specs-list">';
        
        if (!empty($specifications)) {
            foreach ($specifications as $index => $spec) {
                $this->render_specification_row($index, $spec);
            }
        } else {
            $this->render_specification_row(0, array('name' => '', 'value' => ''));
        }
        
        echo '</div>';
        echo '</div>';
        
        // JavaScript template
        echo '<script type="text/template" id="hapd-spec-template">';
        $this->render_specification_row('{{INDEX}}', array('name' => '', 'value' => ''));
        echo '</script>';
    }
    
    /**
     * Render specification row
     */
    private function render_specification_row($index, $spec) {
        echo '<div class="hapd-spec-row">';
        echo '<input type="text" name="hapd_specifications[' . $index . '][name]" value="' . esc_attr($spec['name']) . '" placeholder="' . __('Specification Name', 'hearing-aid-display') . '" class="hapd-spec-name" />';
        echo '<input type="text" name="hapd_specifications[' . $index . '][value]" value="' . esc_attr($spec['value']) . '" placeholder="' . __('Specification Value', 'hearing-aid-display') . '" class="hapd-spec-value" />';
        echo '<button type="button" class="button hapd-remove-spec" title="' . __('Remove Specification', 'hearing-aid-display') . '">' . __('Remove', 'hearing-aid-display') . '</button>';
        echo '</div>';
    }
    
    /**
     * Product features meta box
     */
    public function product_features_meta_box($post) {
        wp_nonce_field('hapd_product_features', 'hapd_product_features_nonce');
        
        $features = get_post_meta($post->ID, '_hapd_features', true);
        if (!is_array($features)) {
            $features = array();
        }
        
        echo '<div id="hapd-features-container">';
        echo '<div class="hapd-features-header">';
        echo '<button type="button" class="button hapd-add-feature">' . __('Add Feature', 'hearing-aid-display') . '</button>';
        echo '<p class="description">' . __('Add key features that highlight the benefits of this product.', 'hearing-aid-display') . '</p>';
        echo '</div>';
        echo '<div class="hapd-features-list">';
        
        if (!empty($features)) {
            foreach ($features as $index => $feature) {
                $this->render_feature_row($index, $feature);
            }
        } else {
            $this->render_feature_row(0, '');
        }
        
        echo '</div>';
        echo '</div>';
        
        // JavaScript template
        echo '<script type="text/template" id="hapd-feature-template">';
        $this->render_feature_row('{{INDEX}}', '');
        echo '</script>';
    }
    
    /**
     * Render feature row
     */
    private function render_feature_row($index, $feature) {
        echo '<div class="hapd-feature-row">';
        echo '<input type="text" name="hapd_features[' . $index . ']" value="' . esc_attr($feature) . '" placeholder="' . __('Feature Description', 'hearing-aid-display') . '" class="hapd-feature-input" />';
        echo '<button type="button" class="button hapd-remove-feature" title="' . __('Remove Feature', 'hearing-aid-display') . '">' . __('Remove', 'hearing-aid-display') . '</button>';
        echo '</div>';
    }
    
    /**
     * Product gallery meta box
     */
    public function product_gallery_meta_box($post) {
        wp_nonce_field('hapd_product_gallery', 'hapd_product_gallery_nonce');
        
        $gallery_ids = get_post_meta($post->ID, '_hapd_gallery_ids', true);
        if (!is_array($gallery_ids)) {
            $gallery_ids = array();
        }
        
        echo '<div id="hapd-gallery-container">';
        echo '<div class="hapd-gallery-images">';
        
        foreach ($gallery_ids as $attachment_id) {
            if (wp_attachment_is_image($attachment_id)) {
                echo '<div class="hapd-gallery-image" data-attachment-id="' . $attachment_id . '">';
                echo wp_get_attachment_image($attachment_id, 'thumbnail');
                echo '<button type="button" class="hapd-remove-image" title="' . __('Remove Image', 'hearing-aid-display') . '">×</button>';
                echo '</div>';
            }
        }
        
        echo '</div>';
        echo '<button type="button" class="button hapd-add-gallery-images">' . __('Add Images', 'hearing-aid-display') . '</button>';
        echo '<input type="hidden" name="hapd_gallery_ids" value="' . esc_attr(implode(',', $gallery_ids)) . '" />';
        echo '<p class="description">' . __('Add multiple images to create a product gallery. Images can be reordered by dragging.', 'hearing-aid-display') . '</p>';
        echo '</div>';
    }
    
    /**
     * Product SEO meta box
     */
    public function product_seo_meta_box($post) {
        wp_nonce_field('hapd_product_seo', 'hapd_product_seo_nonce');
        
        $meta_title = get_post_meta($post->ID, '_hapd_meta_title', true);
        $meta_description = get_post_meta($post->ID, '_hapd_meta_description', true);
        $featured = get_post_meta($post->ID, '_hapd_featured', true);
        $popular = get_post_meta($post->ID, '_hapd_popular', true);
        $new_product = get_post_meta($post->ID, '_hapd_new_product', true);
        
        echo '<div class="hapd-meta-box">';
        
        // Meta Title
        echo '<p>';
        echo '<label for="hapd_meta_title"><strong>' . __('SEO Title', 'hearing-aid-display') . '</strong></label><br>';
        echo '<input type="text" id="hapd_meta_title" name="hapd_meta_title" value="' . esc_attr($meta_title) . '" class="widefat" maxlength="60">';
        echo '<small>' . __('Recommended: 50-60 characters', 'hearing-aid-display') . '</small>';
        echo '</p>';
        
        // Meta Description
        echo '<p>';
        echo '<label for="hapd_meta_description"><strong>' . __('SEO Description', 'hearing-aid-display') . '</strong></label><br>';
        echo '<textarea id="hapd_meta_description" name="hapd_meta_description" class="widefat" rows="3" maxlength="160">' . esc_textarea($meta_description) . '</textarea>';
        echo '<small>' . __('Recommended: 150-160 characters', 'hearing-aid-display') . '</small>';
        echo '</p>';
        
        // Product Flags
        echo '<p><strong>' . __('Product Flags', 'hearing-aid-display') . '</strong></p>';
        
        echo '<p>';
        echo '<label>';
        echo '<input type="checkbox" name="hapd_featured" value="1"' . checked($featured, '1', false) . '>';
        echo ' ' . __('Featured Product', 'hearing-aid-display');
        echo '</label>';
        echo '</p>';
        
        echo '<p>';
        echo '<label>';
        echo '<input type="checkbox" name="hapd_popular" value="1"' . checked($popular, '1', false) . '>';
        echo ' ' . __('Popular Product', 'hearing-aid-display');
        echo '</label>';
        echo '</p>';
        
        echo '<p>';
        echo '<label>';
        echo '<input type="checkbox" name="hapd_new_product" value="1"' . checked($new_product, '1', false) . '>';
        echo ' ' . __('New Product', 'hearing-aid-display');
        echo '</label>';
        echo '</p>';
        
        echo '</div>';
    }
    
    /**
     * Save meta boxes
     */
    public function save_meta_boxes($post_id) {
        // Check if this is an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        // Check post type
        if (get_post_type($post_id) !== 'hearing_aid_product') {
            return;
        }
        
        // Check user permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Save product details
        if (isset($_POST['hapd_product_details_nonce']) && wp_verify_nonce($_POST['hapd_product_details_nonce'], 'hapd_product_details')) {
            $fields = array('hapd_price', 'hapd_model_number', 'hapd_sku', 'hapd_warranty', 'hapd_availability', 'hapd_weight', 'hapd_dimensions', 'hapd_color');
            
            foreach ($fields as $field) {
                if (isset($_POST[$field])) {
                    update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
                }
            }
        }
        
        // Save specifications
        if (isset($_POST['hapd_product_specs_nonce']) && wp_verify_nonce($_POST['hapd_product_specs_nonce'], 'hapd_product_specs')) {
            if (isset($_POST['hapd_specifications'])) {
                $specifications = array();
                foreach ($_POST['hapd_specifications'] as $spec) {
                    if (!empty($spec['name']) && !empty($spec['value'])) {
                        $specifications[] = array(
                            'name' => sanitize_text_field($spec['name']),
                            'value' => sanitize_text_field($spec['value']),
                        );
                    }
                }
                update_post_meta($post_id, '_hapd_specifications', $specifications);
            }
        }
        
        // Save features
        if (isset($_POST['hapd_product_features_nonce']) && wp_verify_nonce($_POST['hapd_product_features_nonce'], 'hapd_product_features')) {
            if (isset($_POST['hapd_features'])) {
                $features = array();
                foreach ($_POST['hapd_features'] as $feature) {
                    if (!empty($feature)) {
                        $features[] = sanitize_text_field($feature);
                    }
                }
                update_post_meta($post_id, '_hapd_features', $features);
            }
        }
        
        // Save gallery
        if (isset($_POST['hapd_product_gallery_nonce']) && wp_verify_nonce($_POST['hapd_product_gallery_nonce'], 'hapd_product_gallery')) {
            if (isset($_POST['hapd_gallery_ids'])) {
                $gallery_ids = array_map('absint', explode(',', $_POST['hapd_gallery_ids']));
                $gallery_ids = array_filter($gallery_ids);
                update_post_meta($post_id, '_hapd_gallery_ids', $gallery_ids);
            }
        }
        
        // Save SEO data
        if (isset($_POST['hapd_product_seo_nonce']) && wp_verify_nonce($_POST['hapd_product_seo_nonce'], 'hapd_product_seo')) {
            if (isset($_POST['hapd_meta_title'])) {
                update_post_meta($post_id, '_hapd_meta_title', sanitize_text_field($_POST['hapd_meta_title']));
            }
            if (isset($_POST['hapd_meta_description'])) {
                update_post_meta($post_id, '_hapd_meta_description', sanitize_textarea_field($_POST['hapd_meta_description']));
            }
            
            // Product flags
            $flags = array('hapd_featured', 'hapd_popular', 'hapd_new_product');
            foreach ($flags as $flag) {
                if (isset($_POST[$flag])) {
                    update_post_meta($post_id, '_' . $flag, '1');
                } else {
                    delete_post_meta($post_id, '_' . $flag);
                }
            }
        }
    }
}

// Initialize meta boxes
if (is_admin()) {
    new HAPD_Meta_Boxes();
}
