<?php
/**
 * Specification Import Diagnostic Tool
 * 
 * এই script টি Excel file এর Column G (Specification) import সমস্যা নির্ণয় করে
 * Browser এ এই file টি সরাসরি access করুন
 */

// WordPress Bootstrap
require_once('../../../wp-config.php');

// Set content type
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Specification Import Diagnostic - স্পেসিফিকেশন ইমপোর্ট সমস্যা নির্ণয়</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 5px; }
        .success { color: #008000; }
        .error { color: #cc0000; }
        .warning { color: #ff8800; }
        .info { color: #0073aa; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h2 { color: #23282d; border-bottom: 2px solid #0073aa; padding-bottom: 5px; }
        h3 { color: #32373c; }
        .fix-button { background: #0073aa; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px 0; }
        .fix-button:hover { background: #005a87; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f9f9f9; padding: 10px; border-left: 4px solid #0073aa; overflow-x: auto; }
        .bangla { background: #e8f4fd; padding: 15px; border-left: 4px solid #0073aa; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Specification Import Diagnostic - স্পেসিফিকেশন ইমপোর্ট সমস্যা নির্ণয়</h1>
        
        <div class="bangla">
            <h3>🇧🇩 বাংলায় সমস্যার বিবরণ:</h3>
            <p><strong>সমস্যা:</strong> Excel file upload হয়েছে কিন্তু Column G (Specification) এর data properly import হয়নি।</p>
            <p><strong>কারণ:</strong> Specification data format, parsing, বা storage এ সমস্যা হতে পারে।</p>
            <p><strong>সমাধান:</strong> আমরা step by step check করব কোথায় সমস্যা এবং কিভাবে ঠিক করতে হবে।</p>
        </div>
        
        <?php
        echo "<div class='section'>";
        echo "<h2>1. Imported Products Check - ইমপোর্ট করা প্রোডাক্ট চেক</h2>";
        
        // Get recent products
        $products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => array('publish', 'draft'),
            'numberposts' => 10,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        if (empty($products)) {
            echo "<p class='error'>❌ কোন hearing aid products পাওয়া যায়নি</p>";
            echo "<p class='info'>প্রথমে Excel file import করুন</p>";
        } else {
            echo "<p class='success'>✅ " . count($products) . " টি products পাওয়া গেছে</p>";
            
            // Check specifications for each product
            $products_with_specs = 0;
            $products_without_specs = 0;
            $spec_details = array();
            
            foreach ($products as $product) {
                $specifications = get_post_meta($product->ID, '_hapd_specifications', true);
                
                if (!empty($specifications) && is_array($specifications)) {
                    $products_with_specs++;
                    $spec_details[] = array(
                        'id' => $product->ID,
                        'title' => $product->post_title,
                        'specs' => $specifications,
                        'has_specs' => true
                    );
                } else {
                    $products_without_specs++;
                    $spec_details[] = array(
                        'id' => $product->ID,
                        'title' => $product->post_title,
                        'specs' => $specifications,
                        'has_specs' => false
                    );
                }
            }
            
            echo "<div class='bangla'>";
            echo "<h4>📊 Specification Status:</h4>";
            echo "<p><strong>Specifications আছে:</strong> $products_with_specs টি products</p>";
            echo "<p><strong>Specifications নেই:</strong> $products_without_specs টি products</p>";
            echo "</div>";
            
            if ($products_without_specs > 0) {
                echo "<p class='warning'>⚠️ " . $products_without_specs . " টি products এ specifications নেই</p>";
            }
        }
        
        echo "</div>";
        
        if (!empty($spec_details)) {
            echo "<div class='section'>";
            echo "<h2>2. Detailed Specification Analysis - বিস্তারিত স্পেসিফিকেশন বিশ্লেষণ</h2>";
            
            echo "<table>";
            echo "<tr><th>Product ID</th><th>Product Title</th><th>Specifications Status</th><th>Data Details</th></tr>";
            
            foreach (array_slice($spec_details, 0, 5) as $detail) {
                echo "<tr>";
                echo "<td>{$detail['id']}</td>";
                echo "<td>" . esc_html($detail['title']) . "</td>";
                
                if ($detail['has_specs']) {
                    echo "<td class='success'>✅ আছে (" . count($detail['specs']) . " টি)</td>";
                    echo "<td>";
                    foreach ($detail['specs'] as $spec) {
                        if (isset($spec['name']) && isset($spec['value'])) {
                            echo "<strong>" . esc_html($spec['name']) . ":</strong> " . esc_html($spec['value']) . "<br>";
                        }
                    }
                    echo "</td>";
                } else {
                    echo "<td class='error'>❌ নেই</td>";
                    echo "<td>";
                    if (empty($detail['specs'])) {
                        echo "Empty data";
                    } else {
                        echo "Data: " . esc_html(print_r($detail['specs'], true));
                    }
                    echo "</td>";
                }
                
                echo "</tr>";
            }
            
            echo "</table>";
            echo "</div>";
        }
        
        echo "<div class='section'>";
        echo "<h2>3. Excel Column G Format Check - Excel এর Column G ফরম্যাট চেক</h2>";
        
        echo "<div class='bangla'>";
        echo "<h4>🔍 Column G এ কি ধরনের data থাকা উচিত:</h4>";
        echo "</div>";
        
        echo "<h3>✅ সঠিক Format Examples:</h3>";
        echo "<table>";
        echo "<tr><th>Format Type</th><th>Example</th><th>Description</th></tr>";
        echo "<tr>";
        echo "<td>Name: Value pairs with |</td>";
        echo "<td>Frequency: 20Hz-8kHz|Battery: 24 hours|Channels: 16</td>";
        echo "<td>সবচেয়ে ভালো format</td>";
        echo "</tr>";
        echo "<tr>";
        echo "<td>Multi-line format</td>";
        echo "<td>Frequency: 20Hz-8kHz<br>Battery: 24 hours<br>Channels: 16</td>";
        echo "<td>Line break দিয়ে আলাদা</td>";
        echo "</tr>";
        echo "<tr>";
        echo "<td>Simple text</td>";
        echo "<td>Advanced digital hearing aid with 16 channels</td>";
        echo "<td>সাধারণ text</td>";
        echo "</tr>";
        echo "</table>";
        
        echo "<h3>❌ ভুল Format Examples:</h3>";
        echo "<ul>";
        echo "<li>Empty cells (খালি ঘর)</li>";
        echo "<li>শুধু numbers বা symbols</li>";
        echo "<li>Very long text without structure</li>";
        echo "</ul>";
        
        echo "</div>";
        
        // Check if we can test specification parsing
        if (isset($_GET['test_parsing'])) {
            echo "<div class='section'>";
            echo "<h2>4. Specification Parsing Test - স্পেসিফিকেশন পার্সিং টেস্ট</h2>";
            
            $test_specs = array(
                'Frequency: 20Hz-8kHz|Battery: 24 hours|Channels: 16',
                "Frequency: 20Hz-8kHz\nBattery: 24 hours\nChannels: 16",
                'Advanced digital hearing aid with noise reduction'
            );
            
            // Include the Excel importer class to test parsing
            if (class_exists('HAPD_Excel_Importer')) {
                $importer = new HAPD_Excel_Importer();
                
                foreach ($test_specs as $index => $test_spec) {
                    echo "<h4>Test " . ($index + 1) . ":</h4>";
                    echo "<p><strong>Input:</strong> " . esc_html($test_spec) . "</p>";
                    
                    // Use reflection to access private method
                    $reflection = new ReflectionClass($importer);
                    $method = $reflection->getMethod('parse_specifications');
                    $method->setAccessible(true);
                    
                    $parsed = $method->invoke($importer, $test_spec);
                    
                    echo "<p><strong>Parsed Result:</strong></p>";
                    echo "<pre>" . esc_html(print_r($parsed, true)) . "</pre>";
                }
            } else {
                echo "<p class='error'>❌ HAPD_Excel_Importer class পাওয়া যায়নি</p>";
            }
            
            echo "</div>";
        }
        
        echo "<div class='section'>";
        echo "<h2>5. Solutions - সমাধান</h2>";
        
        echo "<div class='bangla'>";
        echo "<h3>🛠️ যদি Specifications import হয়নি:</h3>";
        echo "</div>";
        
        echo "<h3>Solution 1: Excel File Format Check</h3>";
        echo "<ul>";
        echo "<li><strong>Column G check করুন:</strong> সেখানে কি data আছে?</li>";
        echo "<li><strong>Format ঠিক আছে কিনা:</strong> Name: Value format ব্যবহার করুন</li>";
        echo "<li><strong>Empty cells এড়িয়ে চলুন:</strong> যদি specification না থাকে তাহলে 'N/A' লিখুন</li>";
        echo "</ul>";
        
        echo "<h3>Solution 2: Re-import with Correct Format</h3>";
        echo "<p><a href='" . admin_url('admin.php?page=hearing-aid-import-export') . "' class='fix-button'>📊 Re-import Excel File</a></p>";
        
        echo "<h3>Solution 3: Manual Fix for Existing Products</h3>";
        echo "<p>যদি products already import হয়ে গেছে কিন্তু specifications নেই:</p>";
        echo "<ul>";
        echo "<li>WordPress Admin → Hearing Aids → All Products এ যান</li>";
        echo "<li>প্রতিটি product edit করুন</li>";
        echo "<li>'Technical Specifications' section এ manually add করুন</li>";
        echo "</ul>";
        
        echo "<h3>Solution 4: Test Parsing</h3>";
        echo "<p><a href='?test_parsing=1' class='fix-button'>🧪 Test Specification Parsing</a></p>";
        
        echo "</div>";
        
        echo "<div class='section'>";
        echo "<h2>6. Excel File Template - Excel ফাইল টেমপ্লেট</h2>";
        
        echo "<div class='bangla'>";
        echo "<h3>📋 Column G এর জন্য সঠিক format:</h3>";
        echo "</div>";
        
        echo "<table>";
        echo "<tr><th>Row</th><th>Column G (Specification) - সঠিক উদাহরণ</th></tr>";
        echo "<tr><td>1</td><td>Frequency: 100Hz-8kHz|Battery: Rechargeable|Channels: 16|Weight: 2.5g</td></tr>";
        echo "<tr><td>2</td><td>Frequency: 200Hz-6kHz|Battery: 120 hours|Channels: 8|Type: BTE</td></tr>";
        echo "<tr><td>3</td><td>Frequency: 250Hz-5kHz|Battery: 80 hours|Channels: 4|Type: ITC</td></tr>";
        echo "</table>";
        
        echo "<div class='bangla'>";
        echo "<p><strong>মনে রাখবেন:</strong></p>";
        echo "<ul>";
        echo "<li>প্রতিটি specification এর format: <code>Name: Value</code></li>";
        echo "<li>Multiple specifications আলাদা করতে <code>|</code> ব্যবহার করুন</li>";
        echo "<li>Colon (:) অবশ্যই ব্যবহার করুন name এবং value এর মধ্যে</li>";
        echo "<li>Empty cells এড়িয়ে চলুন</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "</div>";
        ?>
        
        <div class="section">
            <h2>7. Next Steps - পরবর্তী পদক্ষেপ</h2>
            
            <div class="bangla">
                <h3>🎯 এখন কি করবেন:</h3>
                <ol>
                    <li><strong>Excel file check করুন:</strong> Column G এ সঠিক format এ data আছে কিনা</li>
                    <li><strong>Format ঠিক করুন:</strong> Name: Value|Name: Value format ব্যবহার করুন</li>
                    <li><strong>Re-import করুন:</strong> সঠিক format এ Excel file আবার import করুন</li>
                    <li><strong>Verify করুন:</strong> Import এর পর products check করুন specifications আছে কিনা</li>
                    <li><strong>Frontend test করুন:</strong> Website এ গিয়ে দেখুন specifications display হচ্ছে কিনা</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
