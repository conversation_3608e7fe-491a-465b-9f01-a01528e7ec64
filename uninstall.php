<?php
/**
 * Uninstall Script
 * 
 * This file is executed when the plugin is uninstalled via WordPress admin.
 * It removes all plugin data from the database.
 *
 * @package HearingAidProductDisplay
 * @since 1.0.0
 */

// If uninstall not called from WordPress, exit
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

/**
 * Remove all plugin data
 */
function hapd_uninstall_plugin() {
    global $wpdb;
    
    // Remove custom post type posts and their meta
    $posts = get_posts(array(
        'post_type' => 'hearing_aid_product',
        'numberposts' => -1,
        'post_status' => 'any',
        'fields' => 'ids',
    ));
    
    foreach ($posts as $post_id) {
        // Delete all post meta
        $wpdb->delete(
            $wpdb->postmeta,
            array('post_id' => $post_id),
            array('%d')
        );
        
        // Delete the post
        wp_delete_post($post_id, true);
    }
    
    // Remove taxonomies and their terms
    $taxonomies = array('hearing_aid_category', 'hearing_aid_brand');
    
    foreach ($taxonomies as $taxonomy) {
        $terms = get_terms(array(
            'taxonomy' => $taxonomy,
            'hide_empty' => false,
            'fields' => 'ids',
        ));
        
        if (!is_wp_error($terms)) {
            foreach ($terms as $term_id) {
                wp_delete_term($term_id, $taxonomy);
            }
        }
    }
    
    // Remove custom database tables
    $table_names = array(
        $wpdb->prefix . 'hapd_product_specs',
        $wpdb->prefix . 'hapd_inquiries',
    );
    
    foreach ($table_names as $table_name) {
        $wpdb->query("DROP TABLE IF EXISTS $table_name");
    }
    
    // Remove plugin options
    $options = array(
        'hapd_settings',
        'hapd_version',
        'hapd_db_version',
        'hapd_activation_date',
        'hapd_usage_stats',
    );
    
    foreach ($options as $option) {
        delete_option($option);
        delete_site_option($option); // For multisite
    }
    
    // Remove user meta related to plugin
    $wpdb->delete(
        $wpdb->usermeta,
        array('meta_key' => 'hapd_user_preferences'),
        array('%s')
    );
    
    // Remove transients
    $wpdb->query("DELETE FROM $wpdb->options WHERE option_name LIKE '_transient_hapd_%'");
    $wpdb->query("DELETE FROM $wpdb->options WHERE option_name LIKE '_transient_timeout_hapd_%'");
    
    // Remove uploaded files (be careful with this)
    $upload_dir = wp_upload_dir();
    $plugin_upload_dir = $upload_dir['basedir'] . '/hearing-aid-products/';
    
    if (is_dir($plugin_upload_dir)) {
        hapd_remove_directory($plugin_upload_dir);
    }
    
    // Clear any cached data
    wp_cache_flush();
    
    // Remove rewrite rules
    flush_rewrite_rules();
    
    // Log uninstallation (optional)
    error_log('Hearing Aid Product Display plugin uninstalled and all data removed.');
}

/**
 * Recursively remove directory and its contents
 */
function hapd_remove_directory($dir) {
    if (!is_dir($dir)) {
        return false;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        
        if (is_dir($path)) {
            hapd_remove_directory($path);
        } else {
            unlink($path);
        }
    }
    
    return rmdir($dir);
}

/**
 * Backup data before uninstall (optional)
 */
function hapd_backup_data_before_uninstall() {
    global $wpdb;
    
    // Only create backup if user wants it (could be a setting)
    $create_backup = get_option('hapd_create_backup_on_uninstall', false);
    
    if (!$create_backup) {
        return;
    }
    
    $backup_data = array();
    
    // Backup products
    $products = get_posts(array(
        'post_type' => 'hearing_aid_product',
        'numberposts' => -1,
        'post_status' => 'any',
    ));
    
    foreach ($products as $product) {
        $backup_data['products'][] = array(
            'post' => $product,
            'meta' => get_post_meta($product->ID),
            'terms' => array(
                'categories' => wp_get_post_terms($product->ID, 'hearing_aid_category'),
                'brands' => wp_get_post_terms($product->ID, 'hearing_aid_brand'),
            ),
        );
    }
    
    // Backup settings
    $backup_data['settings'] = get_option('hapd_settings', array());
    
    // Backup taxonomies
    $backup_data['taxonomies'] = array(
        'categories' => get_terms(array('taxonomy' => 'hearing_aid_category', 'hide_empty' => false)),
        'brands' => get_terms(array('taxonomy' => 'hearing_aid_brand', 'hide_empty' => false)),
    );
    
    // Save backup to uploads directory
    $upload_dir = wp_upload_dir();
    $backup_file = $upload_dir['basedir'] . '/hapd-backup-' . date('Y-m-d-H-i-s') . '.json';
    
    file_put_contents($backup_file, json_encode($backup_data, JSON_PRETTY_PRINT));
    
    // Notify admin about backup location
    update_option('hapd_backup_location', $backup_file);
}

/**
 * Send uninstall feedback (optional)
 */
function hapd_send_uninstall_feedback() {
    // Only send if user opted in
    $send_feedback = get_option('hapd_send_usage_data', false);
    
    if (!$send_feedback) {
        return;
    }
    
    $feedback_data = array(
        'site_url' => home_url(),
        'wp_version' => get_bloginfo('version'),
        'plugin_version' => get_option('hapd_version', '1.0.0'),
        'uninstall_date' => current_time('mysql'),
        'products_count' => wp_count_posts('hearing_aid_product')->publish,
        'usage_duration' => time() - get_option('hapd_activation_date', time()),
    );
    
    // Send to remote server (implement your own endpoint)
    wp_remote_post('https://your-domain.com/api/plugin-feedback', array(
        'body' => json_encode($feedback_data),
        'headers' => array('Content-Type' => 'application/json'),
        'timeout' => 10,
    ));
}

/**
 * Clean up scheduled events
 */
function hapd_cleanup_scheduled_events() {
    // Remove any scheduled cron events
    $scheduled_events = array(
        'hapd_daily_cleanup',
        'hapd_weekly_stats',
        'hapd_monthly_report',
    );
    
    foreach ($scheduled_events as $event) {
        wp_clear_scheduled_hook($event);
    }
}

/**
 * Remove capabilities from roles
 */
function hapd_remove_capabilities() {
    $roles = array('administrator', 'editor', 'author');
    $capabilities = array(
        'edit_hearing_aid_product',
        'read_hearing_aid_product',
        'delete_hearing_aid_product',
        'edit_hearing_aid_products',
        'edit_others_hearing_aid_products',
        'publish_hearing_aid_products',
        'read_private_hearing_aid_products',
        'delete_hearing_aid_products',
        'delete_private_hearing_aid_products',
        'delete_published_hearing_aid_products',
        'delete_others_hearing_aid_products',
        'edit_private_hearing_aid_products',
        'edit_published_hearing_aid_products',
        'manage_hearing_aid_categories',
        'edit_hearing_aid_categories',
        'delete_hearing_aid_categories',
        'assign_hearing_aid_categories',
    );
    
    foreach ($roles as $role_name) {
        $role = get_role($role_name);
        if ($role) {
            foreach ($capabilities as $capability) {
                $role->remove_cap($capability);
            }
        }
    }
}

/**
 * Main uninstall function
 */
function hapd_run_uninstall() {
    // Check if we should keep data
    $keep_data = get_option('hapd_keep_data_on_uninstall', false);
    
    if ($keep_data) {
        // Only remove plugin files, keep data
        hapd_cleanup_scheduled_events();
        delete_option('hapd_keep_data_on_uninstall');
        return;
    }
    
    // Create backup if requested
    hapd_backup_data_before_uninstall();
    
    // Send feedback if opted in
    hapd_send_uninstall_feedback();
    
    // Clean up scheduled events
    hapd_cleanup_scheduled_events();
    
    // Remove capabilities
    hapd_remove_capabilities();
    
    // Remove all plugin data
    hapd_uninstall_plugin();
}

// Run the uninstall process
hapd_run_uninstall();
