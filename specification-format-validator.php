<?php
/**
 * Specification Format Validator
 * 
 * This tool validates your Excel Column G specification format
 * and shows exactly how it will be parsed by the import system
 */

// WordPress Bootstrap
require_once('../../../wp-config.php');

// Set content type
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Specification Format Validator - স্পেসিফিকেশন ফরম্যাট যাচাই</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 5px; }
        .success { color: #008000; }
        .error { color: #cc0000; }
        .warning { color: #ff8800; }
        .info { color: #0073aa; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h2 { color: #23282d; border-bottom: 2px solid #0073aa; padding-bottom: 5px; }
        h3 { color: #32373c; }
        .test-button { background: #0073aa; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px 0; }
        .test-button:hover { background: #005a87; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f9f9f9; padding: 10px; border-left: 4px solid #0073aa; overflow-x: auto; }
        .bangla { background: #e8f4fd; padding: 15px; border-left: 4px solid #0073aa; margin: 10px 0; }
        .format-example { background: #f0f8ff; padding: 10px; border: 1px solid #0073aa; border-radius: 3px; margin: 10px 0; }
        .validation-result { padding: 10px; border-radius: 3px; margin: 10px 0; }
        .valid { background: #d4edda; border: 1px solid #c3e6cb; }
        .invalid { background: #f8d7da; border: 1px solid #f5c6cb; }
        textarea { width: 100%; height: 100px; padding: 10px; border: 1px solid #ddd; border-radius: 3px; }
        .download-section { background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Specification Format Validator - স্পেসিফিকেশন ফরম্যাট যাচাই</h1>
        
        <div class="bangla">
            <h3>🇧🇩 এই টুল কি করে:</h3>
            <p><strong>উদ্দেশ্য:</strong> আপনার Excel file এর Column G specification format সঠিক কিনা check করে</p>
            <p><strong>কিভাবে:</strong> আপনার specification text paste করুন এবং দেখুন কিভাবে parse হবে</p>
            <p><strong>ফলাফল:</strong> Import করার আগেই জানুন সব কিছু ঠিক আছে কিনা</p>
        </div>

        <div class="download-section">
            <h2>📥 Download Comprehensive Sample Template</h2>
            <p><strong>10-Row Sample Excel Template:</strong> Complete with realistic hearing aid products and detailed specifications</p>
            <p><a href="create-excel-template.php" class="test-button" download>📊 Download Excel Template (.xls)</a></p>
            <p><a href="comprehensive-hearing-aid-template.csv" class="test-button" download>📄 Download CSV Template (.csv)</a></p>
            
            <h3>Template Features:</h3>
            <ul>
                <li>✅ <strong>10 Different Products:</strong> BTE, ITC, CIC, RIC, Power BTE, Mini BTE, etc.</li>
                <li>✅ <strong>Realistic Specifications:</strong> Frequency range, battery life, channels, weight, features</li>
                <li>✅ <strong>Perfect Format:</strong> All Column G data follows "Name: Value|Name: Value" format</li>
                <li>✅ <strong>Varied Examples:</strong> Different specification categories and complexity levels</li>
                <li>✅ <strong>Ready to Import:</strong> Can be used directly for testing import functionality</li>
            </ul>
        </div>
        
        <?php if (isset($_POST['validate_spec'])): ?>
        <div class="section">
            <h2>🧪 Validation Results - যাচাই ফলাফল</h2>
            
            <?php
            $spec_input = $_POST['spec_input'] ?? '';
            
            if (empty($spec_input)) {
                echo "<div class='validation-result invalid'>";
                echo "<p class='error'>❌ No specification data provided</p>";
                echo "</div>";
            } else {
                echo "<h3>Input Data:</h3>";
                echo "<div class='format-example'>";
                echo "<pre>" . htmlspecialchars($spec_input) . "</pre>";
                echo "</div>";
                
                // Simulate the parsing process
                $specifications = array();
                $spec_data = trim($spec_input);
                
                // Parse using the same logic as the import system
                if (strpos($spec_data, '|') !== false) {
                    // Format: "Spec1: Value1 | Spec2: Value2"
                    $specs = explode('|', $spec_data);
                    foreach ($specs as $spec) {
                        $spec = trim($spec);
                        if (!empty($spec)) {
                            if (strpos($spec, ':') !== false) {
                                $parts = explode(':', $spec, 2);
                                if (count($parts) >= 2) {
                                    $name = trim($parts[0]);
                                    $value = trim($parts[1]);
                                    if (!empty($name) && !empty($value)) {
                                        $specifications[] = array(
                                            'name' => $name,
                                            'value' => $value,
                                        );
                                    }
                                }
                            } else {
                                $specifications[] = array(
                                    'name' => 'Specification',
                                    'value' => $spec,
                                );
                            }
                        }
                    }
                } elseif (strpos($spec_data, "\n") !== false || strpos($spec_data, "\r") !== false) {
                    // Multi-line format
                    $lines = preg_split('/\r\n|\r|\n/', $spec_data);
                    foreach ($lines as $line) {
                        $line = trim($line);
                        if (!empty($line)) {
                            if (strpos($line, ':') !== false) {
                                $parts = explode(':', $line, 2);
                                if (count($parts) >= 2) {
                                    $name = trim($parts[0]);
                                    $value = trim($parts[1]);
                                    if (!empty($name) && !empty($value)) {
                                        $specifications[] = array(
                                            'name' => $name,
                                            'value' => $value,
                                        );
                                    }
                                }
                            } else {
                                $specifications[] = array(
                                    'name' => 'Specification',
                                    'value' => $line,
                                );
                            }
                        }
                    }
                } elseif (strpos($spec_data, ':') !== false) {
                    // Single line with colon
                    $parts = explode(':', $spec_data, 2);
                    if (count($parts) >= 2) {
                        $name = trim($parts[0]);
                        $value = trim($parts[1]);
                        if (!empty($name) && !empty($value)) {
                            $specifications[] = array(
                                'name' => $name,
                                'value' => $value,
                            );
                        }
                    }
                } else {
                    // Single specification
                    $specifications[] = array(
                        'name' => 'General Specification',
                        'value' => $spec_data,
                    );
                }
                
                // Show results
                if (!empty($specifications)) {
                    echo "<div class='validation-result valid'>";
                    echo "<p class='success'>✅ Format is VALID! Will parse " . count($specifications) . " specifications</p>";
                    echo "</div>";
                    
                    echo "<h3>Parsed Specifications:</h3>";
                    echo "<table>";
                    echo "<tr><th>Specification Name</th><th>Specification Value</th></tr>";
                    foreach ($specifications as $spec) {
                        echo "<tr>";
                        echo "<td><strong>" . htmlspecialchars($spec['name']) . "</strong></td>";
                        echo "<td>" . htmlspecialchars($spec['value']) . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                    
                    echo "<div class='bangla'>";
                    echo "<p class='success'><strong>✅ চমৎকার!</strong> আপনার format সঠিক। Import করলে " . count($specifications) . " টি specifications পাওয়া যাবে।</p>";
                    echo "</div>";
                    
                } else {
                    echo "<div class='validation-result invalid'>";
                    echo "<p class='error'>❌ Format has issues - no specifications could be parsed</p>";
                    echo "</div>";
                    
                    echo "<div class='bangla'>";
                    echo "<p class='error'><strong>❌ সমস্যা!</strong> আপনার format এ সমস্যা আছে। নিচের সঠিক format ব্যবহার করুন।</p>";
                    echo "</div>";
                }
            }
            ?>
        </div>
        <?php endif; ?>
        
        <div class="section">
            <h2>🧪 Test Your Specification Format - আপনার ফরম্যাট টেস্ট করুন</h2>
            
            <form method="post">
                <h3>Paste Your Column G Specification Data:</h3>
                <textarea name="spec_input" placeholder="Example: Frequency: 100Hz-8kHz|Battery: 24 hours|Channels: 16|Weight: 2.3g"><?php echo htmlspecialchars($_POST['spec_input'] ?? ''); ?></textarea>
                <br><br>
                <input type="submit" name="validate_spec" value="🔍 Validate Format" class="test-button">
            </form>
            
            <div class="bangla">
                <p><strong>কিভাবে ব্যবহার করবেন:</strong></p>
                <ol>
                    <li>আপনার Excel file এর Column G থেকে specification text copy করুন</li>
                    <li>উপরের text box এ paste করুন</li>
                    <li>"Validate Format" button এ click করুন</li>
                    <li>দেখুন কিভাবে parse হবে এবং কোন সমস্যা আছে কিনা</li>
                </ol>
            </div>
        </div>
        
        <div class="section">
            <h2>✅ Perfect Format Examples - সঠিক ফরম্যাট উদাহরণ</h2>
            
            <h3>Format 1: Pipe-separated (Recommended - সবচেয়ে ভালো)</h3>
            <div class="format-example">
                <strong>Example:</strong><br>
                <code>Frequency: 100Hz-8kHz|Battery: 24 hours rechargeable|Channels: 20|Weight: 2.3g|Type: BTE|Noise Reduction: 25dB</code>
            </div>
            
            <h3>Format 2: Multi-line</h3>
            <div class="format-example">
                <strong>Example:</strong><br>
                <code>Frequency: 100Hz-8kHz<br>
                Battery: 24 hours rechargeable<br>
                Channels: 20<br>
                Weight: 2.3g</code>
            </div>
            
            <h3>Format 3: Simple text</h3>
            <div class="format-example">
                <strong>Example:</strong><br>
                <code>Advanced digital hearing aid with 20 channels and rechargeable battery</code>
            </div>
            
            <div class="bangla">
                <h4>🎯 মনে রাখার বিষয়:</h4>
                <ul>
                    <li><strong>Colon (:)</strong> অবশ্যই ব্যবহার করুন name এবং value এর মধ্যে</li>
                    <li><strong>Pipe (|)</strong> ব্যবহার করুন multiple specifications আলাদা করতে</li>
                    <li><strong>Spaces</strong> এর আগে পরে থাকলেও সমস্যা নেই, automatically trim হবে</li>
                    <li><strong>Empty cells</strong> এড়িয়ে চলুন</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>📊 Sample Template Analysis - নমুনা টেমপ্লেট বিশ্লেষণ</h2>
            
            <p>আমি যে 10-row template তৈরি করেছি তাতে এই ধরনের specifications আছে:</p>
            
            <table>
                <tr><th>Product Type</th><th>Sample Specifications</th><th>Specification Count</th></tr>
                <tr>
                    <td>BTE Premium</td>
                    <td>Frequency: 100Hz-10kHz|Battery: 24h|Channels: 20|Weight: 2.3g|Type: BTE|Noise: 25dB|Bluetooth: 5.2|Water: IP68</td>
                    <td>8 specs</td>
                </tr>
                <tr>
                    <td>ITC Discrete</td>
                    <td>Frequency: 200Hz-7kHz|Battery: 80h|Channels: 12|Weight: 1.8g|Type: ITC|Noise: 18dB|Custom: Yes</td>
                    <td>7 specs</td>
                </tr>
                <tr>
                    <td>CIC Invisible</td>
                    <td>Frequency: 250Hz-6kHz|Battery: 60h|Channels: 8|Weight: 1.2g|Type: CIC|Visibility: Nearly invisible</td>
                    <td>6 specs</td>
                </tr>
                <tr>
                    <td>Budget Basic</td>
                    <td>Frequency: 200Hz-6kHz|Battery: 200h|Channels: 6|Weight: 3.5g|Type: Basic BTE|Warranty: 1 year</td>
                    <td>6 specs</td>
                </tr>
            </table>
            
            <div class="bangla">
                <p><strong>বিশেষত্ব:</strong></p>
                <ul>
                    <li>প্রতিটি product এ <strong>6-8 টি specifications</strong> আছে</li>
                    <li>সব specifications <strong>সঠিক format</strong> এ আছে</li>
                    <li><strong>বিভিন্ন ধরনের hearing aids</strong> এর জন্য আলাদা specs</li>
                    <li><strong>Realistic values</strong> যা actual products এ থাকে</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>🎯 Next Steps - পরবর্তী পদক্ষেপ</h2>
            
            <div class="bangla">
                <h3>আপনার Excel file এর জন্য:</h3>
                <ol>
                    <li><strong>Format validate করুন:</strong> উপরের tool ব্যবহার করে check করুন</li>
                    <li><strong>Sample download করুন:</strong> Reference হিসেবে আমার template download করুন</li>
                    <li><strong>Format match করুন:</strong> আপনার Column G data sample এর মতো করুন</li>
                    <li><strong>Import test করুন:</strong> WordPress admin এ গিয়ে import করুন</li>
                    <li><strong>Results verify করুন:</strong> Products page এ specifications check করুন</li>
                </ol>
                
                <h3>যদি সমস্যা হয়:</h3>
                <ul>
                    <li><strong>Error logs check করুন:</strong> wp-content/debug.log file দেখুন</li>
                    <li><strong>Sample file দিয়ে test করুন:</strong> আমার template import করে দেখুন</li>
                    <li><strong>Manual entry করুন:</strong> Admin panel থেকে specifications add করুন</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
