# Excel Import Guide for Hearing Aid Products

## Overview

The Hearing Aid Product Display plugin now supports importing products from Excel files (.xlsx and .xls formats). This feature allows you to bulk upload hearing aid products with all their specifications, images, and details.

## Excel File Structure

Your Excel file must have exactly **9 columns** in the following order:

| Column | Header | Description | Required | Example |
|--------|--------|-------------|----------|---------|
| A | No. | Sequential number/ID | Optional | 1, 2, 3... |
| B | Model No. | Product model number | **Required** | HA-2000, BTE-Pro |
| C | Photo | Image filename or URL | Optional | product1.jpg, https://example.com/image.jpg |
| D | Packaging | Packaging information | Optional | Box with accessories |
| E | Accessories | Included accessories (separated by \|) | Optional | Charger\|Cleaning kit\|Manual |
| F | Description | Product description | Optional | Advanced digital hearing aid with noise reduction |
| G | Specification | Technical specifications | Optional | Frequency: 20Hz-8kHz\|Battery: Rechargeable |
| H | Measurement | Product dimensions/measurements | Optional | 15 x 10 x 8 mm |
| I | Price | Product price (numbers only) | Optional | 1299.99 |

## Detailed Column Specifications

### Column A: No. (Optional)
- Sequential number or internal ID
- Used for reference only
- Can be left empty

### Column B: Model No. (Required)
- **This is the only required field**
- Will be used as the product title if no description is provided
- Should be unique for each product
- Examples: `HA-2000`, `BTE-Pro-Max`, `ITC-Digital-Plus`

### Column C: Photo (Optional)
The plugin supports two methods for importing images:

#### Method 1: Image URLs
- Provide direct URLs to images
- Images will be downloaded and added to WordPress media library
- Examples: 
  - `https://example.com/images/hearing-aid-1.jpg`
  - `https://cdn.manufacturer.com/products/ha2000.png`

#### Method 2: Local Filenames
- Upload images to `/wp-content/uploads/hearing-aid-products/` directory first
- Then reference the filename in the Excel file
- Examples: `hearing-aid-1.jpg`, `bte-pro.png`

### Column D: Packaging (Optional)
- Information about product packaging
- Will be stored as custom meta data
- Examples: `Premium gift box`, `Eco-friendly packaging`, `Travel case included`

### Column E: Accessories (Optional)
- List of included accessories
- Separate multiple accessories with the pipe symbol `|`
- Will be converted to product features
- Examples: 
  - `Charger|Cleaning kit|User manual`
  - `Batteries|Wax guards|Carrying case|Remote control`

### Column F: Description (Optional)
- Detailed product description
- Supports basic HTML formatting
- Will be used as the main product content
- If Model No. is empty, first few words will be used as product title

### Column G: Specification (Optional)
- Technical specifications
- Multiple formats supported:

#### Format 1: Name: Value pairs separated by |
```
Frequency Range: 20Hz-8kHz|Battery Life: 24 hours|Channels: 16
```

#### Format 2: Multi-line specifications
```
Frequency Range: 20Hz-8kHz
Battery Life: 24 hours
Channels: 16 channels
```

#### Format 3: Simple text
```
Advanced digital signal processing with 16 channels
```

### Column H: Measurement (Optional)
- Product dimensions or measurements
- Will be stored as product dimensions
- Examples: `15 x 10 x 8 mm`, `Weight: 2.5g`, `Length: 25mm`

### Column I: Price (Optional)
- Product price (numbers only)
- Currency symbols will be automatically removed
- Examples: `1299.99`, `$899`, `€750.50`

## Excel File Preparation Tips

### 1. Use the First Row for Headers
Your Excel file should have headers in the first row exactly as specified above:
```
No. | Model No. | Photo | Packaging | Accessories | Description | Specification | Measurement | Price
```

### 2. Data Validation
- Ensure Model No. is filled for every product
- Check that image URLs are accessible
- Verify that local image files exist in the upload directory
- Remove any currency symbols from prices

### 3. Text Formatting
- Use plain text for most fields
- For accessories, use the pipe symbol `|` as separator
- For specifications, use `Name: Value` format when possible

### 4. File Size Limits
- Maximum file size: 300MB
- Files over 50MB may take several minutes to process
- Very large files (100MB+) may require increased server memory limits
- Optimize images before uploading to reduce file size

## Image Import Options

### Option 1: Upload Images First
1. Create folder: `/wp-content/uploads/hearing-aid-products/`
2. Upload all product images to this folder
3. Reference filenames in Column C of your Excel file

### Option 2: Use Image URLs
1. Ensure images are publicly accessible
2. Use direct image URLs in Column C
3. Plugin will download and import images automatically

## Import Process

### Step 1: Prepare Excel File
1. Create Excel file with the 9 required columns
2. Fill in product data according to specifications above
3. Save as .xlsx or .xls format

### Step 2: Import via WordPress Admin
1. Go to `Hearing Aids > Import/Export`
2. Find the "Import from Excel" section
3. Click "Choose File" and select your Excel file
4. Click "Import from Excel" button

### Step 3: Monitor Progress
1. Import progress modal will appear
2. Monitor upload and processing progress
3. Review import results and any error messages

### Step 4: Review Imported Products
1. Go to `Hearing Aids > All Products`
2. Review imported products (they will be in "Draft" status)
3. Edit and publish products as needed

## Sample Excel Template

Here's a sample row of data:

| A | B | C | D | E | F | G | H | I |
|---|---|---|---|---|---|---|---|---|
| 1 | HA-2000 | hearing-aid-1.jpg | Premium box | Charger\|Cleaning kit\|Manual | Advanced digital hearing aid with superior sound quality | Frequency: 20Hz-8kHz\|Channels: 16\|Battery: Rechargeable | 15 x 10 x 8 mm | 1299.99 |

## Troubleshooting

### Common Issues

**"Model No. is required" Error**
- Ensure Column B (Model No.) has data for every row
- Check for empty cells in the Model No. column

**Image Import Failures**
- Verify image URLs are accessible
- Check that local image files exist in the correct directory
- Ensure image file formats are supported (jpg, png, gif, webp)

**Specification Parsing Issues**
- Use the `Name: Value` format for best results
- Separate multiple specifications with `|` symbol
- Avoid special characters that might break parsing

**Large File Import Timeouts**
- Files up to 300MB are supported, but may take time to process
- Files over 100MB may require increased server memory and execution time limits
- Consider splitting extremely large files (200MB+) into smaller batches
- Reduce image file sizes to improve processing speed
- Import during low-traffic periods for better performance

### Getting Help

If you encounter issues:
1. Check the import results for detailed error messages
2. Verify your Excel file format matches the specifications
3. Test with a small sample file first
4. Contact support with specific error messages

## Best Practices

1. **Start Small**: Test with 5-10 products first
2. **Backup First**: Always backup your WordPress site before large imports
3. **Validate Data**: Double-check all data before importing
4. **Image Optimization**: Optimize images for web before importing
5. **Review Results**: Always review imported products before publishing

## Server Requirements for Large Files

### For Files Over 100MB
To successfully import very large Excel files, your server may need the following PHP settings:

```ini
upload_max_filesize = 300M
post_max_size = 300M
memory_limit = 512M
max_execution_time = 300
max_input_time = 300
```

### WordPress Configuration
Add these lines to your `wp-config.php` file if needed:

```php
@ini_set('upload_max_filesize', '300M');
@ini_set('post_max_size', '300M');
@ini_set('memory_limit', '512M');
@ini_set('max_execution_time', 300);
```

### Hosting Provider Settings
- **Shared Hosting**: May have lower limits - contact support to increase
- **VPS/Dedicated**: You can modify PHP settings directly
- **WordPress.com**: Managed hosting with built-in limits
- **Cloud Hosting**: Usually configurable through control panels

### Performance Tips for Large Files
1. **Process during off-peak hours** to reduce server load
2. **Close other browser tabs** to free up memory
3. **Ensure stable internet connection** to prevent upload interruptions
4. **Monitor server resources** during large imports
5. **Consider splitting files over 200MB** into smaller batches

## Advanced Features

### Automatic Categorization
- Products can be automatically categorized based on model numbers
- Use consistent naming conventions for better organization

### Bulk Updates
- Re-import with same model numbers to update existing products
- Useful for price updates or specification changes

### Integration with Existing Products
- Imported products work seamlessly with existing plugin features
- All Gutenberg blocks and shortcodes will display imported products

---

**Need Help?** Contact support with your Excel file and any error messages for personalized assistance.
