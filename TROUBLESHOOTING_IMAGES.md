# Troubleshooting Image Display Issues - Hearing Aid Product Display Plugin

## 🔍 **Quick Diagnosis**

If your imported hearing aid products are not displaying images correctly, follow this step-by-step troubleshooting guide.

## 📋 **Step 1: Check Import Results**

### **Review Import Messages**
1. Go to `Hearing Aids > Import/Export` in WordPress admin
2. Look for any error messages from your recent import
3. Check for warnings about image import failures

### **Common Import Messages**
- ✅ `Image imported successfully from URL` - Image should be working
- ✅ `Image imported successfully from file` - Local file imported correctly
- ❌ `Failed to import image from URL` - URL was not accessible
- ❌ `Image file not found` - Local file doesn't exist in expected location
- ⚠️ `Featured image was not set` - Import succeeded but thumbnail assignment failed

## 📋 **Step 2: Check Product Status**

### **Verify Products Are Published**
1. Go to `Hearing Aids > All Products`
2. Check if imported products show as "Published" or "Draft"
3. **If Draft**: Click "Quick Edit" and change status to "Published"

### **Check Featured Images**
1. In the products list, look for the "Featured Image" column
2. Products should show thumbnail images
3. **If missing**: Click "Edit" on a product and check "Featured Image" section

## 📋 **Step 3: Check Image Files**

### **For URL-Based Images**
1. Check if the URLs in your Excel file are still accessible
2. Try opening the image URLs in a browser
3. Ensure URLs point directly to image files (not web pages)

### **For Local File Images**
1. Check if files exist in `/wp-content/uploads/hearing-aid-products/`
2. Verify file names match exactly what's in your Excel file
3. Ensure files are valid image formats (JPG, PNG, GIF, WebP)

## 📋 **Step 4: Check WordPress Media Library**

### **Verify Images Were Imported**
1. Go to `Media > Library` in WordPress admin
2. Look for recently uploaded images
3. Check if images have proper titles and are attached to products

### **Check Image Attachments**
1. Click on an image in the media library
2. Look for "Uploaded to this post" section
3. Should show the hearing aid product it's attached to

## 📋 **Step 5: Test Frontend Display**

### **Check Individual Product Pages**
1. Go to a hearing aid product page on your website
2. Look for the product image in the main content area
3. Check browser developer tools for broken image links

### **Test Gutenberg Block**
1. Edit a page and add "Hearing Aid Products" block
2. Check if products display with images
3. Try different layout options (grid, list, carousel)

### **Test Shortcode**
1. Add `[hearing_aid_products]` to a page
2. Check if products display correctly
3. Try with parameters: `[hearing_aid_products layout="grid" count="6"]`

## 🔧 **Common Solutions**

### **Solution 1: Run Diagnostic Script**
1. Access `/wp-content/plugins/hearing-aid-product-display/diagnose-imported-products.php`
2. Click "Run Diagnostic" to identify specific issues
3. Review the detailed report for problems

### **Solution 2: Run Image Fix Script**
1. Access `/wp-content/plugins/hearing-aid-product-display/fix-image-display.php`
2. **⚠️ Backup your site first**
3. Click "Run Image Display Fix" to automatically resolve common issues

### **Solution 3: Manual Image Assignment**
1. Go to `Hearing Aids > All Products`
2. Edit a product without an image
3. In "Featured Image" section, click "Set featured image"
4. Select an appropriate image from media library

### **Solution 4: Re-import Images**
1. Prepare a smaller Excel file with just problematic products
2. Ensure image URLs are accessible or files are in correct directory
3. Re-run the import to update existing products

### **Solution 5: Regenerate Thumbnails**
1. Install "Regenerate Thumbnails" plugin
2. Go to `Tools > Regenerate Thumbnails`
3. Run regeneration for all images

## 🛠️ **Advanced Troubleshooting**

### **Check Server Logs**
1. Look in WordPress debug log for HAPD-related errors
2. Check server error logs for image download failures
3. Look for memory or timeout issues during import

### **Verify Image Sizes**
1. Go to `Settings > Media` in WordPress admin
2. Check if custom image sizes are registered
3. Look for: hapd-product-thumbnail, hapd-product-medium, hapd-product-large

### **Check File Permissions**
1. Verify `/wp-content/uploads/` directory is writable
2. Check permissions on `/wp-content/uploads/hearing-aid-products/`
3. Ensure web server can read and write image files

### **Theme Compatibility**
1. Switch to a default WordPress theme temporarily
2. Check if images display correctly
3. If yes, there may be a theme compatibility issue

## 📊 **Specific Error Solutions**

### **"Image file not found" Error**
```
✅ Solutions:
1. Check file exists in /wp-content/uploads/hearing-aid-products/
2. Verify filename matches exactly (case-sensitive)
3. Ensure file has proper image extension
4. Check file permissions are readable
```

### **"Failed to import image from URL" Error**
```
✅ Solutions:
1. Test URL in browser - should show image directly
2. Check if URL requires authentication
3. Verify URL is publicly accessible
4. Try shorter URLs or use local files instead
```

### **"Featured image was not set" Error**
```
✅ Solutions:
1. Run the image fix script
2. Manually assign images in product edit screen
3. Check if attachment was created in media library
4. Verify product ID is valid
```

### **Images Import But Don't Display**
```
✅ Solutions:
1. Check if products are published (not draft)
2. Clear any caching plugins
3. Regenerate thumbnails
4. Check theme compatibility
5. Verify image sizes are registered
```

## 🎯 **Prevention Tips**

### **For Future Imports**
1. **Test Small Batches**: Import 5-10 products first to test
2. **Verify URLs**: Check all image URLs are accessible before import
3. **Prepare Local Files**: Upload images to correct directory first
4. **Use Consistent Naming**: Use clear, consistent image filenames
5. **Check File Sizes**: Ensure images aren't too large (under 5MB recommended)

### **Excel File Best Practices**
1. **Column C Format**: Use either full URLs or just filenames (not paths)
2. **Image URLs**: Must be direct links to image files
3. **Local Files**: Place in `/wp-content/uploads/hearing-aid-products/` first
4. **File Names**: Avoid spaces and special characters
5. **Test Data**: Include a few test rows to verify format

## 📞 **Getting Help**

### **If Issues Persist**
1. **Run Diagnostic**: Use the diagnostic script to get detailed information
2. **Check Logs**: Look for specific error messages in WordPress logs
3. **Document Issues**: Note exact error messages and steps taken
4. **Test Environment**: Try on a staging site first

### **Information to Provide**
- WordPress version and theme name
- Plugin version
- Exact error messages
- Sample Excel file (with sensitive data removed)
- Server configuration (PHP version, memory limits)
- Results from diagnostic script

## ✅ **Success Checklist**

After resolving issues, verify:
- [ ] Products are published and visible
- [ ] Featured images display on product pages
- [ ] Images show in product listings/grids
- [ ] Gutenberg block displays products correctly
- [ ] Shortcode works properly
- [ ] Images are properly sized and optimized
- [ ] No broken image links in browser console

**Remember**: Most image display issues are caused by incorrect file paths, inaccessible URLs, or products being in draft status. The diagnostic and fix scripts can resolve 90% of common problems automatically.
