<?php
/**
 * Diagnostic Script for Imported Hearing Aid Products
 * 
 * This script helps diagnose issues with imported products and their images
 * Run this from WordPress admin to check the status of imported products
 * 
 * @package HearingAidProductDisplay
 * @since 1.1.1
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // If not in WordPress, try to load WordPress
    $wp_load_paths = array(
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php',
    );
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress not found. Please run this from WordPress admin or ensure wp-load.php is accessible.');
    }
}

// Only allow admin users to run this diagnostic
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

/**
 * Hearing Aid Product Diagnostic Class
 */
class HAPD_Product_Diagnostic {
    
    /**
     * Run comprehensive diagnostic
     */
    public function run_diagnostic() {
        echo "<h1>Hearing Aid Product Display - Import Diagnostic</h1>\n";
        echo "<div style='font-family: monospace; background: #f0f0f0; padding: 20px; margin: 20px 0;'>\n";
        
        $this->check_plugin_status();
        $this->check_imported_products();
        $this->check_product_images();
        $this->check_image_sizes();
        $this->check_frontend_display();
        $this->provide_solutions();
        
        echo "</div>\n";
        echo "<h2>Diagnostic Complete</h2>\n";
    }
    
    /**
     * Check plugin status
     */
    private function check_plugin_status() {
        echo "<h3>1. Plugin Status Check</h3>\n";
        
        if (post_type_exists('hearing_aid_product')) {
            echo "✅ Custom post type 'hearing_aid_product' is registered\n";
        } else {
            echo "❌ Custom post type 'hearing_aid_product' is NOT registered\n";
        }
        
        if (taxonomy_exists('hearing_aid_category')) {
            echo "✅ Taxonomy 'hearing_aid_category' is registered\n";
        } else {
            echo "❌ Taxonomy 'hearing_aid_category' is NOT registered\n";
        }
        
        // Check image sizes
        global $_wp_additional_image_sizes;
        $required_sizes = array('hapd-product-thumbnail', 'hapd-product-medium', 'hapd-product-large', 'hapd-product-gallery');
        
        foreach ($required_sizes as $size) {
            if (isset($_wp_additional_image_sizes[$size])) {
                echo "✅ Image size '$size' is registered\n";
            } else {
                echo "❌ Image size '$size' is NOT registered\n";
            }
        }
        
        echo "\n";
    }
    
    /**
     * Check imported products
     */
    private function check_imported_products() {
        echo "<h3>2. Imported Products Check</h3>\n";
        
        $products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => array('publish', 'draft', 'private'),
            'numberposts' => -1,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        if (empty($products)) {
            echo "❌ No hearing aid products found in database\n";
            echo "   This suggests the import may have failed or products were not created\n";
            return;
        }
        
        echo "✅ Found " . count($products) . " hearing aid products\n";
        
        // Check recent products (likely imported)
        $recent_products = array_slice($products, 0, 10);
        echo "\n<strong>Recent Products (likely imported):</strong>\n";
        
        foreach ($recent_products as $product) {
            echo "- ID: {$product->ID} | Title: {$product->post_title} | Status: {$product->post_status} | Date: {$product->post_date}\n";
            
            // Check if it has featured image
            if (has_post_thumbnail($product->ID)) {
                $thumbnail_id = get_post_thumbnail_id($product->ID);
                echo "  ✅ Has featured image (ID: $thumbnail_id)\n";
            } else {
                echo "  ❌ No featured image\n";
            }
            
            // Check meta data
            $model_no = get_post_meta($product->ID, '_hapd_model_number', true);
            $price = get_post_meta($product->ID, '_hapd_price', true);
            
            if ($model_no) {
                echo "  ✅ Model Number: $model_no\n";
            } else {
                echo "  ❌ No model number\n";
            }
            
            if ($price) {
                echo "  ✅ Price: $price\n";
            } else {
                echo "  ⚠️  No price set\n";
            }
        }
        
        echo "\n";
    }
    
    /**
     * Check product images
     */
    private function check_product_images() {
        echo "<h3>3. Product Images Check</h3>\n";
        
        $products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => array('publish', 'draft', 'private'),
            'numberposts' => 10,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        $with_images = 0;
        $without_images = 0;
        $broken_images = 0;
        
        foreach ($products as $product) {
            if (has_post_thumbnail($product->ID)) {
                $with_images++;
                $thumbnail_id = get_post_thumbnail_id($product->ID);
                $image_url = wp_get_attachment_url($thumbnail_id);
                
                // Check if image file exists
                $image_path = get_attached_file($thumbnail_id);
                if ($image_path && file_exists($image_path)) {
                    echo "✅ Product '{$product->post_title}' has valid image\n";
                } else {
                    $broken_images++;
                    echo "❌ Product '{$product->post_title}' has broken image (ID: $thumbnail_id)\n";
                    echo "   Image URL: $image_url\n";
                    echo "   Image Path: $image_path\n";
                }
            } else {
                $without_images++;
                echo "⚠️  Product '{$product->post_title}' has no featured image\n";
            }
        }
        
        echo "\n<strong>Image Summary:</strong>\n";
        echo "Products with images: $with_images\n";
        echo "Products without images: $without_images\n";
        echo "Products with broken images: $broken_images\n";
        
        // Check uploads directory
        $upload_dir = wp_upload_dir();
        $hapd_upload_dir = $upload_dir['basedir'] . '/hearing-aid-products';
        
        if (file_exists($hapd_upload_dir)) {
            $files = glob($hapd_upload_dir . '/*');
            echo "✅ hearing-aid-products directory exists with " . count($files) . " files\n";
        } else {
            echo "❌ hearing-aid-products directory does not exist\n";
        }
        
        echo "\n";
    }
    
    /**
     * Check image sizes
     */
    private function check_image_sizes() {
        echo "<h3>4. Image Sizes Check</h3>\n";
        
        global $_wp_additional_image_sizes;
        
        $required_sizes = array(
            'hapd-product-thumbnail' => array(300, 300),
            'hapd-product-medium' => array(600, 600),
            'hapd-product-large' => array(1200, 1200),
            'hapd-product-gallery' => array(800, 800)
        );
        
        foreach ($required_sizes as $size_name => $expected_size) {
            if (isset($_wp_additional_image_sizes[$size_name])) {
                $actual_size = $_wp_additional_image_sizes[$size_name];
                echo "✅ $size_name: {$actual_size['width']}x{$actual_size['height']} (crop: " . ($actual_size['crop'] ? 'yes' : 'no') . ")\n";
            } else {
                echo "❌ $size_name: Not registered\n";
            }
        }
        
        echo "\n";
    }
    
    /**
     * Check frontend display
     */
    private function check_frontend_display() {
        echo "<h3>5. Frontend Display Check</h3>\n";
        
        // Check if frontend class exists
        if (class_exists('HAPD_Frontend')) {
            echo "✅ HAPD_Frontend class is loaded\n";
        } else {
            echo "❌ HAPD_Frontend class is NOT loaded\n";
        }
        
        // Check if blocks are registered
        if (class_exists('HAPD_Blocks')) {
            echo "✅ HAPD_Blocks class is loaded\n";
        } else {
            echo "❌ HAPD_Blocks class is NOT loaded\n";
        }
        
        // Check shortcodes
        if (shortcode_exists('hearing_aid_products')) {
            echo "✅ [hearing_aid_products] shortcode is registered\n";
        } else {
            echo "❌ [hearing_aid_products] shortcode is NOT registered\n";
        }
        
        // Check CSS and JS files
        $plugin_url = plugin_dir_url(dirname(__FILE__));
        $css_file = $plugin_url . 'assets/css/frontend.css';
        $js_file = $plugin_url . 'assets/js/frontend.js';
        
        echo "Frontend CSS: $css_file\n";
        echo "Frontend JS: $js_file\n";
        
        echo "\n";
    }
    
    /**
     * Provide solutions
     */
    private function provide_solutions() {
        echo "<h3>6. Common Solutions</h3>\n";
        
        echo "<strong>If images are not displaying:</strong>\n";
        echo "1. Check that images were properly imported to WordPress media library\n";
        echo "2. Verify image URLs in Column C of Excel file were accessible\n";
        echo "3. Ensure local image files were in /wp-content/uploads/hearing-aid-products/\n";
        echo "4. Check file permissions on uploads directory\n";
        echo "5. Regenerate thumbnails using a plugin like 'Regenerate Thumbnails'\n";
        echo "\n";
        
        echo "<strong>If products are not displaying on frontend:</strong>\n";
        echo "1. Check that products are published (not draft status)\n";
        echo "2. Clear any caching plugins\n";
        echo "3. Check theme compatibility\n";
        echo "4. Verify shortcode usage: [hearing_aid_products]\n";
        echo "5. Check Gutenberg block: 'Hearing Aid Products'\n";
        echo "\n";
        
        echo "<strong>If import seemed to fail:</strong>\n";
        echo "1. Check Excel file format (must be .xlsx or .xls)\n";
        echo "2. Verify Column B (Model No.) has data for each row\n";
        echo "3. Check server upload limits and memory\n";
        echo "4. Look for error messages in import results\n";
        echo "5. Try importing a smaller test file first\n";
        echo "\n";
    }
    
    /**
     * Fix common issues
     */
    public function fix_common_issues() {
        echo "<h3>7. Automatic Fixes</h3>\n";
        
        // Fix 1: Ensure image sizes are registered
        if (function_exists('add_image_size')) {
            add_image_size('hapd-product-thumbnail', 300, 300, true);
            add_image_size('hapd-product-medium', 600, 600, true);
            add_image_size('hapd-product-large', 1200, 1200, true);
            add_image_size('hapd-product-gallery', 800, 800, true);
            echo "✅ Re-registered image sizes\n";
        }
        
        // Fix 2: Create uploads directory if missing
        $upload_dir = wp_upload_dir();
        $hapd_upload_dir = $upload_dir['basedir'] . '/hearing-aid-products';
        
        if (!file_exists($hapd_upload_dir)) {
            if (wp_mkdir_p($hapd_upload_dir)) {
                echo "✅ Created hearing-aid-products directory\n";
            } else {
                echo "❌ Failed to create hearing-aid-products directory\n";
            }
        }
        
        // Fix 3: Check and fix product post status
        $draft_products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => 'draft',
            'numberposts' => -1
        ));
        
        if (!empty($draft_products)) {
            echo "⚠️  Found " . count($draft_products) . " products in draft status\n";
            echo "   Consider publishing them to make them visible on frontend\n";
        }
        
        echo "\n";
    }
}

// Run the diagnostic if accessed directly
if (isset($_GET['run_diagnostic']) || (defined('WP_CLI') && WP_CLI)) {
    $diagnostic = new HAPD_Product_Diagnostic();
    $diagnostic->run_diagnostic();
    
    if (isset($_GET['fix_issues'])) {
        $diagnostic->fix_common_issues();
    }
} else {
    // Show diagnostic options
    echo "<h1>Hearing Aid Product Display - Import Diagnostic</h1>\n";
    echo "<p>This diagnostic will help identify issues with imported products and their images.</p>\n";
    echo "<p><a href='?run_diagnostic=1' class='button button-primary'>Run Diagnostic</a></p>\n";
    echo "<p><a href='?run_diagnostic=1&fix_issues=1' class='button'>Run Diagnostic + Auto Fix</a></p>\n";
    echo "<hr>\n";
    echo "<h2>What This Diagnostic Checks</h2>\n";
    echo "<ul>\n";
    echo "<li><strong>Plugin Status:</strong> Verifies custom post types and image sizes are registered</li>\n";
    echo "<li><strong>Imported Products:</strong> Lists recent products and their metadata</li>\n";
    echo "<li><strong>Product Images:</strong> Checks featured images and file existence</li>\n";
    echo "<li><strong>Image Sizes:</strong> Verifies custom image sizes are properly registered</li>\n";
    echo "<li><strong>Frontend Display:</strong> Checks classes, shortcodes, and assets</li>\n";
    echo "<li><strong>Common Solutions:</strong> Provides troubleshooting steps</li>\n";
    echo "</ul>\n";
}
?>
