<?php
/**
 * Blocks Class
 *
 * @package HearingAidProductDisplay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Blocks Class
 */
class HAPD_Blocks {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'register_blocks'));
        add_action('enqueue_block_editor_assets', array($this, 'enqueue_block_editor_assets'));
        add_filter('block_categories_all', array($this, 'add_block_category'), 10, 2);
    }
    
    /**
     * Register blocks
     */
    public function register_blocks() {
        if (!function_exists('register_block_type')) {
            return;
        }
        
        // Register the main products block
        register_block_type('hapd/hearing-aid-products', array(
            'editor_script' => 'hapd-blocks',
            'editor_style' => 'hapd-blocks-editor',
            'style' => 'hapd-frontend',
            'render_callback' => array($this, 'render_products_block'),
            'attributes' => array(
                'selectedProducts' => array(
                    'type' => 'array',
                    'default' => array(),
                ),
                'layout' => array(
                    'type' => 'string',
                    'default' => 'grid',
                ),
                'columns' => array(
                    'type' => 'number',
                    'default' => 3,
                ),
                'showPrice' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'showSpecs' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'showFeatures' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'showExcerpt' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'categoryFilter' => array(
                    'type' => 'string',
                    'default' => '',
                ),
                'brandFilter' => array(
                    'type' => 'string',
                    'default' => '',
                ),
                'orderBy' => array(
                    'type' => 'string',
                    'default' => 'date',
                ),
                'order' => array(
                    'type' => 'string',
                    'default' => 'DESC',
                ),
                'productsPerPage' => array(
                    'type' => 'number',
                    'default' => -1,
                ),
            ),
        ));
        
        // Register single product block
        register_block_type('hapd/single-hearing-aid-product', array(
            'editor_script' => 'hapd-blocks',
            'editor_style' => 'hapd-blocks-editor',
            'style' => 'hapd-frontend',
            'render_callback' => array($this, 'render_single_product_block'),
            'attributes' => array(
                'productId' => array(
                    'type' => 'number',
                    'default' => 0,
                ),
                'showGallery' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'showPrice' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'showSpecs' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'showFeatures' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'showContact' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
            ),
        ));
    }
    
    /**
     * Enqueue block editor assets
     */
    public function enqueue_block_editor_assets() {
        wp_enqueue_script(
            'hapd-blocks',
            HAPD_PLUGIN_URL . 'assets/js/blocks.js',
            array(
                'wp-blocks',
                'wp-element',
                'wp-editor',
                'wp-components',
                'wp-i18n',
                'wp-data',
                'wp-api-fetch'
            ),
            HAPD_VERSION,
            true
        );
        
        wp_enqueue_style(
            'hapd-blocks-editor',
            HAPD_PLUGIN_URL . 'assets/css/blocks-editor.css',
            array('wp-edit-blocks'),
            HAPD_VERSION
        );
        
        // Localize script with data
        wp_localize_script('hapd-blocks', 'hapdBlocks', array(
            'apiUrl' => rest_url('wp/v2/'),
            'nonce' => wp_create_nonce('wp_rest'),
            'pluginUrl' => HAPD_PLUGIN_URL,
            'strings' => array(
                'selectProducts' => __('Select Products', 'hearing-aid-display'),
                'noProducts' => __('No products found', 'hearing-aid-display'),
                'loading' => __('Loading...', 'hearing-aid-display'),
                'error' => __('Error loading products', 'hearing-aid-display'),
            ),
        ));
    }
    
    /**
     * Add custom block category
     */
    public function add_block_category($categories, $post) {
        return array_merge(
            $categories,
            array(
                array(
                    'slug' => 'hearing-aid-products',
                    'title' => __('Hearing Aid Products', 'hearing-aid-display'),
                    'icon' => 'admin-generic',
                ),
            )
        );
    }
    
    /**
     * Render products block
     */
    public function render_products_block($attributes) {
        $defaults = array(
            'selectedProducts' => array(),
            'layout' => 'grid',
            'columns' => 3,
            'showPrice' => true,
            'showSpecs' => true,
            'showFeatures' => true,
            'showExcerpt' => true,
            'categoryFilter' => '',
            'brandFilter' => '',
            'orderBy' => 'date',
            'order' => 'DESC',
            'productsPerPage' => -1,
        );
        
        $attributes = wp_parse_args($attributes, $defaults);
        
        // Sanitize attributes
        $attributes['layout'] = sanitize_text_field($attributes['layout']);
        $attributes['columns'] = absint($attributes['columns']);
        $attributes['showPrice'] = (bool) $attributes['showPrice'];
        $attributes['showSpecs'] = (bool) $attributes['showSpecs'];
        $attributes['showFeatures'] = (bool) $attributes['showFeatures'];
        $attributes['showExcerpt'] = (bool) $attributes['showExcerpt'];
        $attributes['categoryFilter'] = sanitize_text_field($attributes['categoryFilter']);
        $attributes['brandFilter'] = sanitize_text_field($attributes['brandFilter']);
        $attributes['orderBy'] = sanitize_text_field($attributes['orderBy']);
        $attributes['order'] = sanitize_text_field($attributes['order']);
        $attributes['productsPerPage'] = intval($attributes['productsPerPage']);
        
        // Get frontend class
        if (!class_exists('HAPD_Frontend')) {
            require_once HAPD_PLUGIN_DIR . 'includes/class-frontend.php';
        }
        
        $frontend = new HAPD_Frontend();
        return $frontend->render_products_block($attributes);
    }
    
    /**
     * Render single product block
     */
    public function render_single_product_block($attributes) {
        $defaults = array(
            'productId' => 0,
            'showGallery' => true,
            'showPrice' => true,
            'showSpecs' => true,
            'showFeatures' => true,
            'showContact' => true,
        );
        
        $attributes = wp_parse_args($attributes, $defaults);
        
        // Sanitize attributes
        $attributes['productId'] = absint($attributes['productId']);
        $attributes['showGallery'] = (bool) $attributes['showGallery'];
        $attributes['showPrice'] = (bool) $attributes['showPrice'];
        $attributes['showSpecs'] = (bool) $attributes['showSpecs'];
        $attributes['showFeatures'] = (bool) $attributes['showFeatures'];
        $attributes['showContact'] = (bool) $attributes['showContact'];
        
        if (!$attributes['productId']) {
            return '<div class="hapd-error">' . __('Please select a product to display.', 'hearing-aid-display') . '</div>';
        }
        
        // Check if product exists and is published
        $product = get_post($attributes['productId']);
        if (!$product || $product->post_type !== 'hearing_aid_product' || $product->post_status !== 'publish') {
            return '<div class="hapd-error">' . __('Product not found or not published.', 'hearing-aid-display') . '</div>';
        }
        
        // Get frontend class
        if (!class_exists('HAPD_Frontend')) {
            require_once HAPD_PLUGIN_DIR . 'includes/class-frontend.php';
        }
        
        $frontend = new HAPD_Frontend();
        return $frontend->render_single_product($attributes['productId'], $attributes);
    }
    
    /**
     * Get products for block editor
     */
    public function get_products_for_editor($request) {
        $args = array(
            'post_type' => 'hearing_aid_product',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC',
        );
        
        $products = get_posts($args);
        $formatted_products = array();
        
        foreach ($products as $product) {
            $formatted_products[] = array(
                'id' => $product->ID,
                'title' => $product->post_title,
                'excerpt' => $product->post_excerpt,
                'thumbnail' => get_the_post_thumbnail_url($product->ID, 'thumbnail'),
                'price' => get_post_meta($product->ID, '_hapd_price', true),
                'edit_link' => get_edit_post_link($product->ID),
            );
        }
        
        return rest_ensure_response($formatted_products);
    }
    
    /**
     * Register REST API endpoints for block editor
     */
    public function register_rest_routes() {
        register_rest_route('hapd/v1', '/products', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_products_for_editor'),
            'permission_callback' => function() {
                return current_user_can('edit_posts');
            },
        ));
        
        register_rest_route('hapd/v1', '/product/(?P<id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_single_product_for_editor'),
            'permission_callback' => function() {
                return current_user_can('edit_posts');
            },
            'args' => array(
                'id' => array(
                    'validate_callback' => function($param, $request, $key) {
                        return is_numeric($param);
                    }
                ),
            ),
        ));
    }
    
    /**
     * Get single product for block editor
     */
    public function get_single_product_for_editor($request) {
        $product_id = $request['id'];
        $product = get_post($product_id);
        
        if (!$product || $product->post_type !== 'hearing_aid_product') {
            return new WP_Error('product_not_found', 'Product not found', array('status' => 404));
        }
        
        $formatted_product = array(
            'id' => $product->ID,
            'title' => $product->post_title,
            'content' => $product->post_content,
            'excerpt' => $product->post_excerpt,
            'thumbnail' => get_the_post_thumbnail_url($product->ID, 'medium'),
            'gallery' => get_post_meta($product->ID, '_hapd_gallery_ids', true),
            'price' => get_post_meta($product->ID, '_hapd_price', true),
            'model_number' => get_post_meta($product->ID, '_hapd_model_number', true),
            'warranty' => get_post_meta($product->ID, '_hapd_warranty', true),
            'availability' => get_post_meta($product->ID, '_hapd_availability', true),
            'features' => get_post_meta($product->ID, '_hapd_features', true),
            'specifications' => get_post_meta($product->ID, '_hapd_specifications', true),
            'categories' => wp_get_post_terms($product->ID, 'hearing_aid_category', array('fields' => 'names')),
            'brands' => wp_get_post_terms($product->ID, 'hearing_aid_brand', array('fields' => 'names')),
            'edit_link' => get_edit_post_link($product->ID),
        );
        
        return rest_ensure_response($formatted_product);
    }
    
    /**
     * Add block patterns
     */
    public function register_block_patterns() {
        if (function_exists('register_block_pattern')) {
            // Featured products pattern
            register_block_pattern(
                'hapd/featured-products',
                array(
                    'title' => __('Featured Hearing Aid Products', 'hearing-aid-display'),
                    'description' => __('A grid layout showcasing featured hearing aid products.', 'hearing-aid-display'),
                    'content' => '<!-- wp:heading {"level":2} -->
<h2>' . __('Featured Products', 'hearing-aid-display') . '</h2>
<!-- /wp:heading -->

<!-- wp:hapd/hearing-aid-products {"layout":"grid","columns":3,"showPrice":true,"showFeatures":true} /-->',
                    'categories' => array('hearing-aid-products'),
                    'keywords' => array('hearing aid', 'products', 'featured'),
                )
            );
            
            // Product comparison pattern
            register_block_pattern(
                'hapd/product-comparison',
                array(
                    'title' => __('Product Comparison', 'hearing-aid-display'),
                    'description' => __('Compare hearing aid products side by side.', 'hearing-aid-display'),
                    'content' => '<!-- wp:heading {"level":2} -->
<h2>' . __('Compare Products', 'hearing-aid-display') . '</h2>
<!-- /wp:heading -->

<!-- wp:hapd/hearing-aid-products {"layout":"list","showPrice":true,"showSpecs":true,"showFeatures":true} /-->',
                    'categories' => array('hearing-aid-products'),
                    'keywords' => array('hearing aid', 'comparison', 'specs'),
                )
            );
        }
    }
}

// Initialize the blocks class
new HAPD_Blocks();
