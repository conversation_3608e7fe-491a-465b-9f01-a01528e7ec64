<?php
/**
 * CSV Import Diagnostic Tool
 * 
 * This script helps diagnose CSV import issues
 * Run this by accessing it directly in your browser
 */

// WordPress Bootstrap
require_once('../../../wp-config.php');

// Set content type
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>CSV Import Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 5px; }
        .success { color: #008000; }
        .error { color: #cc0000; }
        .warning { color: #ff8800; }
        .info { color: #0073aa; }
        pre { background: #f9f9f9; padding: 10px; border-left: 4px solid #0073aa; overflow-x: auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h2 { color: #23282d; border-bottom: 2px solid #0073aa; padding-bottom: 5px; }
        h3 { color: #32373c; }
        .fix-button { background: #0073aa; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px 0; }
        .fix-button:hover { background: #005a87; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .download-link { background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }
        .download-link:hover { background: #218838; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 CSV Import Diagnostic Tool</h1>
        
        <?php
        echo "<div class='section'>";
        echo "<h2>1. Plugin and CSV Import Status</h2>";
        
        // Check if plugin is active
        if (is_plugin_active('hearing-aid-product-display/hearing-aid-product-display.php')) {
            echo "<p class='success'>✅ Plugin is active</p>";
        } else {
            echo "<p class='error'>❌ Plugin is NOT active</p>";
        }
        
        // Check if admin class exists
        if (class_exists('HAPD_Admin')) {
            echo "<p class='success'>✅ HAPD_Admin class is loaded</p>";
        } else {
            echo "<p class='error'>❌ HAPD_Admin class is NOT loaded</p>";
        }
        
        // Check if import/export page exists
        $import_export_url = admin_url('admin.php?page=hearing-aid-import-export');
        echo "<p class='info'>📍 Import/Export page: <a href='$import_export_url' target='_blank'>$import_export_url</a></p>";
        
        echo "</div>";
        
        echo "<div class='section'>";
        echo "<h2>2. CSV Import Form Check</h2>";
        
        // Check if we can access the import form
        echo "<p>The CSV import form should be available at: <strong>WordPress Admin → Hearing Aids → Import/Export</strong></p>";
        echo "<p>Look for the 'Import from CSV' section with the following fields:</p>";
        echo "<ul>";
        echo "<li>File input accepting .csv files</li>";
        echo "<li>'Import CSV' button</li>";
        echo "<li>Required CSV columns list</li>";
        echo "</ul>";
        
        echo "</div>";
        
        echo "<div class='section'>";
        echo "<h2>3. CSV Format Requirements</h2>";
        
        echo "<p>Your CSV file must have these exact column headers (first row):</p>";
        echo "<table>";
        echo "<tr><th>Column</th><th>Header Name</th><th>Required</th><th>Example</th></tr>";
        echo "<tr><td>1</td><td>title</td><td class='error'>Yes</td><td>Advanced Hearing Aid</td></tr>";
        echo "<tr><td>2</td><td>description</td><td>No</td><td>Digital hearing aid with noise reduction</td></tr>";
        echo "<tr><td>3</td><td>excerpt</td><td>No</td><td>Short product summary</td></tr>";
        echo "<tr><td>4</td><td>price</td><td>No</td><td>1299.99</td></tr>";
        echo "<tr><td>5</td><td>model_number</td><td>No</td><td>HA-2000</td></tr>";
        echo "<tr><td>6</td><td>warranty</td><td>No</td><td>2 years</td></tr>";
        echo "<tr><td>7</td><td>availability</td><td>No</td><td>in_stock</td></tr>";
        echo "<tr><td>8</td><td>features</td><td>No</td><td>Noise reduction|Bluetooth|Rechargeable</td></tr>";
        echo "<tr><td>9</td><td>categories</td><td>No</td><td>Digital|Behind-the-ear</td></tr>";
        echo "<tr><td>10</td><td>brands</td><td>No</td><td>Premium|Professional</td></tr>";
        echo "</table>";
        
        echo "<p class='warning'>⚠️ <strong>Important:</strong> The 'title' column is required. All others are optional.</p>";
        echo "<p class='info'>💡 <strong>Tip:</strong> Use the pipe symbol (|) to separate multiple values in features, categories, and brands.</p>";
        
        echo "</div>";
        
        // Generate sample CSV
        if (isset($_GET['generate_csv'])) {
            echo "<div class='section'>";
            echo "<h2>4. Generating Sample CSV File</h2>";
            
            $csv_content = "title,description,excerpt,price,model_number,warranty,availability,features,categories,brands\n";
            $csv_content .= "\"Advanced Digital Hearing Aid\",\"High-quality digital hearing aid with advanced noise reduction technology\",\"Premium digital hearing aid\",1299.99,HA-2000,\"2 years\",in_stock,\"Noise reduction|Bluetooth connectivity|Rechargeable battery\",\"Digital|Premium\",\"TechHear|Professional\"\n";
            $csv_content .= "\"Behind-the-Ear Model\",\"Comfortable behind-the-ear hearing aid suitable for mild to severe hearing loss\",\"BTE hearing aid\",899.99,BTE-Pro,\"1 year\",in_stock,\"Adjustable volume|Water resistant|Long battery life\",\"Behind-the-ear|Standard\",\"ComfortHear|Reliable\"\n";
            $csv_content .= "\"In-the-Canal Compact\",\"Discreet in-the-canal hearing aid with natural sound quality\",\"Compact ITC model\",599.99,ITC-Mini,\"1 year\",in_stock,\"Compact design|Natural sound|Easy controls\",\"In-the-canal|Compact\",\"DiscreetHear|Compact\"\n";
            
            $filename = 'sample-hearing-aid-products.csv';
            $filepath = HAPD_PLUGIN_DIR . $filename;
            
            if (file_put_contents($filepath, $csv_content)) {
                echo "<p class='success'>✅ Sample CSV file generated successfully!</p>";
                echo "<p><a href='" . HAPD_PLUGIN_URL . $filename . "' class='download-link' download>📥 Download Sample CSV File</a></p>";
                echo "<p class='info'>This file contains 3 sample products with all required and optional fields filled in.</p>";
            } else {
                echo "<p class='error'>❌ Failed to generate sample CSV file</p>";
            }
            
            echo "</div>";
        }
        
        echo "<div class='section'>";
        echo "<h2>5. Common CSV Import Issues</h2>";
        
        echo "<h3>Issue 1: File Not Uploading</h3>";
        echo "<ul>";
        echo "<li><strong>Cause:</strong> File size too large or wrong format</li>";
        echo "<li><strong>Solution:</strong> Ensure file is .csv format and under WordPress upload limit</li>";
        echo "</ul>";
        
        echo "<h3>Issue 2: No Products Created</h3>";
        echo "<ul>";
        echo "<li><strong>Cause:</strong> Missing 'title' column or incorrect headers</li>";
        echo "<li><strong>Solution:</strong> Verify CSV headers match exactly (case-sensitive)</li>";
        echo "</ul>";
        
        echo "<h3>Issue 3: Products Created but Not Visible</h3>";
        echo "<ul>";
        echo "<li><strong>Cause:</strong> Products imported as 'draft' status</li>";
        echo "<li><strong>Solution:</strong> Go to Hearing Aids → All Products and publish them</li>";
        echo "</ul>";
        
        echo "<h3>Issue 4: Import Process Fails</h3>";
        echo "<ul>";
        echo "<li><strong>Cause:</strong> PHP errors or memory limits</li>";
        echo "<li><strong>Solution:</strong> Check WordPress error logs and increase memory limit</li>";
        echo "</ul>";
        
        echo "</div>";
        
        echo "<div class='section'>";
        echo "<h2>6. Testing Steps</h2>";
        
        echo "<ol>";
        echo "<li><strong>Generate Sample CSV:</strong> <a href='?generate_csv=1' class='fix-button'>Generate Sample CSV</a></li>";
        echo "<li><strong>Download the sample file</strong> and open it to see the correct format</li>";
        echo "<li><strong>Go to WordPress Admin → Hearing Aids → Import/Export</strong></li>";
        echo "<li><strong>Find the 'Import from CSV' section</strong></li>";
        echo "<li><strong>Upload the sample CSV file</strong></li>";
        echo "<li><strong>Click 'Import CSV'</strong> and wait for completion</li>";
        echo "<li><strong>Check Hearing Aids → All Products</strong> for imported products</li>";
        echo "<li><strong>If products are in 'Draft' status, publish them</strong></li>";
        echo "<li><strong>Test frontend display</strong> with [hearing_aid_products] shortcode</li>";
        echo "</ol>";
        
        echo "</div>";
        
        echo "<div class='section'>";
        echo "<h2>7. Alternative: Excel Import</h2>";
        
        echo "<p>If CSV import continues to have issues, consider using the Excel import feature instead:</p>";
        echo "<ul>";
        echo "<li><strong>More robust:</strong> Better error handling and progress tracking</li>";
        echo "<li><strong>Image support:</strong> Can import images from URLs or local files</li>";
        echo "<li><strong>Larger files:</strong> Supports files up to 300MB</li>";
        echo "<li><strong>Better feedback:</strong> Shows detailed import progress and results</li>";
        echo "</ul>";
        
        echo "<p>Excel format requires 9 columns: No., Model No., Photo, Packaging, Accessories, Description, Specification, Measurement, Price</p>";
        
        echo "</div>";
        
        // Check for existing products
        $products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => array('publish', 'draft'),
            'numberposts' => 5,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        if (!empty($products)) {
            echo "<div class='section'>";
            echo "<h2>8. Recent Products in Database</h2>";
            
            echo "<table>";
            echo "<tr><th>ID</th><th>Title</th><th>Status</th><th>Date</th></tr>";
            
            foreach ($products as $product) {
                $status_class = $product->post_status == 'publish' ? 'success' : 'warning';
                echo "<tr>";
                echo "<td>{$product->ID}</td>";
                echo "<td>" . esc_html($product->post_title) . "</td>";
                echo "<td class='$status_class'>{$product->post_status}</td>";
                echo "<td>{$product->post_date}</td>";
                echo "</tr>";
            }
            
            echo "</table>";
            
            if (count(array_filter($products, function($p) { return $p->post_status == 'draft'; })) > 0) {
                echo "<p class='warning'>⚠️ Some products are in draft status. <a href='" . admin_url('edit.php?post_type=hearing_aid_product') . "' target='_blank'>Publish them here</a></p>";
            }
            
            echo "</div>";
        }
        ?>
        
        <div class="section">
            <h2>9. Next Steps</h2>
            <p>If CSV import is still not working:</p>
            <ol>
                <li><strong>Check WordPress error logs</strong> for PHP errors</li>
                <li><strong>Verify file permissions</strong> on uploads directory</li>
                <li><strong>Test with the sample CSV file</strong> generated above</li>
                <li><strong>Try Excel import instead</strong> for better reliability</li>
                <li><strong>Contact support</strong> with specific error messages</li>
            </ol>
        </div>
    </div>
</body>
</html>
