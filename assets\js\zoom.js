/**
 * Simple Image Zoom Library
 * Lightweight zoom functionality for product images
 */

(function($) {
    'use strict';
    
    /**
     * Simple Zoom Plugin
     */
    $.fn.zoom = function(options) {
        const defaults = {
            magnify: 1.5,
            touch: false,
            callback: null
        };
        
        const settings = $.extend({}, defaults, options);
        
        return this.each(function() {
            const $img = $(this);
            const $container = $img.parent();
            
            // Skip if already initialized
            if ($img.data('zoom-initialized')) {
                return;
            }
            
            $img.data('zoom-initialized', true);
            
            // Get zoom image URL
            const zoomImageUrl = settings.url || $img.data('zoom-image') || $img.attr('src');
            
            if (!zoomImageUrl) {
                return;
            }
            
            // Create zoom container
            const $zoomContainer = $('<div class="hapd-zoom-container"></div>');
            const $zoomImage = $('<img class="hapd-zoom-image" src="' + zoomImageUrl + '" alt="' + ($img.attr('alt') || '') + '">');
            
            $zoomContainer.append($zoomImage);
            $container.append($zoomContainer);
            
            // Position zoom container
            $zoomContainer.css({
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                overflow: 'hidden',
                pointerEvents: 'none',
                opacity: 0,
                transition: 'opacity 0.3s ease',
                zIndex: 10
            });
            
            // Style zoom image
            $zoomImage.css({
                position: 'absolute',
                maxWidth: 'none',
                maxHeight: 'none',
                width: ($img.width() * settings.magnify) + 'px',
                height: ($img.height() * settings.magnify) + 'px'
            });
            
            // Mouse events
            if (!settings.touch || !('ontouchstart' in window)) {
                $img.on('mouseenter', function() {
                    $zoomContainer.css('opacity', 1);
                    $img.css('cursor', 'zoom-in');
                });
                
                $img.on('mouseleave', function() {
                    $zoomContainer.css('opacity', 0);
                    $img.css('cursor', 'default');
                });
                
                $img.on('mousemove', function(e) {
                    const offset = $img.offset();
                    const x = e.pageX - offset.left;
                    const y = e.pageY - offset.top;
                    
                    const xPercent = (x / $img.width()) * 100;
                    const yPercent = (y / $img.height()) * 100;
                    
                    const moveX = -(($zoomImage.width() - $img.width()) * (xPercent / 100));
                    const moveY = -(($zoomImage.height() - $img.height()) * (yPercent / 100));
                    
                    $zoomImage.css({
                        left: moveX + 'px',
                        top: moveY + 'px'
                    });
                });
            }
            
            // Touch events for mobile
            if (settings.touch && 'ontouchstart' in window) {
                let isZoomed = false;
                
                $img.on('touchstart', function(e) {
                    e.preventDefault();
                    
                    if (!isZoomed) {
                        $zoomContainer.css('opacity', 1);
                        isZoomed = true;
                    } else {
                        $zoomContainer.css('opacity', 0);
                        isZoomed = false;
                    }
                });
                
                $img.on('touchmove', function(e) {
                    if (!isZoomed) return;
                    
                    e.preventDefault();
                    const touch = e.originalEvent.touches[0];
                    const offset = $img.offset();
                    const x = touch.pageX - offset.left;
                    const y = touch.pageY - offset.top;
                    
                    const xPercent = (x / $img.width()) * 100;
                    const yPercent = (y / $img.height()) * 100;
                    
                    const moveX = -(($zoomImage.width() - $img.width()) * (xPercent / 100));
                    const moveY = -(($zoomImage.height() - $img.height()) * (yPercent / 100));
                    
                    $zoomImage.css({
                        left: moveX + 'px',
                        top: moveY + 'px'
                    });
                });
            }
            
            // Keyboard support
            $img.attr('tabindex', '0').on('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    
                    if ($zoomContainer.css('opacity') == 0) {
                        $zoomContainer.css('opacity', 1);
                        $img.css('cursor', 'zoom-out');
                        
                        // Announce to screen readers
                        const announcement = 'Image zoomed in. Use arrow keys to navigate or press Escape to zoom out.';
                        announceToScreenReader(announcement);
                    } else {
                        $zoomContainer.css('opacity', 0);
                        $img.css('cursor', 'zoom-in');
                        
                        announceToScreenReader('Image zoomed out');
                    }
                } else if (e.key === 'Escape') {
                    $zoomContainer.css('opacity', 0);
                    $img.css('cursor', 'zoom-in');
                    announceToScreenReader('Image zoomed out');
                } else if ($zoomContainer.css('opacity') == 1) {
                    // Arrow key navigation when zoomed
                    let moveX = parseInt($zoomImage.css('left')) || 0;
                    let moveY = parseInt($zoomImage.css('top')) || 0;
                    const step = 20;
                    
                    switch (e.key) {
                        case 'ArrowLeft':
                            e.preventDefault();
                            moveX = Math.min(0, moveX + step);
                            break;
                        case 'ArrowRight':
                            e.preventDefault();
                            moveX = Math.max(-(($zoomImage.width() - $img.width())), moveX - step);
                            break;
                        case 'ArrowUp':
                            e.preventDefault();
                            moveY = Math.min(0, moveY + step);
                            break;
                        case 'ArrowDown':
                            e.preventDefault();
                            moveY = Math.max(-(($zoomImage.height() - $img.height())), moveY - step);
                            break;
                    }
                    
                    $zoomImage.css({
                        left: moveX + 'px',
                        top: moveY + 'px'
                    });
                }
            });
            
            // Handle window resize
            $(window).on('resize', debounce(function() {
                $zoomImage.css({
                    width: ($img.width() * settings.magnify) + 'px',
                    height: ($img.height() * settings.magnify) + 'px'
                });
            }, 250));
            
            // Callback
            if (typeof settings.callback === 'function') {
                settings.callback.call(this);
            }
        });
    };
    
    /**
     * Lightbox functionality
     */
    $.fn.lightbox = function(options) {
        const defaults = {
            closeOnClick: true,
            closeOnEscape: true,
            showNavigation: true,
            animationSpeed: 300
        };
        
        const settings = $.extend({}, defaults, options);
        
        return this.each(function() {
            const $trigger = $(this);
            
            $trigger.on('click', function(e) {
                e.preventDefault();
                
                const imageUrl = $trigger.attr('href') || $trigger.data('lightbox-image') || $trigger.attr('src');
                const caption = $trigger.attr('title') || $trigger.data('caption') || '';
                
                if (!imageUrl) return;
                
                // Create lightbox
                const $lightbox = $('<div class="hapd-lightbox"></div>');
                const $overlay = $('<div class="hapd-lightbox-overlay"></div>');
                const $container = $('<div class="hapd-lightbox-container"></div>');
                const $image = $('<img class="hapd-lightbox-image" src="' + imageUrl + '" alt="' + caption + '">');
                const $closeBtn = $('<button class="hapd-lightbox-close" aria-label="Close lightbox">×</button>');
                
                if (caption) {
                    const $caption = $('<div class="hapd-lightbox-caption">' + caption + '</div>');
                    $container.append($caption);
                }
                
                $container.append($image, $closeBtn);
                $lightbox.append($overlay, $container);
                $('body').append($lightbox);
                
                // Style lightbox
                $lightbox.css({
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    zIndex: 9999,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    opacity: 0,
                    transition: 'opacity ' + settings.animationSpeed + 'ms ease'
                });
                
                $overlay.css({
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    cursor: settings.closeOnClick ? 'pointer' : 'default'
                });
                
                $container.css({
                    position: 'relative',
                    maxWidth: '90%',
                    maxHeight: '90%',
                    backgroundColor: '#fff',
                    borderRadius: '8px',
                    overflow: 'hidden',
                    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)'
                });
                
                $image.css({
                    display: 'block',
                    maxWidth: '100%',
                    maxHeight: '80vh',
                    width: 'auto',
                    height: 'auto'
                });
                
                $closeBtn.css({
                    position: 'absolute',
                    top: '10px',
                    right: '10px',
                    width: '30px',
                    height: '30px',
                    border: 'none',
                    borderRadius: '50%',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    color: '#fff',
                    fontSize: '18px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                });
                
                // Show lightbox
                setTimeout(() => {
                    $lightbox.css('opacity', 1);
                }, 10);
                
                // Close functionality
                function closeLightbox() {
                    $lightbox.css('opacity', 0);
                    setTimeout(() => {
                        $lightbox.remove();
                        $('body').removeClass('hapd-lightbox-open');
                    }, settings.animationSpeed);
                }
                
                $closeBtn.on('click', closeLightbox);
                
                if (settings.closeOnClick) {
                    $overlay.on('click', closeLightbox);
                }
                
                if (settings.closeOnEscape) {
                    $(document).on('keydown.lightbox', function(e) {
                        if (e.key === 'Escape') {
                            closeLightbox();
                            $(document).off('keydown.lightbox');
                        }
                    });
                }
                
                // Prevent body scroll
                $('body').addClass('hapd-lightbox-open');
                
                // Focus management
                $closeBtn.focus();
                
                // Announce to screen readers
                announceToScreenReader('Image opened in lightbox. Press Escape to close.');
            });
        });
    };
    
    /**
     * Utility functions
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    function announceToScreenReader(text) {
        const $announcement = $('<div class="hapd-sr-only" aria-live="polite" aria-atomic="true"></div>');
        $announcement.text(text);
        $('body').append($announcement);
        
        setTimeout(() => {
            $announcement.remove();
        }, 1000);
    }
    
    // Auto-initialize zoom on images with data-zoom attribute
    $(document).ready(function() {
        $('img[data-zoom-image]').zoom();
        $('a[data-lightbox]').lightbox();
    });
    
})(jQuery);

// CSS for lightbox (injected via JavaScript)
(function() {
    const css = `
        .hapd-lightbox-open {
            overflow: hidden;
        }
        
        .hapd-lightbox-caption {
            padding: 15px;
            text-align: center;
            background: #f8f9fa;
            color: #333;
            font-size: 14px;
        }
        
        .hapd-lightbox-close:hover {
            background: rgba(0, 0, 0, 0.7) !important;
        }
        
        .hapd-lightbox-close:focus {
            outline: 2px solid #007cba;
            outline-offset: 2px;
        }
        
        @media (max-width: 768px) {
            .hapd-lightbox-container {
                max-width: 95% !important;
                max-height: 95% !important;
            }
            
            .hapd-lightbox-image {
                max-height: 70vh !important;
            }
        }
    `;
    
    const style = document.createElement('style');
    style.textContent = css;
    document.head.appendChild(style);
})();
