<?php
/**
 * Plugin Name: Hearing Aid Product Display
 * Plugin URI: https://volkena.com/plugins/hearing-aid-product-display
 * Description: A professional WordPress plugin for displaying hearing aid products with custom Gutenberg blocks, responsive layouts, and comprehensive product management.
 * Version: 1.1.1
 * Author: Völkena Development Team
 * Author URI: https://volkena.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: hearing-aid-display
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 *
 * @package HearingAidProductDisplay
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('HAPD_VERSION', '1.1.1');
define('HAPD_PLUGIN_FILE', __FILE__);
define('HAPD_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('HAPD_PLUGIN_URL', plugin_dir_url(__FILE__));
define('HAPD_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main Plugin Class
 */
class HearingAidProductDisplay {
    
    /**
     * Single instance of the plugin
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Activation and deactivation hooks
        register_activation_hook(HAPD_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(HAPD_PLUGIN_FILE, array($this, 'deactivate'));
        
        // Initialize plugin
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // Admin hooks
        add_action('admin_init', array($this, 'admin_init'));
        
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        
        // Gutenberg block registration
        add_action('init', array($this, 'register_blocks'));
        
        // AJAX handlers
        add_action('wp_ajax_hapd_get_products', array($this, 'ajax_get_products'));
        add_action('wp_ajax_nopriv_hapd_get_products', array($this, 'ajax_get_products'));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create custom post type
        $this->register_post_types();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Create database tables if needed
        $this->create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Log activation
        error_log('Hearing Aid Product Display plugin activated');
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Log deactivation
        error_log('Hearing Aid Product Display plugin deactivated');
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Register custom post types and taxonomies
        $this->register_post_types();
        $this->register_taxonomies();
        
        // Add image sizes
        $this->add_image_sizes();
        
        // Include required files
        $this->include_files();
    }
    
    /**
     * Load text domain for translations
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'hearing-aid-display',
            false,
            dirname(HAPD_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Admin initialization
     */
    public function admin_init() {
        // Register settings
        $this->register_settings();
        
        // Add meta boxes
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_meta_boxes'));
    }
    

    
    /**
     * Register custom post types
     */
    public function register_post_types() {
        $labels = array(
            'name'                  => __('Hearing Aid Products', 'hearing-aid-display'),
            'singular_name'         => __('Hearing Aid Product', 'hearing-aid-display'),
            'menu_name'            => __('Hearing Aid Products', 'hearing-aid-display'),
            'add_new'              => __('Add New Product', 'hearing-aid-display'),
            'add_new_item'         => __('Add New Hearing Aid Product', 'hearing-aid-display'),
            'edit_item'            => __('Edit Hearing Aid Product', 'hearing-aid-display'),
            'new_item'             => __('New Hearing Aid Product', 'hearing-aid-display'),
            'view_item'            => __('View Hearing Aid Product', 'hearing-aid-display'),
            'search_items'         => __('Search Hearing Aid Products', 'hearing-aid-display'),
            'not_found'            => __('No hearing aid products found', 'hearing-aid-display'),
            'not_found_in_trash'   => __('No hearing aid products found in trash', 'hearing-aid-display'),
        );
        
        $args = array(
            'labels'                => $labels,
            'public'                => true,
            'publicly_queryable'    => true,
            'show_ui'              => true,
            'show_in_menu'         => false, // We'll add it to our custom menu
            'show_in_rest'         => true,
            'query_var'            => true,
            'rewrite'              => array('slug' => 'hearing-aid-product'),
            'capability_type'      => 'post',
            'has_archive'          => true,
            'hierarchical'         => false,
            'menu_position'        => null,
            'supports'             => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
            'menu_icon'            => 'dashicons-admin-generic',
        );
        
        register_post_type('hearing_aid_product', $args);
    }
    
    /**
     * Register taxonomies
     */
    public function register_taxonomies() {
        // Product categories
        register_taxonomy(
            'hearing_aid_category',
            'hearing_aid_product',
            array(
                'labels' => array(
                    'name'              => __('Product Categories', 'hearing-aid-display'),
                    'singular_name'     => __('Product Category', 'hearing-aid-display'),
                    'search_items'      => __('Search Categories', 'hearing-aid-display'),
                    'all_items'         => __('All Categories', 'hearing-aid-display'),
                    'edit_item'         => __('Edit Category', 'hearing-aid-display'),
                    'update_item'       => __('Update Category', 'hearing-aid-display'),
                    'add_new_item'      => __('Add New Category', 'hearing-aid-display'),
                    'new_item_name'     => __('New Category Name', 'hearing-aid-display'),
                    'menu_name'         => __('Categories', 'hearing-aid-display'),
                ),
                'hierarchical'      => true,
                'show_ui'          => true,
                'show_admin_column' => true,
                'show_in_rest'     => true,
                'query_var'        => true,
                'rewrite'          => array('slug' => 'hearing-aid-category'),
            )
        );
        
        // Product brands
        register_taxonomy(
            'hearing_aid_brand',
            'hearing_aid_product',
            array(
                'labels' => array(
                    'name'              => __('Brands', 'hearing-aid-display'),
                    'singular_name'     => __('Brand', 'hearing-aid-display'),
                    'search_items'      => __('Search Brands', 'hearing-aid-display'),
                    'all_items'         => __('All Brands', 'hearing-aid-display'),
                    'edit_item'         => __('Edit Brand', 'hearing-aid-display'),
                    'update_item'       => __('Update Brand', 'hearing-aid-display'),
                    'add_new_item'      => __('Add New Brand', 'hearing-aid-display'),
                    'new_item_name'     => __('New Brand Name', 'hearing-aid-display'),
                    'menu_name'         => __('Brands', 'hearing-aid-display'),
                ),
                'hierarchical'      => false,
                'show_ui'          => true,
                'show_admin_column' => true,
                'show_in_rest'     => true,
                'query_var'        => true,
                'rewrite'          => array('slug' => 'hearing-aid-brand'),
            )
        );
    }
    
    /**
     * Add custom image sizes
     */
    public function add_image_sizes() {
        add_image_size('hapd-product-thumbnail', 300, 300, true);
        add_image_size('hapd-product-medium', 600, 600, true);
        add_image_size('hapd-product-large', 1200, 1200, true);
        add_image_size('hapd-product-gallery', 800, 800, true);
    }
    
    /**
     * Include required files
     */
    public function include_files() {
        // Include admin files
        if (is_admin()) {
            require_once HAPD_PLUGIN_DIR . 'includes/admin/class-admin.php';
            require_once HAPD_PLUGIN_DIR . 'includes/admin/class-meta-boxes.php';
            require_once HAPD_PLUGIN_DIR . 'includes/admin/class-excel-importer.php';
        }

        // Include frontend files
        require_once HAPD_PLUGIN_DIR . 'includes/class-frontend.php';
        require_once HAPD_PLUGIN_DIR . 'includes/class-blocks.php';
        require_once HAPD_PLUGIN_DIR . 'includes/class-ajax.php';
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Product specifications table
        $table_name = $wpdb->prefix . 'hapd_product_specs';
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            product_id bigint(20) NOT NULL,
            spec_name varchar(100) NOT NULL,
            spec_value text NOT NULL,
            spec_order int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY product_id (product_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Set default options
     */
    private function set_default_options() {
        $default_options = array(
            'currency_symbol' => '$',
            'currency_position' => 'before',
            'show_price' => true,
            'show_specifications' => true,
            'show_features' => true,
            'default_layout' => 'grid',
            'products_per_page' => 12,
            'enable_zoom' => true,
            'contact_email' => get_option('admin_email'),
            'contact_phone' => '',
            'contact_form_shortcode' => '',
        );
        
        add_option('hapd_settings', $default_options);
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('hapd_settings', 'hapd_settings', array($this, 'sanitize_settings'));
    }
    
    /**
     * Sanitize settings
     */
    public function sanitize_settings($input) {
        $sanitized = array();
        
        if (isset($input['currency_symbol'])) {
            $sanitized['currency_symbol'] = sanitize_text_field($input['currency_symbol']);
        }
        
        if (isset($input['currency_position'])) {
            $sanitized['currency_position'] = in_array($input['currency_position'], array('before', 'after')) 
                ? $input['currency_position'] : 'before';
        }
        
        if (isset($input['show_price'])) {
            $sanitized['show_price'] = (bool) $input['show_price'];
        }
        
        if (isset($input['show_specifications'])) {
            $sanitized['show_specifications'] = (bool) $input['show_specifications'];
        }
        
        if (isset($input['show_features'])) {
            $sanitized['show_features'] = (bool) $input['show_features'];
        }
        
        if (isset($input['default_layout'])) {
            $sanitized['default_layout'] = in_array($input['default_layout'], array('grid', 'list', 'carousel')) 
                ? $input['default_layout'] : 'grid';
        }
        
        if (isset($input['products_per_page'])) {
            $sanitized['products_per_page'] = absint($input['products_per_page']);
        }
        
        if (isset($input['enable_zoom'])) {
            $sanitized['enable_zoom'] = (bool) $input['enable_zoom'];
        }
        
        if (isset($input['contact_email'])) {
            $sanitized['contact_email'] = sanitize_email($input['contact_email']);
        }
        
        if (isset($input['contact_phone'])) {
            $sanitized['contact_phone'] = sanitize_text_field($input['contact_phone']);
        }
        
        if (isset($input['contact_form_shortcode'])) {
            $sanitized['contact_form_shortcode'] = sanitize_text_field($input['contact_form_shortcode']);
        }
        
        return $sanitized;
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        wp_enqueue_style(
            'hapd-frontend',
            HAPD_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            HAPD_VERSION
        );
        
        wp_enqueue_script(
            'hapd-frontend',
            HAPD_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            HAPD_VERSION,
            true
        );
        
        wp_localize_script('hapd-frontend', 'hapd_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('hapd_nonce'),
            'strings' => array(
                'loading' => __('Loading...', 'hearing-aid-display'),
                'error' => __('An error occurred. Please try again.', 'hearing-aid-display'),
                'contact_us' => __('Contact Us', 'hearing-aid-display'),
                'learn_more' => __('Learn More', 'hearing-aid-display'),
            )
        ));
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        global $post_type;
        
        if ($post_type === 'hearing_aid_product' || strpos($hook, 'hearing-aid') !== false) {
            wp_enqueue_style(
                'hapd-admin',
                HAPD_PLUGIN_URL . 'assets/css/admin.css',
                array(),
                HAPD_VERSION
            );
            
            wp_enqueue_script(
                'hapd-admin',
                HAPD_PLUGIN_URL . 'assets/js/admin.js',
                array('jquery', 'wp-color-picker'),
                HAPD_VERSION,
                true
            );
            
            wp_enqueue_style('wp-color-picker');
            wp_enqueue_media();
        }
    }
    
    /**
     * Register Gutenberg blocks
     */
    public function register_blocks() {
        if (function_exists('register_block_type')) {
            wp_register_script(
                'hapd-blocks',
                HAPD_PLUGIN_URL . 'assets/js/blocks.js',
                array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'),
                HAPD_VERSION,
                true
            );
            
            wp_register_style(
                'hapd-blocks-editor',
                HAPD_PLUGIN_URL . 'assets/css/blocks-editor.css',
                array('wp-edit-blocks'),
                HAPD_VERSION
            );
            
            register_block_type('hapd/hearing-aid-products', array(
                'editor_script' => 'hapd-blocks',
                'editor_style' => 'hapd-blocks-editor',
                'style' => 'hapd-frontend',
                'render_callback' => array($this, 'render_products_block'),
                'attributes' => array(
                    'selectedProducts' => array(
                        'type' => 'array',
                        'default' => array(),
                    ),
                    'layout' => array(
                        'type' => 'string',
                        'default' => 'grid',
                    ),
                    'columns' => array(
                        'type' => 'number',
                        'default' => 3,
                    ),
                    'showPrice' => array(
                        'type' => 'boolean',
                        'default' => true,
                    ),
                    'showSpecs' => array(
                        'type' => 'boolean',
                        'default' => true,
                    ),
                    'showFeatures' => array(
                        'type' => 'boolean',
                        'default' => true,
                    ),
                    'showExcerpt' => array(
                        'type' => 'boolean',
                        'default' => true,
                    ),
                ),
            ));
        }
    }
    
    /**
     * Render products block
     */
    public function render_products_block($attributes) {
        $defaults = array(
            'selectedProducts' => array(),
            'layout' => 'grid',
            'columns' => 3,
            'showPrice' => true,
            'showSpecs' => true,
            'showFeatures' => true,
            'showExcerpt' => true,
        );
        
        $attributes = wp_parse_args($attributes, $defaults);
        
        // Include the frontend rendering class
        if (!class_exists('HAPD_Frontend')) {
            require_once HAPD_PLUGIN_DIR . 'includes/class-frontend.php';
        }
        
        $frontend = new HAPD_Frontend();
        return $frontend->render_products_block($attributes);
    }
    
    /**
     * AJAX handler for getting products
     */
    public function ajax_get_products() {
        check_ajax_referer('hapd_nonce', 'nonce');
        
        $search = sanitize_text_field($_POST['search'] ?? '');
        $category = sanitize_text_field($_POST['category'] ?? '');
        $brand = sanitize_text_field($_POST['brand'] ?? '');
        
        $args = array(
            'post_type' => 'hearing_aid_product',
            'post_status' => 'publish',
            'posts_per_page' => -1,
        );
        
        if (!empty($search)) {
            $args['s'] = $search;
        }
        
        $tax_query = array();
        
        if (!empty($category)) {
            $tax_query[] = array(
                'taxonomy' => 'hearing_aid_category',
                'field' => 'slug',
                'terms' => $category,
            );
        }
        
        if (!empty($brand)) {
            $tax_query[] = array(
                'taxonomy' => 'hearing_aid_brand',
                'field' => 'slug',
                'terms' => $brand,
            );
        }
        
        if (!empty($tax_query)) {
            $args['tax_query'] = $tax_query;
        }
        
        $products = get_posts($args);
        $product_data = array();
        
        foreach ($products as $product) {
            $product_data[] = array(
                'id' => $product->ID,
                'title' => $product->post_title,
                'excerpt' => $product->post_excerpt,
                'thumbnail' => get_the_post_thumbnail_url($product->ID, 'hapd-product-thumbnail'),
                'price' => get_post_meta($product->ID, '_hapd_price', true),
            );
        }
        
        wp_send_json_success($product_data);
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        add_meta_box(
            'hapd-product-details',
            __('Product Details', 'hearing-aid-display'),
            array($this, 'product_details_meta_box'),
            'hearing_aid_product',
            'normal',
            'high'
        );
        
        add_meta_box(
            'hapd-product-specifications',
            __('Technical Specifications', 'hearing-aid-display'),
            array($this, 'product_specifications_meta_box'),
            'hearing_aid_product',
            'normal',
            'high'
        );
        
        add_meta_box(
            'hapd-product-features',
            __('Key Features', 'hearing-aid-display'),
            array($this, 'product_features_meta_box'),
            'hearing_aid_product',
            'normal',
            'high'
        );
        
        add_meta_box(
            'hapd-product-gallery',
            __('Product Gallery', 'hearing-aid-display'),
            array($this, 'product_gallery_meta_box'),
            'hearing_aid_product',
            'side',
            'default'
        );
    }
    
    /**
     * Product details meta box
     */
    public function product_details_meta_box($post) {
        wp_nonce_field('hapd_product_details', 'hapd_product_details_nonce');
        
        $price = get_post_meta($post->ID, '_hapd_price', true);
        $model_number = get_post_meta($post->ID, '_hapd_model_number', true);
        $warranty = get_post_meta($post->ID, '_hapd_warranty', true);
        $availability = get_post_meta($post->ID, '_hapd_availability', true);
        
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th><label for="hapd_price">' . __('Price', 'hearing-aid-display') . '</label></th>';
        echo '<td><input type="text" id="hapd_price" name="hapd_price" value="' . esc_attr($price) . '" class="regular-text" /></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="hapd_model_number">' . __('Model Number', 'hearing-aid-display') . '</label></th>';
        echo '<td><input type="text" id="hapd_model_number" name="hapd_model_number" value="' . esc_attr($model_number) . '" class="regular-text" /></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="hapd_warranty">' . __('Warranty', 'hearing-aid-display') . '</label></th>';
        echo '<td><input type="text" id="hapd_warranty" name="hapd_warranty" value="' . esc_attr($warranty) . '" class="regular-text" /></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th><label for="hapd_availability">' . __('Availability', 'hearing-aid-display') . '</label></th>';
        echo '<td>';
        echo '<select id="hapd_availability" name="hapd_availability">';
        echo '<option value="in_stock"' . selected($availability, 'in_stock', false) . '>' . __('In Stock', 'hearing-aid-display') . '</option>';
        echo '<option value="out_of_stock"' . selected($availability, 'out_of_stock', false) . '>' . __('Out of Stock', 'hearing-aid-display') . '</option>';
        echo '<option value="contact_for_availability"' . selected($availability, 'contact_for_availability', false) . '>' . __('Contact for Availability', 'hearing-aid-display') . '</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        echo '</table>';
    }
    
    /**
     * Product specifications meta box
     */
    public function product_specifications_meta_box($post) {
        wp_nonce_field('hapd_product_specs', 'hapd_product_specs_nonce');
        
        $specifications = get_post_meta($post->ID, '_hapd_specifications', true);
        if (!is_array($specifications)) {
            $specifications = array();
        }
        
        echo '<div id="hapd-specifications-container">';
        echo '<div class="hapd-specs-header">';
        echo '<button type="button" class="button hapd-add-spec">' . __('Add Specification', 'hearing-aid-display') . '</button>';
        echo '</div>';
        echo '<div class="hapd-specs-list">';
        
        if (!empty($specifications)) {
            foreach ($specifications as $index => $spec) {
                $this->render_specification_row($index, $spec);
            }
        } else {
            $this->render_specification_row(0, array('name' => '', 'value' => ''));
        }
        
        echo '</div>';
        echo '</div>';
        
        // Add JavaScript template
        echo '<script type="text/template" id="hapd-spec-template">';
        $this->render_specification_row('{{INDEX}}', array('name' => '', 'value' => ''));
        echo '</script>';
    }
    
    /**
     * Render specification row
     */
    private function render_specification_row($index, $spec) {
        echo '<div class="hapd-spec-row">';
        echo '<input type="text" name="hapd_specifications[' . $index . '][name]" value="' . esc_attr($spec['name']) . '" placeholder="' . __('Specification Name', 'hearing-aid-display') . '" class="hapd-spec-name" />';
        echo '<input type="text" name="hapd_specifications[' . $index . '][value]" value="' . esc_attr($spec['value']) . '" placeholder="' . __('Specification Value', 'hearing-aid-display') . '" class="hapd-spec-value" />';
        echo '<button type="button" class="button hapd-remove-spec">' . __('Remove', 'hearing-aid-display') . '</button>';
        echo '</div>';
    }
    
    /**
     * Product features meta box
     */
    public function product_features_meta_box($post) {
        wp_nonce_field('hapd_product_features', 'hapd_product_features_nonce');
        
        $features = get_post_meta($post->ID, '_hapd_features', true);
        if (!is_array($features)) {
            $features = array();
        }
        
        echo '<div id="hapd-features-container">';
        echo '<div class="hapd-features-header">';
        echo '<button type="button" class="button hapd-add-feature">' . __('Add Feature', 'hearing-aid-display') . '</button>';
        echo '</div>';
        echo '<div class="hapd-features-list">';
        
        if (!empty($features)) {
            foreach ($features as $index => $feature) {
                $this->render_feature_row($index, $feature);
            }
        } else {
            $this->render_feature_row(0, '');
        }
        
        echo '</div>';
        echo '</div>';
        
        // Add JavaScript template
        echo '<script type="text/template" id="hapd-feature-template">';
        $this->render_feature_row('{{INDEX}}', '');
        echo '</script>';
    }
    
    /**
     * Render feature row
     */
    private function render_feature_row($index, $feature) {
        echo '<div class="hapd-feature-row">';
        echo '<input type="text" name="hapd_features[' . $index . ']" value="' . esc_attr($feature) . '" placeholder="' . __('Feature Description', 'hearing-aid-display') . '" class="hapd-feature-input" />';
        echo '<button type="button" class="button hapd-remove-feature">' . __('Remove', 'hearing-aid-display') . '</button>';
        echo '</div>';
    }
    
    /**
     * Product gallery meta box
     */
    public function product_gallery_meta_box($post) {
        wp_nonce_field('hapd_product_gallery', 'hapd_product_gallery_nonce');
        
        $gallery_ids = get_post_meta($post->ID, '_hapd_gallery_ids', true);
        if (!is_array($gallery_ids)) {
            $gallery_ids = array();
        }
        
        echo '<div id="hapd-gallery-container">';
        echo '<div class="hapd-gallery-images">';
        
        foreach ($gallery_ids as $attachment_id) {
            if (wp_attachment_is_image($attachment_id)) {
                echo '<div class="hapd-gallery-image" data-attachment-id="' . $attachment_id . '">';
                echo wp_get_attachment_image($attachment_id, 'thumbnail');
                echo '<button type="button" class="hapd-remove-image">×</button>';
                echo '</div>';
            }
        }
        
        echo '</div>';
        echo '<button type="button" class="button hapd-add-gallery-images">' . __('Add Images', 'hearing-aid-display') . '</button>';
        echo '<input type="hidden" name="hapd_gallery_ids" value="' . esc_attr(implode(',', $gallery_ids)) . '" />';
        echo '</div>';
    }
    
    /**
     * Save meta boxes
     */
    public function save_meta_boxes($post_id) {
        // Check if this is an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        // Check post type
        if (get_post_type($post_id) !== 'hearing_aid_product') {
            return;
        }
        
        // Check user permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Save product details
        if (isset($_POST['hapd_product_details_nonce']) && wp_verify_nonce($_POST['hapd_product_details_nonce'], 'hapd_product_details')) {
            if (isset($_POST['hapd_price'])) {
                update_post_meta($post_id, '_hapd_price', sanitize_text_field($_POST['hapd_price']));
            }
            if (isset($_POST['hapd_model_number'])) {
                update_post_meta($post_id, '_hapd_model_number', sanitize_text_field($_POST['hapd_model_number']));
            }
            if (isset($_POST['hapd_warranty'])) {
                update_post_meta($post_id, '_hapd_warranty', sanitize_text_field($_POST['hapd_warranty']));
            }
            if (isset($_POST['hapd_availability'])) {
                update_post_meta($post_id, '_hapd_availability', sanitize_text_field($_POST['hapd_availability']));
            }
        }
        
        // Save specifications
        if (isset($_POST['hapd_product_specs_nonce']) && wp_verify_nonce($_POST['hapd_product_specs_nonce'], 'hapd_product_specs')) {
            if (isset($_POST['hapd_specifications'])) {
                $specifications = array();
                foreach ($_POST['hapd_specifications'] as $spec) {
                    if (!empty($spec['name']) && !empty($spec['value'])) {
                        $specifications[] = array(
                            'name' => sanitize_text_field($spec['name']),
                            'value' => sanitize_text_field($spec['value']),
                        );
                    }
                }
                update_post_meta($post_id, '_hapd_specifications', $specifications);
            }
        }
        
        // Save features
        if (isset($_POST['hapd_product_features_nonce']) && wp_verify_nonce($_POST['hapd_product_features_nonce'], 'hapd_product_features')) {
            if (isset($_POST['hapd_features'])) {
                $features = array();
                foreach ($_POST['hapd_features'] as $feature) {
                    if (!empty($feature)) {
                        $features[] = sanitize_text_field($feature);
                    }
                }
                update_post_meta($post_id, '_hapd_features', $features);
            }
        }
        
        // Save gallery
        if (isset($_POST['hapd_product_gallery_nonce']) && wp_verify_nonce($_POST['hapd_product_gallery_nonce'], 'hapd_product_gallery')) {
            if (isset($_POST['hapd_gallery_ids'])) {
                $gallery_ids = array_map('absint', explode(',', $_POST['hapd_gallery_ids']));
                $gallery_ids = array_filter($gallery_ids);
                update_post_meta($post_id, '_hapd_gallery_ids', $gallery_ids);
            }
        }
    }
    

    

}

// Initialize the plugin
HearingAidProductDisplay::get_instance();

// Uninstall hook
register_uninstall_hook(__FILE__, 'hapd_uninstall');

/**
 * Uninstall function
 */
function hapd_uninstall() {
    global $wpdb;
    
    // Delete custom post type posts
    $posts = get_posts(array(
        'post_type' => 'hearing_aid_product',
        'numberposts' => -1,
        'post_status' => 'any',
    ));
    
    foreach ($posts as $post) {
        wp_delete_post($post->ID, true);
    }
    
    // Delete taxonomies
    $terms = get_terms(array(
        'taxonomy' => array('hearing_aid_category', 'hearing_aid_brand'),
        'hide_empty' => false,
    ));
    
    foreach ($terms as $term) {
        wp_delete_term($term->term_id, $term->taxonomy);
    }
    
    // Delete custom table
    $table_name = $wpdb->prefix . 'hapd_product_specs';
    $wpdb->query("DROP TABLE IF EXISTS $table_name");
    
    // Delete options
    delete_option('hapd_settings');
    
    // Clear rewrite rules
    flush_rewrite_rules();
}
