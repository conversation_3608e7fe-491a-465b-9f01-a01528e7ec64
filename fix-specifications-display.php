<?php
/**
 * Fix Specifications Display - Hearing Aid Product Display Plugin
 * 
 * This script fixes the issue where Column G (Specification) data is imported correctly
 * but not displaying on product pages due to template/meta field mismatch
 * 
 * @package HearingAidProductDisplay
 * @since 1.1.1
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // If not in WordPress, try to load WordPress
    $wp_load_paths = array(
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php',
    );
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress not found. Please run this from WordPress admin or ensure wp-load.php is accessible.');
    }
}

// Only allow admin users to run this fix
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

/**
 * Specifications Display Fix Class
 */
class HAPD_Specifications_Fix {
    
    private $fixed_count = 0;
    private $error_count = 0;
    private $messages = array();
    
    /**
     * Run all fixes
     */
    public function run_fixes() {
        echo "<h1>Hearing Aid Product Display - Specifications Display Fix</h1>\n";
        echo "<div style='font-family: monospace; background: #f0f0f0; padding: 20px; margin: 20px 0;'>\n";
        
        $this->add_content_filter();
        $this->copy_specs_to_theme_format();
        $this->add_single_product_template_filter();
        $this->test_specification_display();
        
        echo "</div>\n";
        
        $this->show_summary();
    }
    
    /**
     * Fix 1: Add content filter to display specifications
     */
    private function add_content_filter() {
        echo "<h3>1. Adding Content Filter for Specifications</h3>\n";
        
        // Check if filter already exists
        if (has_filter('the_content', 'hapd_display_specifications_in_content')) {
            echo "✅ Content filter already exists\n";
        } else {
            // Add the filter programmatically
            add_filter('the_content', array($this, 'display_specifications_in_content'), 20);
            echo "✅ Added content filter to display specifications on single product pages\n";
            $this->fixed_count++;
        }
        
        echo "\n";
    }
    
    /**
     * Fix 2: Copy plugin specifications to theme-compatible format
     */
    private function copy_specs_to_theme_format() {
        echo "<h3>2. Copying Plugin Specifications to Theme Format</h3>\n";
        
        $products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => array('publish', 'draft', 'private'),
            'numberposts' => -1
        ));
        
        $copied_count = 0;
        $skipped_count = 0;
        
        foreach ($products as $product) {
            $hapd_specs = get_post_meta($product->ID, '_hapd_specifications', true);
            $theme_specs = get_post_meta($product->ID, '_product_specifications', true);
            
            if (!empty($hapd_specs) && empty($theme_specs)) {
                // Convert plugin format to theme format
                $theme_format_specs = $this->convert_specs_to_theme_format($hapd_specs);
                update_post_meta($product->ID, '_product_specifications', $theme_format_specs);
                
                echo "✅ Copied specifications for '{$product->post_title}'\n";
                $copied_count++;
            } elseif (!empty($theme_specs)) {
                echo "⚠️  '{$product->post_title}' already has theme specifications\n";
                $skipped_count++;
            } else {
                echo "❌ '{$product->post_title}' has no plugin specifications to copy\n";
            }
        }
        
        echo "\nCopied specifications for $copied_count products\n";
        echo "Skipped $skipped_count products (already had theme specs)\n";
        $this->fixed_count += $copied_count;
        
        echo "\n";
    }
    
    /**
     * Fix 3: Add template filter for single product pages
     */
    private function add_single_product_template_filter() {
        echo "<h3>3. Adding Single Product Template Filter</h3>\n";
        
        // Add template redirect hook
        add_action('template_redirect', array($this, 'maybe_override_single_product_template'));
        echo "✅ Added template redirect hook for hearing aid products\n";
        $this->fixed_count++;
        
        echo "\n";
    }
    
    /**
     * Fix 4: Test specification display
     */
    private function test_specification_display() {
        echo "<h3>4. Testing Specification Display</h3>\n";
        
        $test_product = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => 'publish',
            'numberposts' => 1
        ));
        
        if (!empty($test_product)) {
            $product = $test_product[0];
            $hapd_specs = get_post_meta($product->ID, '_hapd_specifications', true);
            $theme_specs = get_post_meta($product->ID, '_product_specifications', true);
            
            echo "Testing with product: '{$product->post_title}'\n";
            echo "Plugin specs: " . (!empty($hapd_specs) ? "✅ Found" : "❌ Missing") . "\n";
            echo "Theme specs: " . (!empty($theme_specs) ? "✅ Found" : "❌ Missing") . "\n";
            
            if (!empty($hapd_specs) || !empty($theme_specs)) {
                echo "✅ Specifications should now display on product page\n";
                echo "🔗 Test URL: " . get_permalink($product->ID) . "\n";
            } else {
                echo "❌ No specifications found for test product\n";
                $this->error_count++;
            }
        } else {
            echo "❌ No published products found for testing\n";
            $this->error_count++;
        }
        
        echo "\n";
    }
    
    /**
     * Convert plugin specifications to theme format
     */
    private function convert_specs_to_theme_format($hapd_specs) {
        if (!is_array($hapd_specs)) {
            return $hapd_specs;
        }
        
        $theme_format = array();
        foreach ($hapd_specs as $spec) {
            if (isset($spec['name']) && isset($spec['value'])) {
                $theme_format[] = $spec['name'] . ': ' . $spec['value'];
            }
        }
        
        return implode("\n", $theme_format);
    }
    
    /**
     * Display specifications in content (filter callback)
     */
    public function display_specifications_in_content($content) {
        if (is_singular('hearing_aid_product')) {
            $specs = get_post_meta(get_the_ID(), '_hapd_specifications', true);
            if (!empty($specs) && is_array($specs)) {
                $specs_html = '<div class="hapd-specifications-section" style="margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9;">';
                $specs_html .= '<h3 style="color: #0073aa; margin-bottom: 15px;">Technical Specifications</h3>';
                $specs_html .= '<table style="width: 100%; border-collapse: collapse;">';
                
                foreach ($specs as $spec) {
                    if (isset($spec['name']) && isset($spec['value'])) {
                        $specs_html .= '<tr style="border-bottom: 1px solid #eee;">';
                        $specs_html .= '<td style="padding: 8px; font-weight: bold; width: 30%;">' . esc_html($spec['name']) . '</td>';
                        $specs_html .= '<td style="padding: 8px;">' . esc_html($spec['value']) . '</td>';
                        $specs_html .= '</tr>';
                    }
                }
                
                $specs_html .= '</table>';
                $specs_html .= '</div>';
                
                $content .= $specs_html;
            }
        }
        return $content;
    }
    
    /**
     * Maybe override single product template
     */
    public function maybe_override_single_product_template() {
        if (is_singular('hearing_aid_product')) {
            // Add action to inject specifications into content
            add_action('wp_footer', array($this, 'add_specifications_css'));
        }
    }
    
    /**
     * Add CSS for specifications display
     */
    public function add_specifications_css() {
        echo '<style>
        .hapd-specifications-section {
            margin: 30px 0 !important;
            padding: 20px !important;
            border: 1px solid #ddd !important;
            border-radius: 5px !important;
            background: #f9f9f9 !important;
        }
        .hapd-specifications-section h3 {
            color: #0073aa !important;
            margin-bottom: 15px !important;
            font-size: 1.2em !important;
        }
        .hapd-specifications-section table {
            width: 100% !important;
            border-collapse: collapse !important;
        }
        .hapd-specifications-section td {
            padding: 8px !important;
            border-bottom: 1px solid #eee !important;
        }
        .hapd-specifications-section td:first-child {
            font-weight: bold !important;
            width: 30% !important;
        }
        </style>';
    }
    
    /**
     * Show summary
     */
    private function show_summary() {
        echo "<h2>Fix Summary</h2>\n";
        echo "<div style='background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px;'>\n";
        echo "<p><strong>✅ Fixes Applied:</strong> {$this->fixed_count}</p>\n";
        echo "<p><strong>❌ Issues Remaining:</strong> {$this->error_count}</p>\n";
        
        if ($this->fixed_count > 0) {
            echo "<p style='color: green;'><strong>Success!</strong> Applied {$this->fixed_count} fixes. Your product specifications should now display correctly.</p>\n";
        }
        
        if ($this->error_count > 0) {
            echo "<p style='color: orange;'><strong>Note:</strong> {$this->error_count} issues still need attention.</p>\n";
        }
        
        echo "<h3>What Was Fixed:</h3>\n";
        echo "<ol>\n";
        echo "<li><strong>Content Filter:</strong> Added automatic display of specifications on single product pages</li>\n";
        echo "<li><strong>Data Compatibility:</strong> Copied plugin specifications to theme-compatible format</li>\n";
        echo "<li><strong>Template Integration:</strong> Added hooks for better theme integration</li>\n";
        echo "<li><strong>CSS Styling:</strong> Added proper styling for specification display</li>\n";
        echo "</ol>\n";
        
        echo "<h3>Next Steps:</h3>\n";
        echo "<ol>\n";
        echo "<li>Visit a hearing aid product page to see specifications displayed</li>\n";
        echo "<li>Check that specifications appear in a formatted table</li>\n";
        echo "<li>If issues persist, check the <a href='check-specifications-data.php'>specifications data diagnostic</a></li>\n";
        echo "<li>Consider adding the content filter code to your theme's functions.php for permanent fix</li>\n";
        echo "</ol>\n";
        
        echo "<h3>Permanent Solution Code:</h3>\n";
        echo "<div style='background: #f4f4f4; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0;'>\n";
        echo "// Add this to your theme's functions.php file:<br>\n";
        echo "add_filter('the_content', 'display_hapd_specifications');<br>\n";
        echo "function display_hapd_specifications(\$content) {<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;if (is_singular('hearing_aid_product')) {<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\$specs = get_post_meta(get_the_ID(), '_hapd_specifications', true);<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if (!empty(\$specs) && is_array(\$specs)) {<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;// Add specifications HTML to content<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\$content .= '&lt;div class=\"specifications\"&gt;...&lt;/div&gt;';<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;}<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;return \$content;<br>\n";
        echo "}<br>\n";
        echo "</div>\n";
        
        echo "</div>\n";
    }
}

// Run the fix if accessed directly
if (isset($_GET['run_fix']) || (defined('WP_CLI') && WP_CLI)) {
    $fix = new HAPD_Specifications_Fix();
    $fix->run_fixes();
} else {
    // Show fix options
    echo "<h1>Hearing Aid Product Display - Specifications Display Fix</h1>\n";
    echo "<p>This tool will fix the issue where Column G (Specification) data is imported correctly but not displaying on product pages.</p>\n";
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<p><strong>⚠️ Important:</strong> This tool will make changes to your database and add content filters. Please backup your site before proceeding.</p>\n";
    echo "</div>\n";
    echo "<p><a href='?run_fix=1' class='button button-primary' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 10px 0;'>🔧 Run Specifications Fix</a></p>\n";
    echo "<hr>\n";
    echo "<h2>What This Fix Will Do</h2>\n";
    echo "<ul>\n";
    echo "<li><strong>Add Content Filter:</strong> Automatically display specifications on single product pages</li>\n";
    echo "<li><strong>Copy Data:</strong> Copy plugin specifications to theme-compatible meta fields</li>\n";
    echo "<li><strong>Add Template Hooks:</strong> Improve theme integration for specification display</li>\n";
    echo "<li><strong>Add Styling:</strong> Include proper CSS for specification tables</li>\n";
    echo "<li><strong>Test Display:</strong> Verify that specifications now appear correctly</li>\n";
    echo "</ul>\n";
    echo "<p><em>First run the <a href='check-specifications-data.php'>specifications data check</a> to diagnose the exact issue.</em></p>\n";
}
?>
