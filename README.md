# Hearing Aid Product Display

A professional WordPress plugin for displaying hearing aid products with custom Gutenberg blocks, responsive layouts, and comprehensive product management.

## Features

### 🎯 **Core Functionality**
- **Custom Post Type**: Dedicated hearing aid product management
- **Gutenberg Blocks**: Native block editor integration
- **Responsive Layouts**: Grid, list, and carousel display options
- **Product Gallery**: Multiple images with zoom functionality
- **Technical Specifications**: Detailed product specs in table format
- **Key Features**: Highlight product benefits and capabilities

### 🎨 **Display Options**
- **Multiple Layouts**: Grid (1-6 columns), List, Carousel
- **Customizable Display**: Toggle price, specs, features, excerpts
- **Professional Styling**: Medical/healthcare-appropriate design
- **Mobile Responsive**: Optimized for all device sizes
- **Accessibility Compliant**: WCAG 2.1 AA standards

### 🛠 **Admin Features**
- **Intuitive Interface**: Easy product management
- **Bulk Operations**: Import/export via CSV
- **Category Management**: Organize by categories and brands
- **SEO Optimization**: Meta titles and descriptions
- **Product Flags**: Featured, popular, new product badges

### 🔧 **Technical Features**
- **Security First**: Proper sanitization and nonce verification
- **Performance Optimized**: Efficient database queries
- **Translation Ready**: Full internationalization support
- **Clean Uninstall**: Complete data removal option

## Installation

1. **Upload the plugin files** to `/wp-content/plugins/hearing-aid-product-display/`
2. **Activate the plugin** through the 'Plugins' screen in WordPress
3. **Configure settings** at `Hearing Aids > Settings`
4. **Create your first product** at `Hearing Aids > Add New`

## Usage

### Creating Products

1. Navigate to `Hearing Aids > Add New`
2. Fill in product details:
   - **Title & Description**: Basic product information
   - **Price & Model**: Pricing and model number
   - **Specifications**: Technical details in table format
   - **Features**: Key benefits and capabilities
   - **Gallery**: Multiple product images
   - **Categories & Brands**: Organize products

### Using Gutenberg Blocks

1. **Add Block**: Search for "Hearing Aid Products"
2. **Select Products**: Choose which products to display
3. **Configure Layout**: Grid, list, or carousel
4. **Customize Display**: Toggle price, specs, features
5. **Publish**: Your products are now displayed

### Shortcode Usage

```php
[hearing_aid_products layout="grid" columns="3" show_price="true"]
```

**Available Parameters:**
- `layout`: grid, list, carousel
- `columns`: 1-6 (for grid/carousel)
- `show_price`: true/false
- `show_specs`: true/false
- `show_features`: true/false
- `show_excerpt`: true/false
- `ids`: Comma-separated product IDs
- `category`: Category slug
- `brand`: Brand slug

## Customization

### CSS Classes

The plugin uses semantic CSS classes for easy customization:

```css
.hapd-products-container { /* Main container */ }
.hapd-products-grid { /* Grid layout */ }
.hapd-products-list { /* List layout */ }
.hapd-products-carousel { /* Carousel layout */ }
.hapd-product-card { /* Individual product card */ }
.hapd-product-image { /* Product image container */ }
.hapd-product-title { /* Product title */ }
.hapd-product-price { /* Price display */ }
.hapd-product-features { /* Features list */ }
.hapd-product-specs { /* Specifications */ }
```

### Hooks & Filters

```php
// Modify product display
add_filter('hapd_product_display', 'custom_product_display', 10, 2);

// Add custom product fields
add_action('hapd_product_meta_boxes', 'add_custom_meta_box');

// Customize product query
add_filter('hapd_product_query_args', 'modify_product_query');
```

## Settings

### General Settings
- **Currency Symbol**: $ (default)
- **Currency Position**: Before/After price
- **Default Layout**: Grid, List, or Carousel
- **Products Per Page**: Number to display

### Display Settings
- **Enable Image Zoom**: On/Off
- **Show Product Badges**: Featured, Popular, New
- **Default Columns**: For grid layout

### Contact Settings
- **Contact Email**: For customer inquiries
- **Contact Phone**: Optional phone number
- **Contact Form**: Integration with contact forms

## Import/Export

### Export Products
1. Go to `Hearing Aids > Import/Export`
2. Click "Export Products"
3. Download CSV file with all product data

### Import from Excel (NEW!)
The plugin now supports importing products from Excel files (.xlsx/.xls) with a structured format:

**Excel Column Structure:**
- **Column A**: No. (Sequential number/ID)
- **Column B**: Model No. (Product model number) - **Required**
- **Column C**: Photo (Image filename or URL)
- **Column D**: Packaging (Packaging information)
- **Column E**: Accessories (Included accessories, separated by |)
- **Column F**: Description (Product description)
- **Column G**: Specification (Technical specifications)
- **Column H**: Measurement (Product dimensions/measurements)
- **Column I**: Price (Product price, numbers only)

**Import Process:**
1. Prepare Excel file with the 9 columns above
2. Go to `Hearing Aids > Import/Export`
3. Use the "Import from Excel" section
4. Upload your .xlsx or .xls file
5. Monitor progress and review results

**Image Import Options:**
- **URLs**: Provide direct image URLs (will be downloaded automatically)
- **Local Files**: Upload images to `/wp-content/uploads/hearing-aid-products/` first, then reference filenames

**File Size Limits:**
- Maximum file size: 300MB (increased from 10MB)
- Files over 50MB may take several minutes to process
- Large files may require increased server memory and upload limits

### Import from CSV (Legacy)
1. Prepare CSV file with columns:
   - title (required)
   - description
   - excerpt
   - price
   - model_number
   - warranty
   - availability
   - features (separated by |)
   - categories (separated by |)
   - brands (separated by |)

2. Upload via `Hearing Aids > Import/Export`

## Troubleshooting

### Common Issues

**Products not displaying:**
- Check if products are published
- Verify block settings
- Clear caching plugins

**Images not loading:**
- Check file permissions
- Verify image URLs
- Regenerate thumbnails

**Styling issues:**
- Check theme compatibility
- Clear browser cache
- Inspect CSS conflicts

### Debug Mode

Enable WordPress debug mode to see detailed error messages:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Requirements

- **WordPress**: 5.0 or higher
- **PHP**: 7.4 or higher
- **MySQL**: 5.6 or higher
- **Memory**: 128MB minimum

## Browser Support

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+
- Internet Explorer 11 (limited support)

## Security

The plugin implements comprehensive security measures:

- **Nonce Verification**: All forms protected
- **Data Sanitization**: Input/output sanitization
- **Capability Checks**: Proper user permissions
- **SQL Injection Prevention**: Prepared statements
- **XSS Protection**: Escaped output

## Performance

- **Optimized Queries**: Efficient database operations
- **Lazy Loading**: Images loaded on demand
- **Caching Support**: Compatible with caching plugins
- **Minified Assets**: Compressed CSS/JS files

## Accessibility

- **WCAG 2.1 AA Compliant**: Meets accessibility standards
- **Keyboard Navigation**: Full keyboard support
- **Screen Reader Support**: Proper ARIA labels
- **High Contrast**: Support for high contrast mode
- **Focus Management**: Clear focus indicators

## Changelog

### Version 1.1.1
- **ENHANCED**: Increased Excel import file size limit from 10MB to 300MB
- **NEW**: Server upload limit detection and warnings
- **NEW**: Large file processing warnings and guidance
- **NEW**: Server requirements documentation for large files
- **ENHANCED**: Better error handling for large file uploads
- **ENHANCED**: Performance optimizations for large file processing

### Version 1.1.0
- **NEW**: Excel file import support (.xlsx/.xls formats)
- **NEW**: Structured 9-column Excel import format
- **NEW**: Image import from URLs and local files
- **NEW**: Advanced specification parsing
- **NEW**: Real-time import progress tracking
- **NEW**: Detailed import results and error reporting
- **NEW**: Excel import documentation and guides
- **ENHANCED**: Import/Export interface with better organization
- **ENHANCED**: Admin interface with progress modals
- **ENHANCED**: Backward compatibility with existing CSV import

### Version 1.0.0
- Initial release
- Custom post type for hearing aid products
- Gutenberg block integration
- Responsive layouts (grid, list, carousel)
- Product gallery with zoom
- Technical specifications
- Import/export functionality
- Admin interface
- Security implementation
- Accessibility compliance

## Support

For support, feature requests, or bug reports:

1. **Documentation**: Check this README first
2. **WordPress Support**: Visit plugin support forum
3. **GitHub Issues**: Report bugs on GitHub
4. **Email Support**: <EMAIL>

## License

This plugin is licensed under the GPL v2 or later.

## Credits

Developed by the Völkena Development Team for professional hearing aid product display and management.

---

**Thank you for using Hearing Aid Product Display!** 🎧
