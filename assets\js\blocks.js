(function() {
    'use strict';
    
    const { registerBlockType } = wp.blocks;
    const { createElement: el, Component, Fragment } = wp.element;
    const { 
        InspectorControls,
        BlockControls
    } = wp.blockEditor || wp.editor;
    const { 
        PanelBody,
        SelectControl,
        RangeControl,
        ToggleControl,
        CheckboxControl,
        Button,
        Spinner,
        Placeholder,
        Toolbar,
        ToolbarGroup,
        ToolbarButton
    } = wp.components;
    const { __ } = wp.i18n;
    const { apiFetch } = wp;
    const { withSelect } = wp.data;
    
    /**
     * Hearing Aid Products Block
     */
    class HearingAidProductsBlock extends Component {
        constructor(props) {
            super(props);
            
            this.state = {
                products: [],
                loading: true,
                error: null
            };
            
            this.loadProducts = this.loadProducts.bind(this);
            this.toggleProductSelection = this.toggleProductSelection.bind(this);
        }
        
        componentDidMount() {
            this.loadProducts();
        }
        
        /**
         * Load products from API
         */
        loadProducts() {
            this.setState({ loading: true, error: null });
            
            apiFetch({
                path: '/wp/v2/hearing_aid_product?per_page=100&status=publish'
            })
            .then(products => {
                this.setState({ 
                    products: products || [],
                    loading: false 
                });
            })
            .catch(error => {
                console.error('Error loading products:', error);
                this.setState({ 
                    error: error.message || 'Failed to load products',
                    loading: false 
                });
            });
        }
        
        /**
         * Toggle product selection
         */
        toggleProductSelection(productId) {
            const { attributes, setAttributes } = this.props;
            const { selectedProducts } = attributes;
            
            let newSelection;
            if (selectedProducts.includes(productId)) {
                newSelection = selectedProducts.filter(id => id !== productId);
            } else {
                newSelection = [...selectedProducts, productId];
            }
            
            setAttributes({ selectedProducts: newSelection });
        }
        
        /**
         * Render product selection
         */
        renderProductSelection() {
            const { products, loading, error } = this.state;
            const { attributes } = this.props;
            const { selectedProducts } = attributes;
            
            if (loading) {
                return el('div', { className: 'hapd-loading' },
                    el(Spinner),
                    el('p', null, __('Loading products...', 'hearing-aid-display'))
                );
            }
            
            if (error) {
                return el('div', { className: 'hapd-error' },
                    el('p', null, __('Error loading products: ', 'hearing-aid-display') + error),
                    el(Button, {
                        isPrimary: true,
                        onClick: this.loadProducts
                    }, __('Retry', 'hearing-aid-display'))
                );
            }
            
            if (!products.length) {
                return el('div', { className: 'hapd-no-products' },
                    el('p', null, __('No hearing aid products found.', 'hearing-aid-display')),
                    el('p', null, 
                        el('a', { 
                            href: wp.url.addQueryArgs('post-new.php', { post_type: 'hearing_aid_product' }),
                            target: '_blank'
                        }, __('Create your first product', 'hearing-aid-display'))
                    )
                );
            }
            
            return el('div', { className: 'hapd-product-selection' },
                el('h4', null, __('Select Products to Display:', 'hearing-aid-display')),
                el('div', { className: 'hapd-product-checkboxes' },
                    products.map(product => 
                        el('div', { 
                            key: product.id,
                            className: 'hapd-product-checkbox'
                        },
                            el(CheckboxControl, {
                                label: product.title.rendered,
                                checked: selectedProducts.includes(product.id),
                                onChange: () => this.toggleProductSelection(product.id)
                            })
                        )
                    )
                ),
                el('div', { className: 'hapd-selection-actions' },
                    el(Button, {
                        isSecondary: true,
                        onClick: () => {
                            const allIds = products.map(p => p.id);
                            this.props.setAttributes({ selectedProducts: allIds });
                        }
                    }, __('Select All', 'hearing-aid-display')),
                    el(Button, {
                        isSecondary: true,
                        onClick: () => {
                            this.props.setAttributes({ selectedProducts: [] });
                        }
                    }, __('Clear All', 'hearing-aid-display'))
                )
            );
        }
        
        /**
         * Render block preview
         */
        renderPreview() {
            const { attributes } = this.props;
            const { selectedProducts, layout, columns } = attributes;
            const { products } = this.state;
            
            if (!selectedProducts.length) {
                return el(Placeholder, {
                    icon: 'admin-generic',
                    label: __('Hearing Aid Products', 'hearing-aid-display'),
                    instructions: __('Select products to display from the block settings.', 'hearing-aid-display')
                });
            }
            
            const selectedProductsData = products.filter(p => selectedProducts.includes(p.id));
            
            return el('div', { 
                className: `hapd-block-preview hapd-layout-${layout} hapd-columns-${columns}`
            },
                el('div', { className: 'hapd-preview-header' },
                    el('h3', null, __('Hearing Aid Products Preview', 'hearing-aid-display')),
                    el('p', null, 
                        selectedProductsData.length + ' ' + 
                        (selectedProductsData.length === 1 ? 
                            __('product selected', 'hearing-aid-display') : 
                            __('products selected', 'hearing-aid-display')
                        )
                    )
                ),
                el('div', { className: 'hapd-preview-products' },
                    selectedProductsData.slice(0, 6).map(product => 
                        el('div', { 
                            key: product.id,
                            className: 'hapd-preview-product'
                        },
                            product.featured_media && el('div', { className: 'hapd-preview-image' },
                                el('img', {
                                    src: product._embedded && product._embedded['wp:featuredmedia'] && 
                                         product._embedded['wp:featuredmedia'][0] && 
                                         product._embedded['wp:featuredmedia'][0].media_details &&
                                         product._embedded['wp:featuredmedia'][0].media_details.sizes &&
                                         product._embedded['wp:featuredmedia'][0].media_details.sizes.thumbnail ?
                                         product._embedded['wp:featuredmedia'][0].media_details.sizes.thumbnail.source_url :
                                         product._embedded && product._embedded['wp:featuredmedia'] && 
                                         product._embedded['wp:featuredmedia'][0] ?
                                         product._embedded['wp:featuredmedia'][0].source_url : '',
                                    alt: product.title.rendered
                                })
                            ),
                            el('h4', null, product.title.rendered),
                            product.excerpt && el('p', { 
                                dangerouslySetInnerHTML: { __html: product.excerpt.rendered }
                            })
                        )
                    )
                ),
                selectedProductsData.length > 6 && el('p', { className: 'hapd-preview-more' },
                    __('... and ', 'hearing-aid-display') + (selectedProductsData.length - 6) + 
                    __(' more products', 'hearing-aid-display')
                )
            );
        }
        
        render() {
            const { attributes, setAttributes, isSelected } = this.props;
            const { 
                layout, 
                columns, 
                showPrice, 
                showSpecs, 
                showFeatures, 
                showExcerpt 
            } = attributes;
            
            return el(Fragment, null,
                // Block Controls
                el(BlockControls, null,
                    el(ToolbarGroup, null,
                        el(ToolbarButton, {
                            icon: 'grid-view',
                            title: __('Grid Layout', 'hearing-aid-display'),
                            isActive: layout === 'grid',
                            onClick: () => setAttributes({ layout: 'grid' })
                        }),
                        el(ToolbarButton, {
                            icon: 'list-view',
                            title: __('List Layout', 'hearing-aid-display'),
                            isActive: layout === 'list',
                            onClick: () => setAttributes({ layout: 'list' })
                        }),
                        el(ToolbarButton, {
                            icon: 'slides',
                            title: __('Carousel Layout', 'hearing-aid-display'),
                            isActive: layout === 'carousel',
                            onClick: () => setAttributes({ layout: 'carousel' })
                        })
                    )
                ),
                
                // Inspector Controls
                el(InspectorControls, null,
                    // Layout Settings
                    el(PanelBody, {
                        title: __('Layout Settings', 'hearing-aid-display'),
                        initialOpen: true
                    },
                        el(SelectControl, {
                            label: __('Layout Style', 'hearing-aid-display'),
                            value: layout,
                            options: [
                                { label: __('Grid', 'hearing-aid-display'), value: 'grid' },
                                { label: __('List', 'hearing-aid-display'), value: 'list' },
                                { label: __('Carousel', 'hearing-aid-display'), value: 'carousel' }
                            ],
                            onChange: (value) => setAttributes({ layout: value })
                        }),
                        
                        (layout === 'grid' || layout === 'carousel') && el(RangeControl, {
                            label: __('Columns', 'hearing-aid-display'),
                            value: columns,
                            onChange: (value) => setAttributes({ columns: value }),
                            min: 1,
                            max: 6,
                            step: 1
                        })
                    ),
                    
                    // Display Options
                    el(PanelBody, {
                        title: __('Display Options', 'hearing-aid-display'),
                        initialOpen: true
                    },
                        el(ToggleControl, {
                            label: __('Show Price', 'hearing-aid-display'),
                            checked: showPrice,
                            onChange: (value) => setAttributes({ showPrice: value })
                        }),
                        
                        el(ToggleControl, {
                            label: __('Show Excerpt', 'hearing-aid-display'),
                            checked: showExcerpt,
                            onChange: (value) => setAttributes({ showExcerpt: value })
                        }),
                        
                        el(ToggleControl, {
                            label: __('Show Features', 'hearing-aid-display'),
                            checked: showFeatures,
                            onChange: (value) => setAttributes({ showFeatures: value })
                        }),
                        
                        el(ToggleControl, {
                            label: __('Show Specifications', 'hearing-aid-display'),
                            checked: showSpecs,
                            onChange: (value) => setAttributes({ showSpecs: value })
                        })
                    ),
                    
                    // Product Selection
                    el(PanelBody, {
                        title: __('Product Selection', 'hearing-aid-display'),
                        initialOpen: false
                    },
                        this.renderProductSelection()
                    )
                ),
                
                // Block Content
                el('div', { className: 'hapd-block-container' },
                    this.renderPreview()
                )
            );
        }
    }
    
    // Enhanced component with WordPress data
    const HearingAidProductsBlockWithData = withSelect((select) => {
        const { getEntityRecords } = select('core');
        
        return {
            products: getEntityRecords('postType', 'hearing_aid_product', {
                per_page: -1,
                status: 'publish',
                _embed: true
            })
        };
    })(HearingAidProductsBlock);
    
    /**
     * Register the block
     */
    registerBlockType('hapd/hearing-aid-products', {
        title: __('Hearing Aid Products', 'hearing-aid-display'),
        description: __('Display hearing aid products in a professional layout with customizable options.', 'hearing-aid-display'),
        icon: 'admin-generic',
        category: 'widgets',
        keywords: [
            __('hearing aid', 'hearing-aid-display'),
            __('products', 'hearing-aid-display'),
            __('medical', 'hearing-aid-display')
        ],
        supports: {
            align: ['wide', 'full'],
            html: false
        },
        attributes: {
            selectedProducts: {
                type: 'array',
                default: []
            },
            layout: {
                type: 'string',
                default: 'grid'
            },
            columns: {
                type: 'number',
                default: 3
            },
            showPrice: {
                type: 'boolean',
                default: true
            },
            showSpecs: {
                type: 'boolean',
                default: true
            },
            showFeatures: {
                type: 'boolean',
                default: true
            },
            showExcerpt: {
                type: 'boolean',
                default: true
            }
        },
        
        edit: HearingAidProductsBlockWithData,
        
        save: function() {
            // Server-side rendering
            return null;
        }
    });
    
})();
