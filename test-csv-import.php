<?php
/**
 * CSV Import Test Script
 * 
 * This script tests the CSV import functionality
 * Run this by accessing it directly in your browser
 */

// WordPress Bootstrap
require_once('../../../wp-config.php');

// Set content type
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>CSV Import Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 5px; }
        .success { color: #008000; }
        .error { color: #cc0000; }
        .warning { color: #ff8800; }
        .info { color: #0073aa; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h2 { color: #23282d; border-bottom: 2px solid #0073aa; padding-bottom: 5px; }
        .test-button { background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px 0; }
        .test-button:hover { background: #005a87; color: white; }
        pre { background: #f9f9f9; padding: 10px; border-left: 4px solid #0073aa; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 CSV Import Test</h1>
        
        <?php
        if (isset($_GET['run_test'])) {
            echo "<div class='section'>";
            echo "<h2>Running CSV Import Test</h2>";
            
            // Test 1: Check if plugin is active
            echo "<h3>1. Plugin Status</h3>";
            if (is_plugin_active('hearing-aid-product-display/hearing-aid-product-display.php')) {
                echo "<p class='success'>✅ Plugin is active</p>";
            } else {
                echo "<p class='error'>❌ Plugin is NOT active</p>";
                echo "</div></div></body></html>";
                exit;
            }
            
            // Test 2: Check if admin class exists
            echo "<h3>2. Admin Class</h3>";
            if (class_exists('HAPD_Admin')) {
                echo "<p class='success'>✅ HAPD_Admin class exists</p>";
            } else {
                echo "<p class='error'>❌ HAPD_Admin class does NOT exist</p>";
            }
            
            // Test 3: Create test CSV content
            echo "<h3>3. Creating Test CSV Data</h3>";
            $test_csv_content = "title,description,excerpt,price,model_number,warranty,availability,features,categories,brands\n";
            $test_csv_content .= "\"Test CSV Product\",\"This is a test product from CSV import\",\"Test product\",99.99,TEST-001,\"1 year\",in_stock,\"Test feature 1|Test feature 2\",\"Test Category\",\"Test Brand\"\n";
            
            $temp_file = tempnam(sys_get_temp_dir(), 'hapd_csv_test_');
            $temp_csv_file = $temp_file . '.csv';
            rename($temp_file, $temp_csv_file);
            
            if (file_put_contents($temp_csv_file, $test_csv_content)) {
                echo "<p class='success'>✅ Test CSV file created</p>";
                echo "<pre>" . htmlspecialchars($test_csv_content) . "</pre>";
            } else {
                echo "<p class='error'>❌ Failed to create test CSV file</p>";
                echo "</div></div></body></html>";
                exit;
            }
            
            // Test 4: Simulate CSV import process
            echo "<h3>4. Testing CSV Import Process</h3>";
            
            // Check if we can read the CSV
            $handle = fopen($temp_csv_file, 'r');
            if (!$handle) {
                echo "<p class='error'>❌ Cannot read test CSV file</p>";
            } else {
                echo "<p class='success'>✅ Can read test CSV file</p>";
                
                $headers = fgetcsv($handle);
                if ($headers) {
                    echo "<p class='success'>✅ CSV headers read successfully</p>";
                    echo "<p class='info'>Headers: " . implode(', ', $headers) . "</p>";
                    
                    // Check for required 'title' column
                    if (in_array('title', $headers)) {
                        echo "<p class='success'>✅ Required 'title' column found</p>";
                    } else {
                        echo "<p class='error'>❌ Required 'title' column missing</p>";
                    }
                    
                    // Read first data row
                    $data = fgetcsv($handle);
                    if ($data) {
                        echo "<p class='success'>✅ CSV data row read successfully</p>";
                        $product_data = array_combine($headers, $data);
                        echo "<p class='info'>Sample data: " . htmlspecialchars(json_encode($product_data, JSON_PRETTY_PRINT)) . "</p>";
                        
                        // Test product creation
                        if (!empty($product_data['title'])) {
                            echo "<p class='success'>✅ Title field has data: '" . htmlspecialchars($product_data['title']) . "'</p>";
                            
                            // Actually create a test product
                            $post_id = wp_insert_post(array(
                                'post_title' => sanitize_text_field($product_data['title']),
                                'post_content' => wp_kses_post($product_data['description'] ?? ''),
                                'post_excerpt' => sanitize_text_field($product_data['excerpt'] ?? ''),
                                'post_type' => 'hearing_aid_product',
                                'post_status' => 'draft', // Use draft for test
                            ));
                            
                            if ($post_id && !is_wp_error($post_id)) {
                                echo "<p class='success'>✅ Test product created successfully (ID: $post_id)</p>";
                                
                                // Add meta data
                                if (!empty($product_data['price'])) {
                                    update_post_meta($post_id, '_hapd_price', sanitize_text_field($product_data['price']));
                                    echo "<p class='success'>✅ Price meta added</p>";
                                }
                                
                                if (!empty($product_data['model_number'])) {
                                    update_post_meta($post_id, '_hapd_model_number', sanitize_text_field($product_data['model_number']));
                                    echo "<p class='success'>✅ Model number meta added</p>";
                                }
                                
                                if (!empty($product_data['features'])) {
                                    $features = explode('|', $product_data['features']);
                                    update_post_meta($post_id, '_hapd_features', array_map('sanitize_text_field', $features));
                                    echo "<p class='success'>✅ Features meta added</p>";
                                }
                                
                                // Clean up - delete test product
                                wp_delete_post($post_id, true);
                                echo "<p class='info'>🧹 Test product cleaned up</p>";
                                
                            } else {
                                echo "<p class='error'>❌ Failed to create test product</p>";
                                if (is_wp_error($post_id)) {
                                    echo "<p class='error'>Error: " . $post_id->get_error_message() . "</p>";
                                }
                            }
                        } else {
                            echo "<p class='error'>❌ Title field is empty</p>";
                        }
                    } else {
                        echo "<p class='error'>❌ No data rows found in CSV</p>";
                    }
                } else {
                    echo "<p class='error'>❌ Cannot read CSV headers</p>";
                }
                
                fclose($handle);
            }
            
            // Clean up temp file
            if (file_exists($temp_csv_file)) {
                unlink($temp_csv_file);
                echo "<p class='info'>🧹 Temporary CSV file cleaned up</p>";
            }
            
            // Test 5: Check existing products
            echo "<h3>5. Checking Existing Products</h3>";
            $products = get_posts(array(
                'post_type' => 'hearing_aid_product',
                'post_status' => array('publish', 'draft'),
                'numberposts' => 5,
                'orderby' => 'date',
                'order' => 'DESC'
            ));
            
            if (!empty($products)) {
                echo "<p class='success'>✅ Found " . count($products) . " existing products</p>";
                foreach ($products as $product) {
                    echo "<p class='info'>- {$product->post_title} (Status: {$product->post_status})</p>";
                }
            } else {
                echo "<p class='warning'>⚠️ No existing products found</p>";
            }
            
            echo "<h3>6. Test Results Summary</h3>";
            echo "<p class='success'>✅ CSV import functionality appears to be working correctly!</p>";
            echo "<p class='info'>If you're still having issues, try:</p>";
            echo "<ul>";
            echo "<li>Using the sample CSV file provided</li>";
            echo "<li>Checking WordPress error logs</li>";
            echo "<li>Verifying file permissions</li>";
            echo "<li>Testing with a smaller CSV file</li>";
            echo "</ul>";
            
            echo "</div>";
        } else {
            // Show test options
            echo "<div class='section'>";
            echo "<h2>CSV Import Testing</h2>";
            echo "<p>This test will verify that the CSV import functionality is working correctly.</p>";
            echo "<p><a href='?run_test=1' class='test-button'>🧪 Run CSV Import Test</a></p>";
            echo "</div>";
            
            echo "<div class='section'>";
            echo "<h2>Manual Testing Steps</h2>";
            echo "<ol>";
            echo "<li><strong>Run the test above</strong> to verify system compatibility</li>";
            echo "<li><strong>Download sample CSV:</strong> <a href='sample-hearing-aid-products.csv' download>sample-hearing-aid-products.csv</a></li>";
            echo "<li><strong>Go to WordPress Admin:</strong> Hearing Aids → Import/Export</li>";
            echo "<li><strong>Find CSV Import section</strong> (labeled 'Import from CSV (Legacy)')</li>";
            echo "<li><strong>Upload the sample CSV file</strong></li>";
            echo "<li><strong>Click 'Import CSV'</strong> and wait for results</li>";
            echo "<li><strong>Check results:</strong> Go to Hearing Aids → All Products</li>";
            echo "<li><strong>Test frontend:</strong> Add [hearing_aid_products] to a page</li>";
            echo "</ol>";
            echo "</div>";
            
            echo "<div class='section'>";
            echo "<h2>CSV Format Requirements</h2>";
            echo "<p>Your CSV file must have these exact headers:</p>";
            echo "<pre>title,description,excerpt,price,model_number,warranty,availability,features,categories,brands</pre>";
            echo "<p><strong>Important:</strong></p>";
            echo "<ul>";
            echo "<li>The 'title' column is required</li>";
            echo "<li>Headers are case-sensitive</li>";
            echo "<li>Use pipe (|) to separate multiple values in features, categories, brands</li>";
            echo "<li>Save file as .csv format (not .xlsx)</li>";
            echo "</ul>";
            echo "</div>";
        }
        ?>
    </div>
</body>
</html>
