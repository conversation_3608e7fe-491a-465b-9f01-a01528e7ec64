<?php
/**
 * Excel Importer Class
 *
 * @package HearingAidProductDisplay
 * @since 1.1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Excel Importer Class
 */
class HAPD_Excel_Importer {
    
    /**
     * Expected Excel column mapping
     */
    private $excel_column_mapping = array(
        'A' => 'no',           // No. - Sequential number/ID
        'B' => 'model_no',     // Model No. - Product model number
        'C' => 'photo',        // Photo - Image filename or URL
        'D' => 'packaging',    // Packaging - Packaging information
        'E' => 'accessories',  // Accessories - Included accessories list
        'F' => 'description',  // Description - Product description
        'G' => 'specification', // Specification - Technical specifications
        'H' => 'measurement',  // Measurement - Product dimensions/measurements
        'I' => 'price',        // Price - Product price
    );
    
    /**
     * Import results
     */
    private $import_results = array(
        'imported' => 0,
        'errors' => 0,
        'warnings' => 0,
        'messages' => array(),
    );
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_ajax_hapd_excel_import', array($this, 'handle_ajax_import'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    /**
     * Enqueue scripts for Excel import
     */
    public function enqueue_scripts($hook) {
        if (strpos($hook, 'hearing-aid-import-export') !== false) {
            wp_enqueue_script(
                'hapd-excel-import',
                HAPD_PLUGIN_URL . 'assets/js/excel-import.js',
                array('jquery'),
                HAPD_VERSION,
                true
            );
            
            wp_localize_script('hapd-excel-import', 'hapdExcelImport', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('hapd_excel_import'),
                'strings' => array(
                    'processing' => __('Processing Excel file...', 'hearing-aid-display'),
                    'importing' => __('Importing products...', 'hearing-aid-display'),
                    'complete' => __('Import complete!', 'hearing-aid-display'),
                    'error' => __('Import failed. Please try again.', 'hearing-aid-display'),
                ),
            ));
        }
    }
    
    /**
     * Check if file is Excel format
     */
    public function is_excel_file($filename) {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return in_array($extension, array('xlsx', 'xls'));
    }

    /**
     * Check WordPress upload limits and provide warnings
     */
    public function check_upload_limits() {
        $wp_max_upload = wp_max_upload_size();
        $php_max_upload = $this->return_bytes(ini_get('upload_max_filesize'));
        $php_max_post = $this->return_bytes(ini_get('post_max_size'));
        $php_memory_limit = $this->return_bytes(ini_get('memory_limit'));

        $effective_limit = min($wp_max_upload, $php_max_upload, $php_max_post);
        $target_size = 300 * 1024 * 1024; // 300MB

        $warnings = array();

        if ($effective_limit < $target_size) {
            $limit_mb = round($effective_limit / (1024 * 1024));
            $warnings[] = sprintf(
                __('Current upload limit is %dMB. You may need to increase server limits to upload files up to 300MB.', 'hearing-aid-display'),
                $limit_mb
            );
        }

        if ($php_memory_limit > 0 && $php_memory_limit < ($target_size * 2)) {
            $memory_mb = round($php_memory_limit / (1024 * 1024));
            $warnings[] = sprintf(
                __('PHP memory limit is %dMB. Large Excel files may require more memory. Consider increasing memory_limit.', 'hearing-aid-display'),
                $memory_mb
            );
        }

        return $warnings;
    }

    /**
     * Convert PHP size format to bytes
     */
    private function return_bytes($size_str) {
        if (empty($size_str)) {
            return 0;
        }

        $size_str = trim($size_str);
        $last = strtolower($size_str[strlen($size_str) - 1]);
        $size = (int) $size_str;

        switch ($last) {
            case 'g':
                $size *= 1024;
            case 'm':
                $size *= 1024;
            case 'k':
                $size *= 1024;
        }

        return $size;
    }
    
    /**
     * Handle AJAX Excel import
     */
    public function handle_ajax_import() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'hapd_excel_import')) {
            wp_die(__('Security check failed', 'hearing-aid-display'));
        }
        
        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'hearing-aid-display'));
        }
        
        if (!isset($_FILES['excel_file'])) {
            wp_send_json_error(__('No file uploaded', 'hearing-aid-display'));
        }
        
        $file = $_FILES['excel_file'];
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(__('File upload error', 'hearing-aid-display'));
        }
        
        if (!$this->is_excel_file($file['name'])) {
            wp_send_json_error(__('Please upload an Excel file (.xlsx or .xls)', 'hearing-aid-display'));
        }

        // Check file size (max 300MB)
        $max_file_size = 300 * 1024 * 1024; // 300MB
        if ($file['size'] > $max_file_size) {
            wp_send_json_error(__('File size exceeds the maximum limit of 300MB', 'hearing-aid-display'));
        }

        // Log large file processing
        if ($file['size'] > 50 * 1024 * 1024) { // 50MB+
            $file_size_mb = round($file['size'] / (1024 * 1024), 2);
            error_log("HAPD: Processing large Excel file ({$file_size_mb}MB) - {$file['name']}");
        }

        // Process the Excel file
        $result = $this->process_excel_file($file['tmp_name']);
        
        if ($result) {
            wp_send_json_success($this->import_results);
        } else {
            wp_send_json_error(__('Failed to process Excel file', 'hearing-aid-display'));
        }
    }
    
    /**
     * Process Excel file using SimpleXLSX library
     */
    public function process_excel_file($file_path) {
        // Include SimpleXLSX library
        require_once HAPD_PLUGIN_DIR . 'includes/libraries/SimpleXLSX.php';
        
        try {
            if ($xlsx = SimpleXLSX::parse($file_path)) {
                $rows = $xlsx->rows();
                
                // Skip header row if it exists
                $header_row = array_shift($rows);
                $this->validate_excel_headers($header_row);
                
                $total_rows = count($rows);
                $processed = 0;
                
                foreach ($rows as $row_index => $row) {
                    $processed++;
                    
                    // Update progress (for AJAX)
                    if ($processed % 10 === 0) {
                        $this->update_progress($processed, $total_rows);
                    }
                    
                    $this->process_excel_row($row, $row_index + 2); // +2 because we skipped header and arrays are 0-indexed
                }
                
                return true;
            } else {
                $this->add_error(__('Failed to parse Excel file: ', 'hearing-aid-display') . SimpleXLSX::parseError());
                return false;
            }
        } catch (Exception $e) {
            $this->add_error(__('Excel processing error: ', 'hearing-aid-display') . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Validate Excel headers
     */
    private function validate_excel_headers($header_row) {
        $expected_headers = array(
            __('No.', 'hearing-aid-display'),
            __('Model No.', 'hearing-aid-display'),
            __('Photo', 'hearing-aid-display'),
            __('Packaging', 'hearing-aid-display'),
            __('Accessories', 'hearing-aid-display'),
            __('Description', 'hearing-aid-display'),
            __('Specification', 'hearing-aid-display'),
            __('Measurement', 'hearing-aid-display'),
            __('Price', 'hearing-aid-display'),
        );
        
        // Check if we have the minimum required columns
        if (count($header_row) < 9) {
            $this->add_warning(__('Excel file should have 9 columns. Some data may be missing.', 'hearing-aid-display'));
        }
        
        // Log header validation for debugging
        $this->add_message(__('Excel headers detected: ', 'hearing-aid-display') . implode(', ', $header_row));
    }
    
    /**
     * Process individual Excel row
     */
    private function process_excel_row($row, $row_number) {
        // Map Excel columns to our data structure
        $product_data = array(
            'no' => $row[0] ?? '',
            'model_no' => $row[1] ?? '',
            'photo' => $row[2] ?? '',
            'packaging' => $row[3] ?? '',
            'accessories' => $row[4] ?? '',
            'description' => $row[5] ?? '',
            'specification' => $row[6] ?? '',
            'measurement' => $row[7] ?? '',
            'price' => $row[8] ?? '',
        );
        
        // Validate required fields
        if (empty($product_data['model_no']) && empty($product_data['description'])) {
            $this->add_error(sprintf(__('Row %d: Missing required data (Model No. or Description)', 'hearing-aid-display'), $row_number));
            return false;
        }
        
        // Create product title from model number or description
        $product_title = !empty($product_data['model_no']) ? $product_data['model_no'] : 
                        (!empty($product_data['description']) ? wp_trim_words($product_data['description'], 5) : 
                        sprintf(__('Product %s', 'hearing-aid-display'), $product_data['no']));
        
        // Create WordPress post
        $post_data = array(
            'post_title' => sanitize_text_field($product_title),
            'post_content' => wp_kses_post($product_data['description']),
            'post_excerpt' => wp_trim_words(strip_tags($product_data['description']), 20),
            'post_type' => 'hearing_aid_product',
            'post_status' => 'draft',
        );
        
        $post_id = wp_insert_post($post_data);
        
        if (is_wp_error($post_id)) {
            $this->add_error(sprintf(__('Row %d: Failed to create product - %s', 'hearing-aid-display'), $row_number, $post_id->get_error_message()));
            return false;
        }
        
        // Add meta data
        $this->add_product_meta($post_id, $product_data, $row_number);
        
        // Handle image import
        $this->handle_image_import($post_id, $product_data['photo'], $row_number);
        
        $this->import_results['imported']++;
        return true;
    }
    
    /**
     * Add product meta data
     */
    private function add_product_meta($post_id, $product_data, $row_number) {
        // Basic product information
        if (!empty($product_data['model_no'])) {
            update_post_meta($post_id, '_hapd_model_number', sanitize_text_field($product_data['model_no']));
        }
        
        if (!empty($product_data['price'])) {
            // Clean price data (remove currency symbols, etc.)
            $price = preg_replace('/[^\d.,]/', '', $product_data['price']);
            update_post_meta($post_id, '_hapd_price', sanitize_text_field($price));
        }
        
        if (!empty($product_data['measurement'])) {
            update_post_meta($post_id, '_hapd_dimensions', sanitize_text_field($product_data['measurement']));
        }
        
        if (!empty($product_data['packaging'])) {
            update_post_meta($post_id, '_hapd_packaging', sanitize_text_field($product_data['packaging']));
        }
        
        // Process specifications
        if (!empty($product_data['specification'])) {
            $specifications = $this->parse_specifications($product_data['specification']);
            update_post_meta($post_id, '_hapd_specifications', $specifications);
        }
        
        // Process accessories as features
        if (!empty($product_data['accessories'])) {
            $accessories = $this->parse_accessories($product_data['accessories']);
            update_post_meta($post_id, '_hapd_features', $accessories);
        }
        
        // Set default availability
        update_post_meta($post_id, '_hapd_availability', 'contact_for_availability');
        
        // Add import metadata
        update_post_meta($post_id, '_hapd_imported_from_excel', current_time('mysql'));
        update_post_meta($post_id, '_hapd_excel_row_number', $row_number);
        update_post_meta($post_id, '_hapd_excel_original_no', sanitize_text_field($product_data['no']));
    }
    
    /**
     * Parse specifications from Excel data
     */
    private function parse_specifications($spec_data) {
        $specifications = array();
        
        // Try to parse different formats
        if (strpos($spec_data, '|') !== false) {
            // Format: "Spec1: Value1 | Spec2: Value2"
            $specs = explode('|', $spec_data);
            foreach ($specs as $spec) {
                if (strpos($spec, ':') !== false) {
                    list($name, $value) = explode(':', $spec, 2);
                    $specifications[] = array(
                        'name' => trim(sanitize_text_field($name)),
                        'value' => trim(sanitize_text_field($value)),
                    );
                }
            }
        } elseif (strpos($spec_data, "\n") !== false) {
            // Format: Multi-line specifications
            $lines = explode("\n", $spec_data);
            foreach ($lines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    if (strpos($line, ':') !== false) {
                        list($name, $value) = explode(':', $line, 2);
                        $specifications[] = array(
                            'name' => trim(sanitize_text_field($name)),
                            'value' => trim(sanitize_text_field($value)),
                        );
                    } else {
                        // Single line specification
                        $specifications[] = array(
                            'name' => __('Specification', 'hearing-aid-display'),
                            'value' => sanitize_text_field($line),
                        );
                    }
                }
            }
        } else {
            // Single specification
            $specifications[] = array(
                'name' => __('Specification', 'hearing-aid-display'),
                'value' => sanitize_text_field($spec_data),
            );
        }
        
        return $specifications;
    }
    
    /**
     * Parse accessories from Excel data
     */
    private function parse_accessories($accessories_data) {
        $accessories = array();
        
        // Try different separators
        $separators = array('|', ',', ';', "\n");
        
        foreach ($separators as $separator) {
            if (strpos($accessories_data, $separator) !== false) {
                $items = explode($separator, $accessories_data);
                foreach ($items as $item) {
                    $item = trim($item);
                    if (!empty($item)) {
                        $accessories[] = sanitize_text_field($item);
                    }
                }
                break;
            }
        }
        
        // If no separators found, treat as single accessory
        if (empty($accessories) && !empty($accessories_data)) {
            $accessories[] = sanitize_text_field($accessories_data);
        }
        
        return $accessories;
    }
    
    /**
     * Handle image import
     */
    private function handle_image_import($post_id, $photo_data, $row_number) {
        if (empty($photo_data)) {
            return;
        }

        // Trim whitespace and normalize the photo data
        $photo_data = trim($photo_data);

        // Log the image import attempt
        error_log("HAPD: Attempting to import image for product ID $post_id, Row $row_number: $photo_data");

        // Check if it's a URL
        if (filter_var($photo_data, FILTER_VALIDATE_URL)) {
            $this->import_image_from_url($post_id, $photo_data, $row_number);
        } else {
            // Assume it's a filename in uploads directory
            $this->import_image_from_filename($post_id, $photo_data, $row_number);
        }

        // Verify the image was actually set
        if (has_post_thumbnail($post_id)) {
            $thumbnail_id = get_post_thumbnail_id($post_id);
            $this->add_message(sprintf(__('Row %d: Featured image successfully set (Attachment ID: %d)', 'hearing-aid-display'), $row_number, $thumbnail_id));
        } else {
            $this->add_warning(sprintf(__('Row %d: Featured image was not set for unknown reason', 'hearing-aid-display'), $row_number));
        }
    }
    
    /**
     * Import image from URL
     */
    private function import_image_from_url($post_id, $image_url, $row_number) {
        require_once(ABSPATH . 'wp-admin/includes/media.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        
        try {
            $attachment_id = media_sideload_image($image_url, $post_id, null, 'id');
            
            if (is_wp_error($attachment_id)) {
                $this->add_warning(sprintf(__('Row %d: Failed to import image from URL - %s', 'hearing-aid-display'), $row_number, $attachment_id->get_error_message()));
            } else {
                set_post_thumbnail($post_id, $attachment_id);
                $this->add_message(sprintf(__('Row %d: Image imported successfully from URL', 'hearing-aid-display'), $row_number));
            }
        } catch (Exception $e) {
            $this->add_warning(sprintf(__('Row %d: Image import error - %s', 'hearing-aid-display'), $row_number, $e->getMessage()));
        }
    }
    
    /**
     * Import image from filename
     */
    private function import_image_from_filename($post_id, $filename, $row_number) {
        // Look for image in uploads/hearing-aid-products/ directory
        $upload_dir = wp_upload_dir();
        $source_image_path = $upload_dir['basedir'] . '/hearing-aid-products/' . $filename;

        if (!file_exists($source_image_path)) {
            $this->add_warning(sprintf(__('Row %d: Image file not found - %s', 'hearing-aid-display'), $row_number, $filename));
            return;
        }

        // Generate unique filename to avoid conflicts
        $file_info = pathinfo($filename);
        $unique_filename = wp_unique_filename($upload_dir['path'], $filename);
        $destination_path = $upload_dir['path'] . '/' . $unique_filename;

        // Copy file to WordPress uploads directory
        if (!copy($source_image_path, $destination_path)) {
            $this->add_warning(sprintf(__('Row %d: Failed to copy image file - %s', 'hearing-aid-display'), $row_number, $filename));
            return;
        }

        // Create attachment
        $attachment = array(
            'post_mime_type' => wp_check_filetype($destination_path)['type'],
            'post_title' => sanitize_file_name($file_info['filename']),
            'post_content' => '',
            'post_status' => 'inherit'
        );

        $attachment_id = wp_insert_attachment($attachment, $destination_path, $post_id);

        if (!is_wp_error($attachment_id)) {
            require_once(ABSPATH . 'wp-admin/includes/image.php');
            $attachment_data = wp_generate_attachment_metadata($attachment_id, $destination_path);
            wp_update_attachment_metadata($attachment_id, $attachment_data);
            set_post_thumbnail($post_id, $attachment_id);

            $this->add_message(sprintf(__('Row %d: Image imported successfully from file - %s', 'hearing-aid-display'), $row_number, $unique_filename));
        } else {
            // Clean up copied file if attachment creation failed
            if (file_exists($destination_path)) {
                unlink($destination_path);
            }
            $this->add_warning(sprintf(__('Row %d: Failed to create image attachment - %s', 'hearing-aid-display'), $row_number, $attachment_id->get_error_message()));
        }
    }
    
    /**
     * Update import progress
     */
    private function update_progress($processed, $total) {
        // This could be used for real-time progress updates via AJAX
        $percentage = round(($processed / $total) * 100);
        
        // Store progress in transient for AJAX polling
        set_transient('hapd_import_progress_' . get_current_user_id(), array(
            'processed' => $processed,
            'total' => $total,
            'percentage' => $percentage,
        ), 300); // 5 minutes
    }
    
    /**
     * Add error message
     */
    private function add_error($message) {
        $this->import_results['errors']++;
        $this->import_results['messages'][] = array(
            'type' => 'error',
            'message' => $message,
        );
    }
    
    /**
     * Add warning message
     */
    private function add_warning($message) {
        $this->import_results['warnings']++;
        $this->import_results['messages'][] = array(
            'type' => 'warning',
            'message' => $message,
        );
    }
    
    /**
     * Add info message
     */
    private function add_message($message) {
        $this->import_results['messages'][] = array(
            'type' => 'info',
            'message' => $message,
        );
    }
    
    /**
     * Get import results
     */
    public function get_import_results() {
        return $this->import_results;
    }
    
    /**
     * Reset import results
     */
    public function reset_import_results() {
        $this->import_results = array(
            'imported' => 0,
            'errors' => 0,
            'warnings' => 0,
            'messages' => array(),
        );
    }
}

// Initialize Excel importer
if (is_admin()) {
    new HAPD_Excel_Importer();
}
