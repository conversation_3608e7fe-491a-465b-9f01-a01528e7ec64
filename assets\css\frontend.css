/**
 * Hearing Aid Product Display - Frontend Styles
 * Professional, accessible, and responsive styling for hearing aid products
 */

/* ==========================================================================
   Base Styles
   ========================================================================== */

.hapd-products-container {
    margin: 2rem 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.hapd-no-products {
    text-align: center;
    padding: 3rem 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;
    font-size: 1.1rem;
}

/* ==========================================================================
   Grid Layout
   ========================================================================== */

.hapd-products-grid {
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.hapd-columns-1 { grid-template-columns: 1fr; }
.hapd-columns-2 { grid-template-columns: repeat(2, 1fr); }
.hapd-columns-3 { grid-template-columns: repeat(3, 1fr); }
.hapd-columns-4 { grid-template-columns: repeat(4, 1fr); }
.hapd-columns-5 { grid-template-columns: repeat(5, 1fr); }
.hapd-columns-6 { grid-template-columns: repeat(6, 1fr); }

/* Responsive grid adjustments */
@media (max-width: 1200px) {
    .hapd-columns-5,
    .hapd-columns-6 { grid-template-columns: repeat(4, 1fr); }
}

@media (max-width: 992px) {
    .hapd-columns-4,
    .hapd-columns-5,
    .hapd-columns-6 { grid-template-columns: repeat(3, 1fr); }
}

@media (max-width: 768px) {
    .hapd-columns-3,
    .hapd-columns-4,
    .hapd-columns-5,
    .hapd-columns-6 { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 480px) {
    .hapd-products-grid { grid-template-columns: 1fr; }
}

/* ==========================================================================
   Product Cards
   ========================================================================== */

.hapd-product-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.hapd-product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #007cba;
}

.hapd-product-image {
    position: relative;
    overflow: hidden;
    background: #f8f9fa;
    aspect-ratio: 1;
}

.hapd-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.hapd-product-card:hover .hapd-product-image img {
    transform: scale(1.05);
}

.hapd-product-image a {
    display: block;
    width: 100%;
    height: 100%;
}

.hapd-availability-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hapd-availability-in-stock {
    background: #d4edda;
    color: #155724;
}

.hapd-availability-out-of-stock {
    background: #f8d7da;
    color: #721c24;
}

.hapd-availability-contact-for-availability {
    background: #fff3cd;
    color: #856404;
}

.hapd-product-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.hapd-product-title {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.3;
}

.hapd-product-title a {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.3s ease;
}

.hapd-product-title a:hover {
    color: #007cba;
}

.hapd-product-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #007cba;
    margin-bottom: 1rem;
}

.hapd-product-excerpt {
    margin-bottom: 1.5rem;
    color: #6c757d;
    line-height: 1.6;
    flex: 1;
}

.hapd-product-excerpt p {
    margin: 0;
}

.hapd-product-features,
.hapd-product-specs-preview {
    margin-bottom: 1.5rem;
}

.hapd-product-features h4,
.hapd-product-specs-preview h4 {
    margin: 0 0 0.75rem 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hapd-product-features ul,
.hapd-product-specs-preview ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.hapd-product-features li,
.hapd-product-specs-preview li {
    padding: 0.25rem 0;
    font-size: 0.9rem;
    color: #6c757d;
    position: relative;
    padding-left: 1.2rem;
}

.hapd-product-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

.hapd-product-actions {
    margin-top: auto;
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* ==========================================================================
   List Layout
   ========================================================================== */

.hapd-products-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.hapd-product-list-item {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.hapd-product-list-item:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #007cba;
}

.hapd-product-list-item .hapd-product-image {
    width: 200px;
    flex-shrink: 0;
    aspect-ratio: 1;
}

.hapd-product-list-item .hapd-product-content {
    flex: 1;
    padding: 2rem;
}

.hapd-product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
}

.hapd-product-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
    .hapd-product-list-item {
        flex-direction: column;
    }
    
    .hapd-product-list-item .hapd-product-image {
        width: 100%;
        aspect-ratio: 16/9;
    }
    
    .hapd-product-details {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .hapd-product-header {
        flex-direction: column;
        align-items: flex-start;
    }
}

/* ==========================================================================
   Carousel Layout
   ========================================================================== */

.hapd-products-carousel {
    position: relative;
    overflow: hidden;
}

.hapd-carousel-container {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
}

.hapd-carousel-track {
    display: flex;
    transition: transform 0.3s ease;
}

.hapd-carousel-slide {
    flex: 0 0 auto;
    width: calc(100% / 3);
    padding: 0 1rem;
}

.hapd-carousel-prev,
.hapd-carousel-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #dee2e6;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #495057;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.hapd-carousel-prev:hover,
.hapd-carousel-next:hover {
    background: #ffffff;
    color: #007cba;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.hapd-carousel-prev {
    left: 1rem;
}

.hapd-carousel-next {
    right: 1rem;
}

.hapd-carousel-dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

.hapd-carousel-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #dee2e6;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.hapd-carousel-dot.active {
    background: #007cba;
}

@media (max-width: 992px) {
    .hapd-carousel-slide {
        width: calc(100% / 2);
    }
}

@media (max-width: 576px) {
    .hapd-carousel-slide {
        width: 100%;
    }
}

/* ==========================================================================
   Buttons
   ========================================================================== */

.hapd-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    line-height: 1;
    min-height: 44px; /* Accessibility: minimum touch target */
}

.hapd-btn-primary {
    background: #007cba;
    color: #ffffff;
}

.hapd-btn-primary:hover {
    background: #005a87;
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 124, 186, 0.3);
}

.hapd-btn-secondary {
    background: #ffffff;
    color: #007cba;
    border: 2px solid #007cba;
}

.hapd-btn-secondary:hover {
    background: #007cba;
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 124, 186, 0.3);
}

.hapd-contact-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.hapd-contact-email,
.hapd-contact-phone {
    flex: 1;
    min-width: 120px;
}

/* ==========================================================================
   Single Product Page
   ========================================================================== */

.hapd-single-product {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin: 2rem 0;
}

.hapd-product-gallery {
    position: sticky;
    top: 2rem;
    height: fit-content;
}

.hapd-gallery-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.hapd-main-gallery {
    border-radius: 12px;
    overflow: hidden;
    background: #f8f9fa;
    aspect-ratio: 1;
}

.hapd-main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: zoom-in;
}

.hapd-gallery-thumbnails {
    display: flex;
    gap: 0.75rem;
    overflow-x: auto;
    padding: 0.5rem 0;
}

.hapd-thumb-container {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.hapd-thumb-container:hover,
.hapd-thumb-container.active {
    border-color: #007cba;
}

.hapd-gallery-thumb {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hapd-product-info {
    padding: 1rem 0;
}

.hapd-product-info .hapd-product-header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.hapd-product-info .hapd-product-title {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.hapd-product-info .hapd-product-price {
    font-size: 2.5rem;
    margin: 0;
}

.hapd-product-details {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.hapd-product-details p {
    margin: 0.5rem 0;
    display: flex;
    justify-content: space-between;
}

.hapd-product-description {
    margin-bottom: 2rem;
    line-height: 1.7;
}

.hapd-product-features,
.hapd-product-specifications {
    margin-bottom: 2rem;
}

.hapd-product-features h3,
.hapd-product-specifications h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
    font-size: 1.5rem;
}

.hapd-product-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.hapd-product-features li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
    position: relative;
    padding-left: 2rem;
}

.hapd-product-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
    font-size: 1.2rem;
}

.hapd-specs-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.hapd-specs-table tr {
    border-bottom: 1px solid #e9ecef;
}

.hapd-specs-table td {
    padding: 1rem 0;
    vertical-align: top;
}

.hapd-specs-table td:first-child {
    width: 40%;
    font-weight: 600;
    color: #495057;
}

.hapd-contact-section {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
}

.hapd-contact-section h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.hapd-contact-section p {
    margin-bottom: 1.5rem;
    color: #6c757d;
}

@media (max-width: 992px) {
    .hapd-single-product {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .hapd-product-gallery {
        position: static;
    }
}

/* ==========================================================================
   Accessibility
   ========================================================================== */

/* Focus styles for keyboard navigation */
.hapd-btn:focus,
.hapd-product-title a:focus,
.hapd-gallery-thumb:focus,
.hapd-carousel-prev:focus,
.hapd-carousel-next:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* Screen reader only text */
.hapd-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .hapd-product-card {
        border-width: 2px;
    }
    
    .hapd-btn {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .hapd-product-card,
    .hapd-btn,
    .hapd-carousel-track,
    .hapd-product-image img {
        transition: none;
    }
    
    .hapd-product-card:hover {
        transform: none;
    }
    
    .hapd-product-card:hover .hapd-product-image img {
        transform: none;
    }
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.hapd-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #6c757d;
}

.hapd-loading .components-spinner {
    margin-bottom: 1rem;
}

/* ==========================================================================
   Error States
   ========================================================================== */

.hapd-error {
    background: #f8d7da;
    color: #721c24;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
}

.hapd-error p {
    margin-bottom: 1rem;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 576px) {
    .hapd-products-container {
        margin: 1rem 0;
    }

    .hapd-products-grid {
        gap: 1rem;
    }

    .hapd-product-card .hapd-product-content {
        padding: 1rem;
    }

    .hapd-product-actions {
        flex-direction: column;
    }

    .hapd-btn {
        width: 100%;
    }

    .hapd-contact-buttons {
        flex-direction: column;
    }

    .hapd-carousel-prev,
    .hapd-carousel-next {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .hapd-carousel-prev {
        left: 0.5rem;
    }

    .hapd-carousel-next {
        right: 0.5rem;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .hapd-carousel-prev,
    .hapd-carousel-next,
    .hapd-carousel-dots,
    .hapd-product-actions {
        display: none !important;
    }

    .hapd-product-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }

    .hapd-single-product {
        grid-template-columns: 1fr;
    }
}

/* ==========================================================================
   Modal Styles
   ========================================================================== */

.hapd-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.hapd-modal {
    background: #fff;
    border-radius: 12px;
    max-width: 900px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
}

.hapd-modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.hapd-modal-title {
    margin: 0;
    font-size: 1.5em;
    color: #333;
    font-weight: 600;
}

.hapd-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 5px;
    line-height: 1;
    border-radius: 4px;
    transition: all 0.2s ease;
    min-width: 44px;
    min-height: 44px;
}

.hapd-modal-close:hover {
    background: #e9ecef;
    color: #333;
}

.hapd-modal-body {
    padding: 0;
    max-height: calc(90vh - 80px);
    overflow-y: auto;
}

.hapd-modal-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0;
    min-height: 400px;
}

.hapd-modal-image {
    padding: 25px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hapd-modal-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.hapd-modal-info {
    padding: 25px;
    overflow-y: auto;
}

.hapd-modal-info .hapd-modal-header h2 {
    margin: 0 0 10px 0;
    font-size: 1.4em;
    color: #333;
}

.hapd-modal-price {
    font-size: 1.3em;
    font-weight: bold;
    color: #007cba;
    margin-bottom: 15px;
}

.hapd-modal-details {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.hapd-modal-details p {
    margin: 5px 0;
    font-size: 0.95em;
}

.hapd-modal-description,
.hapd-modal-specifications,
.hapd-modal-accessories,
.hapd-modal-features {
    margin-bottom: 25px;
}

.hapd-modal-description h3,
.hapd-modal-specifications h3,
.hapd-modal-accessories h3,
.hapd-modal-features h3 {
    margin: 0 0 12px 0;
    font-size: 1.1em;
    color: #007cba;
    border-bottom: 2px solid #007cba;
    padding-bottom: 5px;
}

.hapd-modal-specifications .hapd-specs-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.hapd-modal-specifications .hapd-specs-table td {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
    vertical-align: top;
}

.hapd-modal-specifications .hapd-specs-table td:first-child {
    font-weight: bold;
    width: 40%;
    color: #333;
}

.hapd-modal-specifications .hapd-specs-table tr:hover {
    background-color: #f5f5f5;
}

.hapd-modal-accessories ul,
.hapd-modal-features ul {
    margin: 10px 0;
    padding-left: 20px;
}

.hapd-modal-accessories li,
.hapd-modal-features li {
    margin-bottom: 5px;
    line-height: 1.4;
}

.hapd-modal-contact {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.hapd-modal-contact h3 {
    margin: 0 0 10px 0;
    color: #007cba;
}

.hapd-modal-contact p {
    margin: 0 0 15px 0;
    color: #666;
}

.hapd-modal-actions {
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.hapd-modal-loading {
    text-align: center;
    padding: 60px 20px;
}

.hapd-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007cba;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: hapd-spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes hapd-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hapd-modal-error {
    text-align: center;
    padding: 40px 20px;
    color: #d63384;
}

/* Prevent body scroll when modal is open */
body.hapd-modal-open {
    overflow: hidden;
}

/* Responsive modal */
@media (max-width: 768px) {
    .hapd-modal-overlay {
        padding: 10px;
    }

    .hapd-modal {
        max-height: 95vh;
    }

    .hapd-modal-content {
        grid-template-columns: 1fr;
    }

    .hapd-modal-image {
        order: 1;
        padding: 20px;
    }

    .hapd-modal-info {
        order: 2;
        padding: 20px;
    }

    .hapd-modal-header {
        padding: 15px 20px;
    }

    .hapd-modal-title {
        font-size: 1.3em;
    }
}

@media (max-width: 480px) {
    .hapd-modal-overlay {
        padding: 5px;
    }

    .hapd-modal-header {
        padding: 12px 15px;
    }

    .hapd-modal-image,
    .hapd-modal-info {
        padding: 15px;
    }

    .hapd-modal-title {
        font-size: 1.2em;
    }
}
