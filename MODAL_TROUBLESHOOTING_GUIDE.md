# 🔧 Modal AJAX "Bad Request" Troubleshooting Guide

## 🎯 **Quick Fix Steps**

### **Step 1: Test the Debug Files**
1. **Upload `test-modal-ajax.php`** to your WordPress root directory
2. **Access it via browser:** `http://yoursite.com/test-modal-ajax.php`
3. **Check all test results** - this will identify the exact issue

### **Step 2: Browser Console Debug**
1. **Open browser console** (F12 → Console tab)
2. **Copy and paste the contents of `modal-debug.js`** into console
3. **Run the debug commands** to test functionality

### **Step 3: Check WordPress Error Log**
- **Location:** Usually in `/wp-content/debug.log` or server error logs
- **Look for:** Messages starting with "HAPD Modal"

---

## 🔍 **Common Issues & Solutions**

### **Issue 1: "hapd_ajax is not defined"**
**Cause:** JavaScript localization not working
**Solution:**
```php
// Check if this code exists in includes/class-frontend.php around line 57:
wp_localize_script('hapd-frontend', 'hapd_ajax', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('hapd_ajax_nonce')
));
```

### **Issue 2: "AJAX action not registered"**
**Cause:** Frontend class not initialized properly
**Solution:** Verify these lines exist in `hearing-aid-product-display.php`:
```php
// Around line 76:
add_action('init', array($this, 'init_frontend'));

// Around line 153:
public function init_frontend() {
    if (!class_exists('HAPD_Frontend')) {
        require_once HAPD_PLUGIN_DIR . 'includes/class-frontend.php';
    }
    $this->frontend = new HAPD_Frontend();
}
```

### **Issue 3: "Security check failed"**
**Cause:** Nonce mismatch
**Solution:** Clear browser cache and refresh page

### **Issue 4: "Product not found"**
**Cause:** Invalid product ID or product doesn't exist
**Solution:** Check if products exist and are published

---

## 🛠️ **Manual Testing Commands**

### **In Browser Console:**
```javascript
// Test if everything is loaded
console.log('hapd_ajax:', typeof hapd_ajax !== 'undefined' ? hapd_ajax : 'NOT DEFINED');
console.log('HearingAidDisplay:', typeof HearingAidDisplay !== 'undefined' ? 'DEFINED' : 'NOT DEFINED');

// Test modal with product ID (replace 123 with actual product ID)
testModal(123);

// Test AJAX directly
testAjax(123);
```

### **In WordPress Admin → Tools → Site Health → Info:**
Check for JavaScript errors and plugin conflicts

---

## 🔧 **Step-by-Step Debugging**

### **1. Verify Plugin Structure**
```
hearing-aid-product-display/
├── hearing-aid-product-display.php ✓
├── includes/
│   └── class-frontend.php ✓
├── assets/
│   ├── js/frontend.js ✓
│   └── css/frontend.css ✓
```

### **2. Check WordPress Hooks**
Run this in WordPress admin → Tools → Site Health → Info → Debug:
```php
// Check if AJAX actions are registered
global $wp_filter;
var_dump(isset($wp_filter['wp_ajax_hapd_get_product_modal']));
var_dump(isset($wp_filter['wp_ajax_nopriv_hapd_get_product_modal']));
```

### **3. Test AJAX Endpoint Manually**
```bash
curl -X POST "http://yoursite.com/wp-admin/admin-ajax.php" \
  -d "action=hapd_get_product_modal&product_id=123&nonce=YOUR_NONCE"
```

---

## 🚨 **Emergency Fixes**

### **Fix 1: Force Frontend Initialization**
Add this to your theme's `functions.php`:
```php
add_action('init', function() {
    if (class_exists('HearingAidProductDisplay')) {
        $plugin = HearingAidProductDisplay::get_instance();
        if (method_exists($plugin, 'init_frontend')) {
            $plugin->init_frontend();
        }
    }
}, 20);
```

### **Fix 2: Manual AJAX Registration**
Add this to your theme's `functions.php`:
```php
add_action('wp_ajax_hapd_get_product_modal', function() {
    if (class_exists('HAPD_Frontend')) {
        $frontend = new HAPD_Frontend();
        $frontend->ajax_get_product_modal();
    }
});
add_action('wp_ajax_nopriv_hapd_get_product_modal', function() {
    if (class_exists('HAPD_Frontend')) {
        $frontend = new HAPD_Frontend();
        $frontend->ajax_get_product_modal();
    }
});
```

### **Fix 3: Force Script Localization**
Add this to your theme's `functions.php`:
```php
add_action('wp_enqueue_scripts', function() {
    if (wp_script_is('hapd-frontend', 'enqueued')) {
        wp_localize_script('hapd-frontend', 'hapd_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('hapd_ajax_nonce')
        ));
    }
}, 20);
```

---

## 📞 **Next Steps**

1. **Run the test file** (`test-modal-ajax.php`) first
2. **Check browser console** for JavaScript errors
3. **Review WordPress error log** for PHP errors
4. **Try emergency fixes** if needed
5. **Report results** with specific error messages

The debug files will help identify exactly where the issue is occurring!
