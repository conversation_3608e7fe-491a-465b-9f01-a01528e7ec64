<?php
/**
 * AJAX Handler Class
 *
 * @package HearingAidProductDisplay
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AJAX Handler Class
 */
class HAPD_Ajax {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_ajax_hapd_get_products', array($this, 'get_products'));
        add_action('wp_ajax_nopriv_hapd_get_products', array($this, 'get_products'));
        
        add_action('wp_ajax_hapd_get_product_details', array($this, 'get_product_details'));
        add_action('wp_ajax_nopriv_hapd_get_product_details', array($this, 'get_product_details'));
        
        add_action('wp_ajax_hapd_contact_form', array($this, 'handle_contact_form'));
        add_action('wp_ajax_nopriv_hapd_contact_form', array($this, 'handle_contact_form'));
        
        add_action('wp_ajax_hapd_search_products', array($this, 'search_products'));
        add_action('wp_ajax_nopriv_hapd_search_products', array($this, 'search_products'));
        
        add_action('wp_ajax_hapd_filter_products', array($this, 'filter_products'));
        add_action('wp_ajax_nopriv_hapd_filter_products', array($this, 'filter_products'));
    }
    
    /**
     * Get products via AJAX
     */
    public function get_products() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'hapd_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }
        
        // Sanitize input parameters
        $search = sanitize_text_field($_POST['search'] ?? '');
        $category = sanitize_text_field($_POST['category'] ?? '');
        $brand = sanitize_text_field($_POST['brand'] ?? '');
        $orderby = sanitize_text_field($_POST['orderby'] ?? 'date');
        $order = sanitize_text_field($_POST['order'] ?? 'DESC');
        $posts_per_page = intval($_POST['posts_per_page'] ?? -1);
        $paged = intval($_POST['paged'] ?? 1);
        
        // Build query arguments
        $args = array(
            'post_type' => 'hearing_aid_product',
            'post_status' => 'publish',
            'posts_per_page' => $posts_per_page,
            'paged' => $paged,
            'orderby' => $orderby,
            'order' => $order,
        );
        
        // Add search parameter
        if (!empty($search)) {
            $args['s'] = $search;
        }
        
        // Add taxonomy filters
        $tax_query = array();
        
        if (!empty($category)) {
            $tax_query[] = array(
                'taxonomy' => 'hearing_aid_category',
                'field' => 'slug',
                'terms' => explode(',', $category),
            );
        }
        
        if (!empty($brand)) {
            $tax_query[] = array(
                'taxonomy' => 'hearing_aid_brand',
                'field' => 'slug',
                'terms' => explode(',', $brand),
            );
        }
        
        if (!empty($tax_query)) {
            $args['tax_query'] = $tax_query;
        }
        
        // Execute query
        $query = new WP_Query($args);
        $products = array();
        
        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $product_id = get_the_ID();
                
                $products[] = array(
                    'id' => $product_id,
                    'title' => get_the_title(),
                    'excerpt' => get_the_excerpt(),
                    'content' => get_the_content(),
                    'permalink' => get_permalink(),
                    'thumbnail' => get_the_post_thumbnail_url($product_id, 'hapd-product-thumbnail'),
                    'medium_image' => get_the_post_thumbnail_url($product_id, 'hapd-product-medium'),
                    'large_image' => get_the_post_thumbnail_url($product_id, 'hapd-product-large'),
                    'price' => get_post_meta($product_id, '_hapd_price', true),
                    'model_number' => get_post_meta($product_id, '_hapd_model_number', true),
                    'warranty' => get_post_meta($product_id, '_hapd_warranty', true),
                    'availability' => get_post_meta($product_id, '_hapd_availability', true),
                    'features' => get_post_meta($product_id, '_hapd_features', true),
                    'specifications' => get_post_meta($product_id, '_hapd_specifications', true),
                    'gallery_ids' => get_post_meta($product_id, '_hapd_gallery_ids', true),
                    'categories' => wp_get_post_terms($product_id, 'hearing_aid_category', array('fields' => 'names')),
                    'brands' => wp_get_post_terms($product_id, 'hearing_aid_brand', array('fields' => 'names')),
                );
            }
            wp_reset_postdata();
        }
        
        // Prepare response
        $response = array(
            'products' => $products,
            'total' => $query->found_posts,
            'pages' => $query->max_num_pages,
            'current_page' => $paged,
        );
        
        wp_send_json_success($response);
    }
    
    /**
     * Get single product details via AJAX
     */
    public function get_product_details() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'hapd_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }
        
        $product_id = intval($_POST['product_id'] ?? 0);
        
        if (!$product_id) {
            wp_send_json_error('Invalid product ID');
            return;
        }
        
        $product = get_post($product_id);
        
        if (!$product || $product->post_type !== 'hearing_aid_product' || $product->post_status !== 'publish') {
            wp_send_json_error('Product not found');
            return;
        }
        
        // Get product data
        $product_data = array(
            'id' => $product_id,
            'title' => $product->post_title,
            'content' => apply_filters('the_content', $product->post_content),
            'excerpt' => $product->post_excerpt,
            'permalink' => get_permalink($product_id),
            'price' => get_post_meta($product_id, '_hapd_price', true),
            'model_number' => get_post_meta($product_id, '_hapd_model_number', true),
            'warranty' => get_post_meta($product_id, '_hapd_warranty', true),
            'availability' => get_post_meta($product_id, '_hapd_availability', true),
            'features' => get_post_meta($product_id, '_hapd_features', true),
            'specifications' => get_post_meta($product_id, '_hapd_specifications', true),
            'gallery_ids' => get_post_meta($product_id, '_hapd_gallery_ids', true),
            'categories' => wp_get_post_terms($product_id, 'hearing_aid_category'),
            'brands' => wp_get_post_terms($product_id, 'hearing_aid_brand'),
        );
        
        // Get gallery images
        $gallery_ids = $product_data['gallery_ids'];
        $gallery_images = array();
        
        if (!empty($gallery_ids) && is_array($gallery_ids)) {
            foreach ($gallery_ids as $attachment_id) {
                if (wp_attachment_is_image($attachment_id)) {
                    $gallery_images[] = array(
                        'id' => $attachment_id,
                        'thumbnail' => wp_get_attachment_image_url($attachment_id, 'hapd-product-thumbnail'),
                        'medium' => wp_get_attachment_image_url($attachment_id, 'hapd-product-medium'),
                        'large' => wp_get_attachment_image_url($attachment_id, 'hapd-product-large'),
                        'full' => wp_get_attachment_image_url($attachment_id, 'full'),
                        'alt' => get_post_meta($attachment_id, '_wp_attachment_image_alt', true),
                        'caption' => wp_get_attachment_caption($attachment_id),
                    );
                }
            }
        }
        
        $product_data['gallery_images'] = $gallery_images;
        
        wp_send_json_success($product_data);
    }
    
    /**
     * Handle contact form submission
     */
    public function handle_contact_form() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'hapd_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }
        
        // Sanitize form data
        $name = sanitize_text_field($_POST['name'] ?? '');
        $email = sanitize_email($_POST['email'] ?? '');
        $phone = sanitize_text_field($_POST['phone'] ?? '');
        $product_id = intval($_POST['product_id'] ?? 0);
        $message = sanitize_textarea_field($_POST['message'] ?? '');
        $subject = sanitize_text_field($_POST['subject'] ?? '');
        
        // Validate required fields
        $errors = array();
        
        if (empty($name)) {
            $errors[] = __('Name is required', 'hearing-aid-display');
        }
        
        if (empty($email) || !is_email($email)) {
            $errors[] = __('Valid email is required', 'hearing-aid-display');
        }
        
        if (empty($message)) {
            $errors[] = __('Message is required', 'hearing-aid-display');
        }
        
        if (!empty($errors)) {
            wp_send_json_error(implode(', ', $errors));
            return;
        }
        
        // Get product information
        $product_title = '';
        if ($product_id) {
            $product = get_post($product_id);
            if ($product) {
                $product_title = $product->post_title;
            }
        }
        
        // Prepare email
        $settings = get_option('hapd_settings', array());
        $to_email = $settings['contact_email'] ?? get_option('admin_email');
        
        if (empty($subject)) {
            $subject = $product_title ? 
                sprintf(__('Inquiry about %s', 'hearing-aid-display'), $product_title) :
                __('Hearing Aid Product Inquiry', 'hearing-aid-display');
        }
        
        // Email content
        $email_content = sprintf(
            __("New inquiry from your website:\n\nName: %s\nEmail: %s\nPhone: %s\nProduct: %s\n\nMessage:\n%s\n\n---\nSent from: %s", 'hearing-aid-display'),
            $name,
            $email,
            $phone,
            $product_title,
            $message,
            home_url()
        );
        
        // Email headers
        $headers = array(
            'Content-Type: text/plain; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>',
            'Reply-To: ' . $name . ' <' . $email . '>',
        );
        
        // Send email
        $sent = wp_mail($to_email, $subject, $email_content, $headers);
        
        if ($sent) {
            // Log the inquiry (optional)
            $this->log_inquiry(array(
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'product_id' => $product_id,
                'message' => $message,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'timestamp' => current_time('mysql'),
            ));
            
            wp_send_json_success(__('Thank you for your inquiry. We will get back to you soon!', 'hearing-aid-display'));
        } else {
            wp_send_json_error(__('Sorry, there was an error sending your message. Please try again.', 'hearing-aid-display'));
        }
    }
    
    /**
     * Search products via AJAX
     */
    public function search_products() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'hapd_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }
        
        $search_term = sanitize_text_field($_POST['search'] ?? '');
        $limit = intval($_POST['limit'] ?? 10);
        
        if (empty($search_term)) {
            wp_send_json_success(array());
            return;
        }
        
        $args = array(
            'post_type' => 'hearing_aid_product',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            's' => $search_term,
            'orderby' => 'relevance',
        );
        
        $products = get_posts($args);
        $results = array();
        
        foreach ($products as $product) {
            $results[] = array(
                'id' => $product->ID,
                'title' => $product->post_title,
                'excerpt' => wp_trim_words($product->post_excerpt, 15),
                'permalink' => get_permalink($product->ID),
                'thumbnail' => get_the_post_thumbnail_url($product->ID, 'hapd-product-thumbnail'),
                'price' => get_post_meta($product->ID, '_hapd_price', true),
            );
        }
        
        wp_send_json_success($results);
    }
    
    /**
     * Filter products via AJAX
     */
    public function filter_products() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'hapd_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }
        
        // Get filter parameters
        $filters = array(
            'categories' => array_map('sanitize_text_field', $_POST['categories'] ?? array()),
            'brands' => array_map('sanitize_text_field', $_POST['brands'] ?? array()),
            'price_min' => floatval($_POST['price_min'] ?? 0),
            'price_max' => floatval($_POST['price_max'] ?? 0),
            'features' => array_map('sanitize_text_field', $_POST['features'] ?? array()),
        );
        
        $args = array(
            'post_type' => 'hearing_aid_product',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'meta_query' => array(),
            'tax_query' => array(),
        );
        
        // Add taxonomy filters
        if (!empty($filters['categories'])) {
            $args['tax_query'][] = array(
                'taxonomy' => 'hearing_aid_category',
                'field' => 'slug',
                'terms' => $filters['categories'],
            );
        }
        
        if (!empty($filters['brands'])) {
            $args['tax_query'][] = array(
                'taxonomy' => 'hearing_aid_brand',
                'field' => 'slug',
                'terms' => $filters['brands'],
            );
        }
        
        // Add price filter
        if ($filters['price_min'] > 0 || $filters['price_max'] > 0) {
            $price_query = array(
                'key' => '_hapd_price',
                'type' => 'NUMERIC',
                'compare' => 'BETWEEN',
            );
            
            if ($filters['price_min'] > 0 && $filters['price_max'] > 0) {
                $price_query['value'] = array($filters['price_min'], $filters['price_max']);
            } elseif ($filters['price_min'] > 0) {
                $price_query['value'] = $filters['price_min'];
                $price_query['compare'] = '>=';
            } elseif ($filters['price_max'] > 0) {
                $price_query['value'] = $filters['price_max'];
                $price_query['compare'] = '<=';
            }
            
            $args['meta_query'][] = $price_query;
        }
        
        $query = new WP_Query($args);
        $products = array();
        
        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $product_id = get_the_ID();
                
                // Filter by features if specified
                if (!empty($filters['features'])) {
                    $product_features = get_post_meta($product_id, '_hapd_features', true);
                    if (empty($product_features) || !is_array($product_features)) {
                        continue;
                    }
                    
                    $has_required_features = false;
                    foreach ($filters['features'] as $required_feature) {
                        foreach ($product_features as $product_feature) {
                            if (stripos($product_feature, $required_feature) !== false) {
                                $has_required_features = true;
                                break 2;
                            }
                        }
                    }
                    
                    if (!$has_required_features) {
                        continue;
                    }
                }
                
                $products[] = array(
                    'id' => $product_id,
                    'title' => get_the_title(),
                    'excerpt' => get_the_excerpt(),
                    'permalink' => get_permalink(),
                    'thumbnail' => get_the_post_thumbnail_url($product_id, 'hapd-product-thumbnail'),
                    'price' => get_post_meta($product_id, '_hapd_price', true),
                    'features' => get_post_meta($product_id, '_hapd_features', true),
                    'categories' => wp_get_post_terms($product_id, 'hearing_aid_category', array('fields' => 'names')),
                    'brands' => wp_get_post_terms($product_id, 'hearing_aid_brand', array('fields' => 'names')),
                );
            }
            wp_reset_postdata();
        }
        
        wp_send_json_success($products);
    }
    
    /**
     * Log inquiry to database (optional)
     */
    private function log_inquiry($data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'hapd_inquiries';
        
        // Create table if it doesn't exist
        $charset_collate = $wpdb->get_charset_collate();
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            name varchar(100) NOT NULL,
            email varchar(100) NOT NULL,
            phone varchar(20),
            product_id bigint(20),
            message text,
            ip_address varchar(45),
            user_agent text,
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Insert inquiry
        $wpdb->insert(
            $table_name,
            $data,
            array('%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s')
        );
    }
}

// Initialize AJAX handler
new HAPD_Ajax();
