/**
 * Hearing Aid Product Display - Block Editor Styles
 * Styles for the Gutenberg block editor interface
 */

/* ==========================================================================
   Block Container
   ========================================================================== */

.hapd-block-container {
    margin: 1rem 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ==========================================================================
   Block Preview
   ========================================================================== */

.hapd-block-preview {
    border: 2px dashed #e0e0e0;
    border-radius: 8px;
    padding: 2rem;
    background: #fafafa;
    text-align: center;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.hapd-preview-header {
    margin-bottom: 1.5rem;
}

.hapd-preview-header h3 {
    margin: 0 0 0.5rem 0;
    color: #1e1e1e;
    font-size: 1.25rem;
}

.hapd-preview-header p {
    margin: 0;
    color: #757575;
    font-size: 0.9rem;
}

.hapd-preview-products {
    display: grid;
    gap: 1rem;
    width: 100%;
    max-width: 800px;
}

.hapd-layout-grid .hapd-preview-products {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
}

.hapd-layout-list .hapd-preview-products {
    grid-template-columns: 1fr;
}

.hapd-layout-carousel .hapd-preview-products {
    grid-template-columns: repeat(3, 1fr);
    overflow: hidden;
}

.hapd-preview-product {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 1rem;
    text-align: left;
}

.hapd-preview-image {
    width: 100%;
    height: 80px;
    background: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hapd-preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hapd-preview-product h4 {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    color: #1e1e1e;
    line-height: 1.3;
}

.hapd-preview-product p {
    margin: 0;
    font-size: 0.8rem;
    color: #757575;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.hapd-preview-more {
    margin-top: 1rem;
    color: #757575;
    font-style: italic;
    font-size: 0.9rem;
}

/* ==========================================================================
   Inspector Controls
   ========================================================================== */

.hapd-product-selection {
    margin: 1rem 0;
}

.hapd-product-selection h4 {
    margin: 0 0 1rem 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #1e1e1e;
}

.hapd-product-checkboxes {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 0.5rem;
    background: #ffffff;
}

.hapd-product-checkbox {
    margin-bottom: 0.5rem;
}

.hapd-product-checkbox:last-child {
    margin-bottom: 0;
}

.hapd-product-checkbox .components-checkbox-control__label {
    font-size: 0.9rem;
    line-height: 1.4;
}

.hapd-selection-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
}

.hapd-selection-actions .components-button {
    font-size: 0.8rem;
    height: auto;
    padding: 0.5rem 1rem;
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.hapd-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #757575;
}

.hapd-loading .components-spinner {
    margin-bottom: 1rem;
}

.hapd-loading p {
    margin: 0;
    font-size: 0.9rem;
}

/* ==========================================================================
   Error States
   ========================================================================== */

.hapd-error {
    background: #f8d7da;
    color: #721c24;
    padding: 1.5rem;
    border-radius: 6px;
    text-align: center;
}

.hapd-error p {
    margin: 0 0 1rem 0;
    font-size: 0.9rem;
}

.hapd-error .components-button {
    background: #721c24;
    color: #ffffff;
    border-color: #721c24;
}

.hapd-error .components-button:hover {
    background: #5a161d;
    border-color: #5a161d;
}

/* ==========================================================================
   No Products State
   ========================================================================== */

.hapd-no-products {
    text-align: center;
    padding: 2rem;
    color: #757575;
}

.hapd-no-products p {
    margin: 0 0 1rem 0;
    font-size: 0.9rem;
}

.hapd-no-products a {
    color: #007cba;
    text-decoration: none;
    font-weight: 500;
}

.hapd-no-products a:hover {
    text-decoration: underline;
}

/* ==========================================================================
   Block Toolbar
   ========================================================================== */

.block-editor-block-toolbar .components-toolbar-group .components-button.is-active {
    background: #007cba;
    color: #ffffff;
}

.block-editor-block-toolbar .components-toolbar-group .components-button:hover {
    background: #005a87;
    color: #ffffff;
}

/* ==========================================================================
   Panel Body Customizations
   ========================================================================== */

.components-panel__body .components-range-control__wrapper {
    margin-bottom: 1rem;
}

.components-panel__body .components-toggle-control {
    margin-bottom: 1rem;
}

.components-panel__body .components-select-control {
    margin-bottom: 1rem;
}

/* ==========================================================================
   Responsive Preview
   ========================================================================== */

@media (max-width: 768px) {
    .hapd-layout-grid .hapd-preview-products,
    .hapd-layout-carousel .hapd-preview-products {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .hapd-layout-grid .hapd-preview-products,
    .hapd-layout-carousel .hapd-preview-products {
        grid-template-columns: 1fr;
    }
    
    .hapd-block-preview {
        padding: 1rem;
    }
    
    .hapd-selection-actions {
        flex-direction: column;
    }
}

/* ==========================================================================
   Accessibility
   ========================================================================== */

.hapd-product-checkbox .components-checkbox-control__input:focus + .components-checkbox-control__label {
    box-shadow: 0 0 0 2px #007cba;
    border-radius: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .hapd-block-preview {
        border-color: #000000;
        background: #ffffff;
    }
    
    .hapd-preview-product {
        border-color: #000000;
    }
    
    .hapd-product-checkboxes {
        border-color: #000000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .hapd-preview-product,
    .components-button {
        transition: none;
    }
}
