<?php
/**
 * Test Excel Import Functionality
 * 
 * This file can be used to test the Excel import functionality
 * Run this file from WordPress admin or via WP-CLI to test the import
 * 
 * @package HearingAidProductDisplay
 * @since 1.1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // If not in WordPress, try to load WordPress
    $wp_load_paths = array(
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php',
    );
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress not found. Please run this from WordPress admin or ensure wp-load.php is accessible.');
    }
}

// Only allow admin users to run this test
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

/**
 * Test Excel Import Class
 */
class HAPD_Excel_Import_Test {
    
    /**
     * Run all tests
     */
    public function run_tests() {
        echo "<h1>Hearing Aid Product Display - Excel Import Test</h1>\n";
        echo "<div style='font-family: monospace; background: #f0f0f0; padding: 20px; margin: 20px 0;'>\n";
        
        $this->test_plugin_loaded();
        $this->test_excel_library();
        $this->test_excel_importer_class();
        $this->test_sample_data_creation();
        $this->test_file_permissions();
        
        echo "</div>\n";
        echo "<h2>Test Complete</h2>\n";
        echo "<p>If all tests passed, the Excel import functionality should work correctly.</p>\n";
    }
    
    /**
     * Test if plugin is loaded
     */
    private function test_plugin_loaded() {
        echo "<h3>1. Testing Plugin Load</h3>\n";
        
        if (class_exists('HearingAidProductDisplay')) {
            echo "✅ Main plugin class loaded\n";
        } else {
            echo "❌ Main plugin class NOT loaded\n";
            return;
        }
        
        if (defined('HAPD_VERSION')) {
            echo "✅ Plugin constants defined (Version: " . HAPD_VERSION . ")\n";
        } else {
            echo "❌ Plugin constants NOT defined\n";
        }
        
        if (post_type_exists('hearing_aid_product')) {
            echo "✅ Custom post type registered\n";
        } else {
            echo "❌ Custom post type NOT registered\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test Excel library
     */
    private function test_excel_library() {
        echo "<h3>2. Testing Excel Library</h3>\n";
        
        $library_path = HAPD_PLUGIN_DIR . 'includes/libraries/SimpleXLSX.php';
        
        if (file_exists($library_path)) {
            echo "✅ SimpleXLSX library file exists\n";
        } else {
            echo "❌ SimpleXLSX library file NOT found at: $library_path\n";
            return;
        }
        
        require_once $library_path;
        
        if (class_exists('SimpleXLSX')) {
            echo "✅ SimpleXLSX class loaded\n";
        } else {
            echo "❌ SimpleXLSX class NOT loaded\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test Excel importer class
     */
    private function test_excel_importer_class() {
        echo "<h3>3. Testing Excel Importer Class</h3>\n";
        
        $importer_path = HAPD_PLUGIN_DIR . 'includes/admin/class-excel-importer.php';
        
        if (file_exists($importer_path)) {
            echo "✅ Excel importer file exists\n";
        } else {
            echo "❌ Excel importer file NOT found at: $importer_path\n";
            return;
        }
        
        if (class_exists('HAPD_Excel_Importer')) {
            echo "✅ HAPD_Excel_Importer class loaded\n";
        } else {
            echo "❌ HAPD_Excel_Importer class NOT loaded\n";
        }
        
        // Test AJAX hooks
        if (has_action('wp_ajax_hapd_excel_import')) {
            echo "✅ AJAX import hook registered\n";
        } else {
            echo "❌ AJAX import hook NOT registered\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test sample data creation
     */
    private function test_sample_data_creation() {
        echo "<h3>4. Testing Sample Data Creation</h3>\n";
        
        // Create a sample product to test the system
        $sample_product = array(
            'post_title' => 'Test Hearing Aid - Excel Import Test',
            'post_content' => 'This is a test product created by the Excel import test.',
            'post_type' => 'hearing_aid_product',
            'post_status' => 'draft',
        );
        
        $product_id = wp_insert_post($sample_product);
        
        if ($product_id && !is_wp_error($product_id)) {
            echo "✅ Sample product created (ID: $product_id)\n";
            
            // Add some meta data
            update_post_meta($product_id, '_hapd_model_number', 'TEST-001');
            update_post_meta($product_id, '_hapd_price', '999.99');
            update_post_meta($product_id, '_hapd_availability', 'in_stock');
            
            echo "✅ Sample product meta data added\n";
            
            // Clean up - delete the test product
            wp_delete_post($product_id, true);
            echo "✅ Sample product cleaned up\n";
            
        } else {
            echo "❌ Failed to create sample product\n";
            if (is_wp_error($product_id)) {
                echo "   Error: " . $product_id->get_error_message() . "\n";
            }
        }
        
        echo "\n";
    }
    
    /**
     * Test file permissions
     */
    private function test_file_permissions() {
        echo "<h3>5. Testing File Permissions</h3>\n";
        
        $upload_dir = wp_upload_dir();
        $base_upload_dir = $upload_dir['basedir'];
        
        if (is_writable($base_upload_dir)) {
            echo "✅ WordPress uploads directory is writable\n";
        } else {
            echo "❌ WordPress uploads directory is NOT writable: $base_upload_dir\n";
        }
        
        // Test hearing aid products directory
        $hapd_upload_dir = $base_upload_dir . '/hearing-aid-products';

        if (!file_exists($hapd_upload_dir)) {
            if (wp_mkdir_p($hapd_upload_dir)) {
                echo "✅ Created hearing-aid-products directory\n";
            } else {
                echo "❌ Failed to create hearing-aid-products directory\n";
            }
        } else {
            echo "✅ hearing-aid-products directory exists\n";
        }

        if (is_writable($hapd_upload_dir)) {
            echo "✅ hearing-aid-products directory is writable\n";
        } else {
            echo "❌ hearing-aid-products directory is NOT writable\n";
        }

        // Test creating a temporary file
        $test_file = $hapd_upload_dir . '/test-write-permissions.txt';
        if (file_put_contents($test_file, 'test')) {
            echo "✅ Can write files to hearing-aid-products directory\n";
            unlink($test_file); // Clean up
        } else {
            echo "❌ Cannot write files to hearing-aid-products directory\n";
        }

        // Test upload limits for large files
        $wp_max_upload = wp_max_upload_size();
        $target_size = 300 * 1024 * 1024; // 300MB

        echo "\n<strong>Upload Limits Check:</strong>\n";
        echo "WordPress max upload size: " . round($wp_max_upload / (1024 * 1024)) . "MB\n";
        echo "Target Excel import limit: 300MB\n";

        if ($wp_max_upload >= $target_size) {
            echo "✅ WordPress upload limit supports 300MB Excel files\n";
        } else {
            echo "⚠️  WordPress upload limit is lower than 300MB - may need server configuration\n";
        }
        
        echo "\n";
    }
    
    /**
     * Generate sample Excel data for testing
     */
    public function generate_sample_excel_data() {
        echo "<h3>Sample Excel Data Structure</h3>\n";
        echo "<p>Here's what your Excel file should look like:</p>\n";
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; margin: 20px 0;'>\n";
        echo "<tr style='background: #f0f0f0; font-weight: bold;'>\n";
        echo "<td>A - No.</td><td>B - Model No.</td><td>C - Photo</td><td>D - Packaging</td><td>E - Accessories</td><td>F - Description</td><td>G - Specification</td><td>H - Measurement</td><td>I - Price</td>\n";
        echo "</tr>\n";
        
        $sample_data = array(
            array('1', 'HA-2000', 'hearing-aid-1.jpg', 'Premium box', 'Charger|Cleaning kit|Manual', 'Advanced digital hearing aid', 'Frequency: 20Hz-8kHz|Channels: 16', '15 x 10 x 8 mm', '1299.99'),
            array('2', 'BTE-Pro', 'hearing-aid-2.jpg', 'Standard box', 'Batteries|Wax guards', 'Behind-the-ear hearing aid', 'Frequency: 100Hz-6kHz|Channels: 8', '20 x 12 x 10 mm', '899.99'),
            array('3', 'ITC-Mini', 'hearing-aid-3.jpg', 'Compact case', 'Cleaning tool|Storage case', 'In-the-canal hearing aid', 'Frequency: 200Hz-5kHz|Channels: 4', '12 x 8 x 6 mm', '599.99'),
        );
        
        foreach ($sample_data as $row) {
            echo "<tr>\n";
            foreach ($row as $cell) {
                echo "<td>" . htmlspecialchars($cell) . "</td>";
            }
            echo "</tr>\n";
        }
        
        echo "</table>\n";
        
        echo "<p><strong>Instructions:</strong></p>\n";
        echo "<ol>\n";
        echo "<li>Create an Excel file with the headers shown above</li>\n";
        echo "<li>Add your product data in the same format</li>\n";
        echo "<li>Save as .xlsx or .xls format</li>\n";
        echo "<li>Go to Hearing Aids > Import/Export to upload</li>\n";
        echo "</ol>\n";
    }
}

// Run the test if accessed directly
if (isset($_GET['run_test']) || (defined('WP_CLI') && WP_CLI)) {
    $test = new HAPD_Excel_Import_Test();
    $test->run_tests();
    
    if (isset($_GET['show_sample'])) {
        $test->generate_sample_excel_data();
    }
} else {
    // Show test options
    echo "<h1>Hearing Aid Product Display - Excel Import Test</h1>\n";
    echo "<p>This test will verify that the Excel import functionality is working correctly.</p>\n";
    echo "<p><a href='?run_test=1' class='button button-primary'>Run Tests</a></p>\n";
    echo "<p><a href='?run_test=1&show_sample=1' class='button'>Run Tests + Show Sample Data</a></p>\n";
    echo "<hr>\n";
    echo "<h2>Manual Testing Steps</h2>\n";
    echo "<ol>\n";
    echo "<li>Click 'Run Tests' above to verify system compatibility</li>\n";
    echo "<li>Go to <strong>Hearing Aids > Import/Export</strong> in WordPress admin</li>\n";
    echo "<li>Create an Excel file with the required 9 columns</li>\n";
    echo "<li>Upload and test the import functionality</li>\n";
    echo "</ol>\n";
}
?>
