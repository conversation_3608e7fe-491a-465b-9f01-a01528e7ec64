/**
 * Excel Import JavaScript
 * 
 * @package HearingAidProductDisplay
 * @since 1.1.0
 */

(function($) {
    'use strict';

    var ExcelImporter = {
        
        /**
         * Initialize Excel import functionality
         */
        init: function() {
            this.bindEvents();
            this.setupProgressModal();
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            $(document).on('click', '#hapd-excel-import-btn', this.handleExcelImport);
            $(document).on('change', '#hapd-excel-file-input', this.validateExcelFile);
            $(document).on('click', '#hapd-close-progress-modal', this.closeProgressModal);
            $(document).on('click', '.hapd-progress-modal-overlay', this.closeProgressModal);
        },
        
        /**
         * Setup progress modal
         */
        setupProgressModal: function() {
            if ($('#hapd-progress-modal').length === 0) {
                var modalHtml = `
                    <div id="hapd-progress-modal" class="hapd-progress-modal" style="display: none;">
                        <div class="hapd-progress-modal-overlay"></div>
                        <div class="hapd-progress-modal-content">
                            <div class="hapd-progress-modal-header">
                                <h3>${hapdExcelImport.strings.processing}</h3>
                                <button id="hapd-close-progress-modal" class="hapd-close-btn">&times;</button>
                            </div>
                            <div class="hapd-progress-modal-body">
                                <div class="hapd-progress-bar-container">
                                    <div class="hapd-progress-bar">
                                        <div class="hapd-progress-bar-fill" style="width: 0%;"></div>
                                    </div>
                                    <div class="hapd-progress-text">0%</div>
                                </div>
                                <div class="hapd-progress-status">
                                    <p id="hapd-progress-message">${hapdExcelImport.strings.processing}</p>
                                </div>
                                <div class="hapd-progress-results" style="display: none;">
                                    <div class="hapd-results-summary"></div>
                                    <div class="hapd-results-messages"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                $('body').append(modalHtml);
            }
        },
        
        /**
         * Validate Excel file
         */
        validateExcelFile: function() {
            var fileInput = $(this);
            var file = fileInput[0].files[0];
            var submitBtn = $('#hapd-excel-import-btn');
            var errorDiv = $('#hapd-excel-file-error');
            
            // Remove existing error messages
            errorDiv.remove();
            
            if (!file) {
                submitBtn.prop('disabled', true);
                return;
            }
            
            // Check file extension
            var fileName = file.name.toLowerCase();
            var validExtensions = ['.xlsx', '.xls'];
            var isValidExtension = validExtensions.some(function(ext) {
                return fileName.endsWith(ext);
            });
            
            if (!isValidExtension) {
                fileInput.after('<div id="hapd-excel-file-error" class="error notice notice-error"><p>Please select an Excel file (.xlsx or .xls)</p></div>');
                submitBtn.prop('disabled', true);
                return;
            }
            
            // Check file size (max 300MB)
            var maxSize = 300 * 1024 * 1024; // 300MB
            if (file.size > maxSize) {
                fileInput.after('<div id="hapd-excel-file-error" class="error notice notice-error"><p>File size must be less than 300MB. Large files may take several minutes to process.</p></div>');
                submitBtn.prop('disabled', true);
                return;
            }

            // Show warning for large files (over 50MB)
            var warningSize = 50 * 1024 * 1024; // 50MB
            if (file.size > warningSize) {
                var fileSizeMB = Math.round(file.size / (1024 * 1024));
                fileInput.after('<div id="hapd-excel-file-warning" class="notice notice-warning"><p><strong>Large File Warning:</strong> Your file is ' + fileSizeMB + 'MB. Processing may take several minutes. Please be patient and do not close this page during import.</p></div>');
            }
            
            // File is valid
            submitBtn.prop('disabled', false);
        },
        
        /**
         * Handle Excel import
         */
        handleExcelImport: function(e) {
            e.preventDefault();
            
            var fileInput = $('#hapd-excel-file-input')[0];
            var file = fileInput.files[0];
            
            if (!file) {
                alert('Please select an Excel file to import.');
                return;
            }
            
            ExcelImporter.showProgressModal();
            ExcelImporter.startImport(file);
        },
        
        /**
         * Start import process
         */
        startImport: function(file) {
            var formData = new FormData();
            formData.append('action', 'hapd_excel_import');
            formData.append('nonce', hapdExcelImport.nonce);
            formData.append('excel_file', file);
            
            this.updateProgress(0, hapdExcelImport.strings.processing);
            
            $.ajax({
                url: hapdExcelImport.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    var xhr = new window.XMLHttpRequest();
                    
                    // Upload progress
                    xhr.upload.addEventListener('progress', function(evt) {
                        if (evt.lengthComputable) {
                            var percentComplete = Math.round((evt.loaded / evt.total) * 50); // Upload is 50% of total
                            ExcelImporter.updateProgress(percentComplete, 'Uploading file...');
                        }
                    }, false);
                    
                    return xhr;
                },
                success: function(response) {
                    if (response.success) {
                        ExcelImporter.handleImportSuccess(response.data);
                    } else {
                        ExcelImporter.handleImportError(response.data || 'Import failed');
                    }
                },
                error: function(xhr, status, error) {
                    ExcelImporter.handleImportError('Network error: ' + error);
                }
            });
        },
        
        /**
         * Handle import success
         */
        handleImportSuccess: function(data) {
            this.updateProgress(100, hapdExcelImport.strings.complete);
            
            // Show results
            var resultsHtml = this.buildResultsHtml(data);
            $('.hapd-results-summary').html(resultsHtml.summary);
            $('.hapd-results-messages').html(resultsHtml.messages);
            $('.hapd-progress-results').show();
            
            // Update close button text
            $('#hapd-close-progress-modal').text('Close');
            
            // Reset form
            $('#hapd-excel-file-input').val('');
            $('#hapd-excel-import-btn').prop('disabled', true);
        },
        
        /**
         * Handle import error
         */
        handleImportError: function(error) {
            this.updateProgress(0, hapdExcelImport.strings.error);
            
            var errorHtml = '<div class="hapd-error-message">';
            errorHtml += '<h4>Import Failed</h4>';
            errorHtml += '<p>' + error + '</p>';
            errorHtml += '</div>';
            
            $('.hapd-results-summary').html(errorHtml);
            $('.hapd-progress-results').show();
            
            // Update close button text
            $('#hapd-close-progress-modal').text('Close');
        },
        
        /**
         * Build results HTML
         */
        buildResultsHtml: function(data) {
            var summary = '<div class="hapd-import-summary">';
            summary += '<h4>Import Complete</h4>';
            summary += '<div class="hapd-summary-stats">';
            summary += '<div class="hapd-stat hapd-stat-success">';
            summary += '<span class="hapd-stat-number">' + data.imported + '</span>';
            summary += '<span class="hapd-stat-label">Products Imported</span>';
            summary += '</div>';
            
            if (data.errors > 0) {
                summary += '<div class="hapd-stat hapd-stat-error">';
                summary += '<span class="hapd-stat-number">' + data.errors + '</span>';
                summary += '<span class="hapd-stat-label">Errors</span>';
                summary += '</div>';
            }
            
            if (data.warnings > 0) {
                summary += '<div class="hapd-stat hapd-stat-warning">';
                summary += '<span class="hapd-stat-number">' + data.warnings + '</span>';
                summary += '<span class="hapd-stat-label">Warnings</span>';
                summary += '</div>';
            }
            
            summary += '</div>';
            summary += '</div>';
            
            var messages = '';
            if (data.messages && data.messages.length > 0) {
                messages += '<div class="hapd-import-messages">';
                messages += '<h4>Import Details</h4>';
                messages += '<div class="hapd-messages-list">';
                
                data.messages.forEach(function(message) {
                    var messageClass = 'hapd-message-' + message.type;
                    messages += '<div class="hapd-message ' + messageClass + '">';
                    messages += '<span class="hapd-message-icon"></span>';
                    messages += '<span class="hapd-message-text">' + message.message + '</span>';
                    messages += '</div>';
                });
                
                messages += '</div>';
                messages += '</div>';
            }
            
            return {
                summary: summary,
                messages: messages
            };
        },
        
        /**
         * Update progress
         */
        updateProgress: function(percentage, message) {
            $('.hapd-progress-bar-fill').css('width', percentage + '%');
            $('.hapd-progress-text').text(percentage + '%');
            $('#hapd-progress-message').text(message);
        },
        
        /**
         * Show progress modal
         */
        showProgressModal: function() {
            $('#hapd-progress-modal').fadeIn(300);
            $('body').addClass('hapd-modal-open');
            
            // Reset modal state
            this.updateProgress(0, hapdExcelImport.strings.processing);
            $('.hapd-progress-results').hide();
            $('#hapd-close-progress-modal').text('Cancel');
        },
        
        /**
         * Close progress modal
         */
        closeProgressModal: function() {
            $('#hapd-progress-modal').fadeOut(300);
            $('body').removeClass('hapd-modal-open');
        },
        
        /**
         * Poll for progress updates (if needed for large imports)
         */
        pollProgress: function() {
            $.ajax({
                url: hapdExcelImport.ajax_url,
                type: 'POST',
                data: {
                    action: 'hapd_get_import_progress',
                    nonce: hapdExcelImport.nonce
                },
                success: function(response) {
                    if (response.success && response.data) {
                        var progress = response.data;
                        var percentage = progress.percentage || 0;
                        var message = 'Processing... (' + progress.processed + '/' + progress.total + ')';
                        
                        ExcelImporter.updateProgress(percentage, message);
                        
                        // Continue polling if not complete
                        if (percentage < 100) {
                            setTimeout(function() {
                                ExcelImporter.pollProgress();
                            }, 1000);
                        }
                    }
                }
            });
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        ExcelImporter.init();
    });
    
    // Make ExcelImporter globally available
    window.HAPDExcelImporter = ExcelImporter;
    
})(jQuery);
