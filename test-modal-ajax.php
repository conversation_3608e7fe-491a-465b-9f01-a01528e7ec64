<?php
/**
 * Test Modal AJAX Functionality
 * 
 * This file helps debug the modal AJAX issues.
 * Place this in your WordPress root directory and access it via browser.
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Check if we're in WordPress
if (!function_exists('wp_verify_nonce')) {
    die('WordPress not loaded properly');
}

echo "<h1>HAPD Modal AJAX Debug Test</h1>";

// Test 1: Check if plugin is active
echo "<h2>1. Plugin Status</h2>";
if (class_exists('HearingAidProductDisplay')) {
    echo "✅ Plugin class exists<br>";
} else {
    echo "❌ Plugin class not found<br>";
}

// Test 2: Check if frontend class exists
echo "<h2>2. Frontend Class Status</h2>";
if (class_exists('HAPD_Frontend')) {
    echo "✅ Frontend class exists<br>";
} else {
    echo "❌ Frontend class not found<br>";
    // Try to include it
    $frontend_path = WP_PLUGIN_DIR . '/hearing-aid-product-display/includes/class-frontend.php';
    if (file_exists($frontend_path)) {
        require_once($frontend_path);
        if (class_exists('HAPD_Frontend')) {
            echo "✅ Frontend class loaded manually<br>";
        } else {
            echo "❌ Frontend class still not available after manual load<br>";
        }
    } else {
        echo "❌ Frontend class file not found at: $frontend_path<br>";
    }
}

// Test 3: Check if AJAX actions are registered
echo "<h2>3. AJAX Actions</h2>";
global $wp_filter;
if (isset($wp_filter['wp_ajax_hapd_get_product_modal'])) {
    echo "✅ AJAX action 'wp_ajax_hapd_get_product_modal' is registered<br>";
} else {
    echo "❌ AJAX action 'wp_ajax_hapd_get_product_modal' is NOT registered<br>";
}

if (isset($wp_filter['wp_ajax_nopriv_hapd_get_product_modal'])) {
    echo "✅ AJAX action 'wp_ajax_nopriv_hapd_get_product_modal' is registered<br>";
} else {
    echo "❌ AJAX action 'wp_ajax_nopriv_hapd_get_product_modal' is NOT registered<br>";
}

// Test 4: Check for hearing aid products
echo "<h2>4. Sample Products</h2>";
$products = get_posts(array(
    'post_type' => 'hearing_aid_product',
    'post_status' => 'publish',
    'numberposts' => 5
));

if (!empty($products)) {
    echo "✅ Found " . count($products) . " hearing aid products:<br>";
    foreach ($products as $product) {
        echo "- ID: {$product->ID}, Title: {$product->post_title}<br>";
    }
} else {
    echo "❌ No hearing aid products found<br>";
}

// Test 5: Manual AJAX test
echo "<h2>5. Manual AJAX Test</h2>";
if (!empty($products)) {
    $test_product_id = $products[0]->ID;
    echo "Testing with product ID: $test_product_id<br>";
    
    // Create nonce
    $nonce = wp_create_nonce('hapd_ajax_nonce');
    echo "Generated nonce: $nonce<br>";
    
    // Simulate AJAX request
    $_POST['action'] = 'hapd_get_product_modal';
    $_POST['product_id'] = $test_product_id;
    $_POST['nonce'] = $nonce;
    
    echo "<h3>Simulating AJAX Request:</h3>";
    echo "Action: hapd_get_product_modal<br>";
    echo "Product ID: $test_product_id<br>";
    echo "Nonce: $nonce<br>";
    
    // Try to call the handler directly
    if (class_exists('HAPD_Frontend')) {
        $frontend = new HAPD_Frontend();
        if (method_exists($frontend, 'ajax_get_product_modal')) {
            echo "<h3>Direct Method Call Test:</h3>";
            ob_start();
            try {
                $frontend->ajax_get_product_modal();
            } catch (Exception $e) {
                echo "Exception: " . $e->getMessage() . "<br>";
            }
            $output = ob_get_clean();
            echo "Output: " . htmlspecialchars($output) . "<br>";
        } else {
            echo "❌ ajax_get_product_modal method not found<br>";
        }
    }
} else {
    echo "❌ Cannot test - no products available<br>";
}

// Test 6: JavaScript test
echo "<h2>6. JavaScript Test</h2>";
?>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
// Test AJAX call
function testAjaxCall() {
    console.log('Testing AJAX call...');
    
    var productId = <?php echo !empty($products) ? $products[0]->ID : 0; ?>;
    var nonce = '<?php echo wp_create_nonce('hapd_ajax_nonce'); ?>';
    var ajaxUrl = '<?php echo admin_url('admin-ajax.php'); ?>';
    
    console.log('Product ID:', productId);
    console.log('Nonce:', nonce);
    console.log('AJAX URL:', ajaxUrl);
    
    if (productId === 0) {
        console.log('No product ID available for testing');
        document.getElementById('ajax-result').innerHTML = '❌ No product available for testing';
        return;
    }
    
    jQuery.ajax({
        url: ajaxUrl,
        type: 'POST',
        data: {
            action: 'hapd_get_product_modal',
            product_id: productId,
            nonce: nonce
        },
        success: function(response) {
            console.log('Success:', response);
            document.getElementById('ajax-result').innerHTML = '✅ AJAX call successful: ' + JSON.stringify(response);
        },
        error: function(xhr, status, error) {
            console.log('Error:', xhr, status, error);
            console.log('Response text:', xhr.responseText);
            document.getElementById('ajax-result').innerHTML = '❌ AJAX call failed: ' + error + '<br>Response: ' + xhr.responseText;
        }
    });
}
</script>

<button onclick="testAjaxCall()">Test AJAX Call</button>
<div id="ajax-result" style="margin-top: 10px; padding: 10px; border: 1px solid #ccc;"></div>

<?php
echo "<h2>7. WordPress Error Log</h2>";
echo "Check your WordPress error log for any HAPD Modal debug messages.<br>";
echo "Error log location is typically: " . ini_get('error_log') . "<br>";
?>
