<?php
/**
 * Quick Product Check - Hearing Aid Product Display Plugin
 * 
 * Simple script to quickly check imported products and image status
 * 
 * @package HearingAidProductDisplay
 * @since 1.1.1
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // If not in WordPress, try to load WordPress
    $wp_load_paths = array(
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php',
    );
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress not found. Please run this from WordPress admin or ensure wp-load.php is accessible.');
    }
}

// Only allow admin users to run this check
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Quick Product Check - Hearing Aid Display</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #0073aa; color: white; padding: 20px; margin: -20px -20px 20px -20px; border-radius: 8px 8px 0 0; }
        .status { padding: 5px 10px; border-radius: 3px; color: white; font-weight: bold; }
        .success { background: #46b450; }
        .warning { background: #ffb900; }
        .error { background: #dc3232; }
        .info { background: #00a0d2; }
        .product-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .product-card { border: 1px solid #ddd; border-radius: 5px; padding: 15px; background: #f9f9f9; }
        .product-image { width: 100px; height: 100px; object-fit: cover; border-radius: 5px; float: left; margin-right: 15px; }
        .no-image { width: 100px; height: 100px; background: #ddd; border-radius: 5px; float: left; margin-right: 15px; display: flex; align-items: center; justify-content: center; color: #666; font-size: 12px; text-align: center; }
        .product-info { overflow: hidden; }
        .product-title { font-weight: bold; margin-bottom: 5px; }
        .product-meta { font-size: 12px; color: #666; margin: 2px 0; }
        .fix-button { background: #46b450; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px 5px 5px 0; }
        .fix-button:hover { background: #3e9b47; color: white; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-box { background: #f0f0f1; padding: 15px; border-radius: 5px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #0073aa; }
        .stat-label { font-size: 12px; color: #666; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎧 Quick Product Check</h1>
            <p>Hearing Aid Product Display Plugin - Status Overview</p>
        </div>

        <?php
        // Get all hearing aid products
        $products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => array('publish', 'draft', 'private'),
            'numberposts' => -1,
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        $total_products = count($products);
        $published_products = 0;
        $draft_products = 0;
        $products_with_images = 0;
        $products_without_images = 0;
        $broken_images = 0;

        // Analyze products
        foreach ($products as $product) {
            if ($product->post_status === 'publish') {
                $published_products++;
            } elseif ($product->post_status === 'draft') {
                $draft_products++;
            }

            if (has_post_thumbnail($product->ID)) {
                $products_with_images++;
                
                // Check if image file exists
                $thumbnail_id = get_post_thumbnail_id($product->ID);
                $image_path = get_attached_file($thumbnail_id);
                if (!$image_path || !file_exists($image_path)) {
                    $broken_images++;
                }
            } else {
                $products_without_images++;
            }
        }

        // Check plugin status
        $plugin_active = is_plugin_active('hearing-aid-product-display/hearing-aid-product-display.php');
        $post_type_exists = post_type_exists('hearing_aid_product');
        
        // Check image sizes
        global $_wp_additional_image_sizes;
        $custom_sizes = array('hapd-product-thumbnail', 'hapd-product-medium', 'hapd-product-large', 'hapd-product-gallery');
        $registered_sizes = 0;
        foreach ($custom_sizes as $size) {
            if (isset($_wp_additional_image_sizes[$size])) {
                $registered_sizes++;
            }
        }
        ?>

        <!-- Statistics Overview -->
        <div class="section">
            <h2>📊 Overview Statistics</h2>
            <div class="stats">
                <div class="stat-box">
                    <div class="stat-number"><?php echo $total_products; ?></div>
                    <div class="stat-label">Total Products</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number"><?php echo $published_products; ?></div>
                    <div class="stat-label">Published</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number"><?php echo $draft_products; ?></div>
                    <div class="stat-label">Draft</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number"><?php echo $products_with_images; ?></div>
                    <div class="stat-label">With Images</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number"><?php echo $products_without_images; ?></div>
                    <div class="stat-label">Without Images</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number"><?php echo $broken_images; ?></div>
                    <div class="stat-label">Broken Images</div>
                </div>
            </div>
        </div>

        <!-- Plugin Status -->
        <div class="section">
            <h2>🔧 Plugin Status</h2>
            <p><strong>Plugin Active:</strong> <span class="status <?php echo $plugin_active ? 'success' : 'error'; ?>"><?php echo $plugin_active ? 'YES' : 'NO'; ?></span></p>
            <p><strong>Post Type Registered:</strong> <span class="status <?php echo $post_type_exists ? 'success' : 'error'; ?>"><?php echo $post_type_exists ? 'YES' : 'NO'; ?></span></p>
            <p><strong>Custom Image Sizes:</strong> <span class="status <?php echo $registered_sizes === 4 ? 'success' : 'warning'; ?>"><?php echo $registered_sizes; ?>/4 Registered</span></p>
        </div>

        <!-- Issues & Solutions -->
        <?php if ($draft_products > 0 || $products_without_images > 0 || $broken_images > 0): ?>
        <div class="section">
            <h2>⚠️ Issues Found</h2>
            
            <?php if ($draft_products > 0): ?>
            <p><span class="status warning">DRAFT PRODUCTS</span> <?php echo $draft_products; ?> products are in draft status and won't show on frontend.</p>
            <p><strong>Solution:</strong> <a href="<?php echo admin_url('edit.php?post_type=hearing_aid_product'); ?>" class="fix-button">Go to Products → Bulk Edit → Publish</a></p>
            <?php endif; ?>

            <?php if ($products_without_images > 0): ?>
            <p><span class="status error">MISSING IMAGES</span> <?php echo $products_without_images; ?> products don't have featured images.</p>
            <p><strong>Solution:</strong> <a href="fix-image-display.php" class="fix-button">Run Image Fix Script</a></p>
            <?php endif; ?>

            <?php if ($broken_images > 0): ?>
            <p><span class="status error">BROKEN IMAGES</span> <?php echo $broken_images; ?> products have broken image files.</p>
            <p><strong>Solution:</strong> <a href="fix-image-display.php" class="fix-button">Run Image Fix Script</a></p>
            <?php endif; ?>

            <?php if ($registered_sizes < 4): ?>
            <p><span class="status warning">IMAGE SIZES</span> Not all custom image sizes are registered.</p>
            <p><strong>Solution:</strong> <a href="fix-image-display.php" class="fix-button">Run Image Fix Script</a></p>
            <?php endif; ?>
        </div>
        <?php else: ?>
        <div class="section">
            <h2>✅ All Good!</h2>
            <p><span class="status success">NO ISSUES FOUND</span> Your hearing aid products appear to be properly configured.</p>
        </div>
        <?php endif; ?>

        <!-- Recent Products -->
        <?php if ($total_products > 0): ?>
        <div class="section">
            <h2>📦 Recent Products (Last 10)</h2>
            <div class="product-grid">
                <?php
                $recent_products = array_slice($products, 0, 10);
                foreach ($recent_products as $product):
                    $model_no = get_post_meta($product->ID, '_hapd_model_number', true);
                    $price = get_post_meta($product->ID, '_hapd_price', true);
                    $has_image = has_post_thumbnail($product->ID);
                    $thumbnail_id = $has_image ? get_post_thumbnail_id($product->ID) : 0;
                    $image_url = $has_image ? wp_get_attachment_image_url($thumbnail_id, 'thumbnail') : '';
                ?>
                <div class="product-card">
                    <?php if ($has_image && $image_url): ?>
                        <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($product->post_title); ?>" class="product-image">
                    <?php else: ?>
                        <div class="no-image">No Image</div>
                    <?php endif; ?>
                    
                    <div class="product-info">
                        <div class="product-title"><?php echo esc_html($product->post_title); ?></div>
                        <div class="product-meta"><strong>Status:</strong> 
                            <span class="status <?php echo $product->post_status === 'publish' ? 'success' : 'warning'; ?>">
                                <?php echo strtoupper($product->post_status); ?>
                            </span>
                        </div>
                        <?php if ($model_no): ?>
                        <div class="product-meta"><strong>Model:</strong> <?php echo esc_html($model_no); ?></div>
                        <?php endif; ?>
                        <?php if ($price): ?>
                        <div class="product-meta"><strong>Price:</strong> $<?php echo esc_html($price); ?></div>
                        <?php endif; ?>
                        <div class="product-meta"><strong>Image:</strong> 
                            <span class="status <?php echo $has_image ? 'success' : 'error'; ?>">
                                <?php echo $has_image ? 'YES' : 'NO'; ?>
                            </span>
                            <?php if ($has_image): ?>
                                (ID: <?php echo $thumbnail_id; ?>)
                            <?php endif; ?>
                        </div>
                        <div class="product-meta">
                            <a href="<?php echo get_edit_post_link($product->ID); ?>">Edit</a> | 
                            <a href="<?php echo get_permalink($product->ID); ?>" target="_blank">View</a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Action Buttons -->
        <div class="section">
            <h2>🛠️ Available Tools</h2>
            <p>
                <a href="diagnose-imported-products.php" class="fix-button">🔍 Full Diagnostic</a>
                <a href="fix-image-display.php" class="fix-button">🔧 Auto Fix Images</a>
                <a href="<?php echo admin_url('edit.php?post_type=hearing_aid_product'); ?>" class="fix-button">📝 Manage Products</a>
                <a href="<?php echo admin_url('admin.php?page=hearing-aid-import-export'); ?>" class="fix-button">📊 Import/Export</a>
            </p>
            <p><em>Refresh this page after running fixes to see updated status.</em></p>
        </div>
    </div>
</body>
</html>
