<?php
/**
 * Quick Diagnosis Script for Hearing Aid Products
 * 
 * This script helps diagnose why imported products are not displaying
 * Run this by accessing it directly in your browser
 */

// WordPress Bootstrap
require_once('../../../wp-config.php');

// Set content type
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Hearing Aid Products - Quick Diagnosis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 5px; }
        .success { color: #008000; }
        .error { color: #cc0000; }
        .warning { color: #ff8800; }
        .info { color: #0073aa; }
        pre { background: #f9f9f9; padding: 10px; border-left: 4px solid #0073aa; overflow-x: auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h2 { color: #23282d; border-bottom: 2px solid #0073aa; padding-bottom: 5px; }
        h3 { color: #32373c; }
        .fix-button { background: #0073aa; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px 0; }
        .fix-button:hover { background: #005a87; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Hearing Aid Products - Quick Diagnosis</h1>
        
        <?php
        echo "<div class='section'>";
        echo "<h2>1. Plugin Status Check</h2>";
        
        // Check if plugin is active
        if (is_plugin_active('hearing-aid-product-display/hearing-aid-product-display.php')) {
            echo "<p class='success'>✅ Plugin is active</p>";
        } else {
            echo "<p class='error'>❌ Plugin is NOT active</p>";
        }
        
        // Check if post type exists
        if (post_type_exists('hearing_aid_product')) {
            echo "<p class='success'>✅ hearing_aid_product post type is registered</p>";
        } else {
            echo "<p class='error'>❌ hearing_aid_product post type is NOT registered</p>";
        }
        
        echo "</div>";
        
        echo "<div class='section'>";
        echo "<h2>2. Products in Database</h2>";
        
        // Check for products
        $all_products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => array('publish', 'draft', 'private'),
            'numberposts' => -1,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        if (empty($all_products)) {
            echo "<p class='error'>❌ No hearing aid products found in database</p>";
            echo "<p class='info'>This suggests the import may have failed completely.</p>";
        } else {
            echo "<p class='success'>✅ Found " . count($all_products) . " hearing aid products in database</p>";
            
            // Check status breakdown
            $published = 0;
            $draft = 0;
            $private = 0;
            
            foreach ($all_products as $product) {
                switch ($product->post_status) {
                    case 'publish':
                        $published++;
                        break;
                    case 'draft':
                        $draft++;
                        break;
                    case 'private':
                        $private++;
                        break;
                }
            }
            
            echo "<p><strong>Status Breakdown:</strong></p>";
            echo "<ul>";
            echo "<li class='" . ($published > 0 ? 'success' : 'warning') . "'>Published: $published</li>";
            echo "<li class='" . ($draft > 0 ? 'warning' : 'info') . "'>Draft: $draft</li>";
            echo "<li class='" . ($private > 0 ? 'warning' : 'info') . "'>Private: $private</li>";
            echo "</ul>";
            
            if ($published == 0 && $draft > 0) {
                echo "<p class='warning'>⚠️ All products are in draft status - they won't display on frontend!</p>";
                echo "<a href='?action=publish_drafts' class='fix-button'>Publish All Draft Products</a>";
            }
        }
        
        echo "</div>";
        
        echo "<div class='section'>";
        echo "<h2>3. Recent Products Sample</h2>";
        
        if (!empty($all_products)) {
            $recent_products = array_slice($all_products, 0, 5);
            echo "<table border='1' cellpadding='5' cellspacing='0' style='width:100%; border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Title</th><th>Status</th><th>Date</th><th>Meta Data</th></tr>";
            
            foreach ($recent_products as $product) {
                $meta_count = count(get_post_meta($product->ID));
                $status_class = $product->post_status == 'publish' ? 'success' : 'warning';
                
                echo "<tr>";
                echo "<td>{$product->ID}</td>";
                echo "<td>" . esc_html($product->post_title) . "</td>";
                echo "<td class='$status_class'>{$product->post_status}</td>";
                echo "<td>{$product->post_date}</td>";
                echo "<td>$meta_count meta fields</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        echo "</div>";
        
        echo "<div class='section'>";
        echo "<h2>4. Frontend Display Check</h2>";
        
        // Check shortcode
        if (shortcode_exists('hearing_aid_products')) {
            echo "<p class='success'>✅ [hearing_aid_products] shortcode is registered</p>";
        } else {
            echo "<p class='error'>❌ [hearing_aid_products] shortcode is NOT registered</p>";
        }
        
        // Check if frontend class exists
        if (class_exists('HAPD_Frontend')) {
            echo "<p class='success'>✅ HAPD_Frontend class is loaded</p>";
        } else {
            echo "<p class='error'>❌ HAPD_Frontend class is NOT loaded</p>";
        }
        
        // Test shortcode output
        if ($published > 0) {
            echo "<h3>Shortcode Test Output:</h3>";
            echo "<div style='border: 1px solid #ddd; padding: 10px; background: #f9f9f9;'>";
            echo do_shortcode('[hearing_aid_products]');
            echo "</div>";
        }
        
        echo "</div>";
        
        // Handle actions
        if (isset($_GET['action']) && $_GET['action'] == 'publish_drafts') {
            echo "<div class='section'>";
            echo "<h2>5. Publishing Draft Products</h2>";
            
            $draft_products = get_posts(array(
                'post_type' => 'hearing_aid_product',
                'post_status' => 'draft',
                'numberposts' => -1
            ));
            
            $published_count = 0;
            foreach ($draft_products as $product) {
                $result = wp_update_post(array(
                    'ID' => $product->ID,
                    'post_status' => 'publish'
                ));
                
                if (!is_wp_error($result)) {
                    $published_count++;
                }
            }
            
            echo "<p class='success'>✅ Published $published_count products</p>";
            echo "<p><a href='?' class='fix-button'>Refresh Diagnosis</a></p>";
            echo "</div>";
        }
        
        echo "<div class='section'>";
        echo "<h2>6. Common Solutions</h2>";
        echo "<ul>";
        echo "<li><strong>Products not showing:</strong> Check if they are published (not draft)</li>";
        echo "<li><strong>Shortcode not working:</strong> Make sure you're using [hearing_aid_products] exactly</li>";
        echo "<li><strong>Block not working:</strong> Search for 'Hearing Aid Products' in block editor</li>";
        echo "<li><strong>Theme issues:</strong> Try switching to a default theme temporarily</li>";
        echo "<li><strong>Caching:</strong> Clear any caching plugins</li>";
        echo "</ul>";
        echo "</div>";
        ?>
        
        <div class="section">
            <h2>7. Next Steps</h2>
            <p>If products are still not displaying after publishing them:</p>
            <ol>
                <li>Check your theme's compatibility</li>
                <li>Disable caching plugins temporarily</li>
                <li>Try the shortcode on a test page: <code>[hearing_aid_products]</code></li>
                <li>Check browser console for JavaScript errors</li>
                <li>Verify WordPress REST API is working: <code>/wp-json/wp/v2/hearing_aid_product</code></li>
            </ol>
        </div>
    </div>
</body>
</html>
