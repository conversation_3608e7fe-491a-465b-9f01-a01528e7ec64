/**
 * Hearing Aid Product Display - Admin Styles
 * Styles for the WordPress admin interface
 */

/* ==========================================================================
   Meta Box Styles
   ========================================================================== */

.hapd-meta-box {
    padding: 0;
}

.hapd-meta-box .inside {
    margin: 0;
    padding: 0;
}

/* Product Details Meta Box */
.hapd-product-details .form-table {
    margin: 0;
}

.hapd-product-details .form-table th {
    width: 150px;
    padding: 15px 10px 15px 0;
    vertical-align: top;
}

.hapd-product-details .form-table td {
    padding: 15px 10px;
}

.hapd-product-details .regular-text {
    width: 100%;
    max-width: 300px;
}

/* Specifications Meta Box */
.hapd-specs-header {
    padding: 15px;
    border-bottom: 1px solid #e1e1e1;
    background: #f9f9f9;
}

.hapd-specs-list {
    padding: 15px;
}

.hapd-spec-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.hapd-spec-row:last-child {
    margin-bottom: 0;
}

.hapd-spec-name,
.hapd-spec-value {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.hapd-spec-name {
    max-width: 200px;
}

.hapd-remove-spec,
.hapd-remove-feature {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    white-space: nowrap;
}

.hapd-remove-spec:hover,
.hapd-remove-feature:hover {
    background: #c82333;
}

.hapd-add-spec,
.hapd-add-feature {
    background: #007cba;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.hapd-add-spec:hover,
.hapd-add-feature:hover {
    background: #005a87;
}

/* Features Meta Box */
.hapd-features-header {
    padding: 15px;
    border-bottom: 1px solid #e1e1e1;
    background: #f9f9f9;
}

.hapd-features-list {
    padding: 15px;
}

.hapd-feature-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.hapd-feature-row:last-child {
    margin-bottom: 0;
}

.hapd-feature-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Gallery Meta Box */
.hapd-gallery-container {
    padding: 15px;
}

.hapd-gallery-images {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
    min-height: 60px;
    border: 2px dashed #ddd;
    border-radius: 4px;
    padding: 10px;
    background: #fafafa;
}

.hapd-gallery-images:empty::before {
    content: "No images selected. Click 'Add Images' to add product photos.";
    color: #666;
    font-style: italic;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 40px;
}

.hapd-gallery-image {
    position: relative;
    display: inline-block;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    background: white;
}

.hapd-gallery-image img {
    display: block;
    width: 80px;
    height: 80px;
    object-fit: cover;
}

.hapd-remove-image {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #dc3545;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 14px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hapd-remove-image:hover {
    background: #c82333;
}

.hapd-add-gallery-images {
    background: #007cba;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
}

.hapd-add-gallery-images:hover {
    background: #005a87;
}

/* ==========================================================================
   Admin Page Styles
   ========================================================================== */

.hapd-admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e1e1e1;
}

.hapd-admin-header h1 {
    margin: 0;
    color: #23282d;
}

.hapd-admin-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.hapd-stat-card {
    background: white;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.hapd-stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #007cba;
    margin-bottom: 5px;
}

.hapd-stat-label {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hapd-products-table {
    background: white;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    overflow: hidden;
}

.hapd-products-table .wp-list-table {
    border: none;
}

.hapd-products-table .wp-list-table th,
.hapd-products-table .wp-list-table td {
    border-bottom: 1px solid #e1e1e1;
}

.hapd-products-table .wp-list-table th {
    background: #f9f9f9;
    font-weight: 600;
}

/* ==========================================================================
   Settings Page Styles
   ========================================================================== */

.hapd-settings-section {
    background: white;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    margin-bottom: 20px;
    overflow: hidden;
}

.hapd-settings-section h2 {
    margin: 0;
    padding: 15px 20px;
    background: #f9f9f9;
    border-bottom: 1px solid #e1e1e1;
    font-size: 1.1rem;
}

.hapd-settings-section .form-table {
    margin: 0;
}

.hapd-settings-section .form-table th {
    width: 200px;
    padding: 20px;
    vertical-align: top;
}

.hapd-settings-section .form-table td {
    padding: 20px;
}

.hapd-settings-help {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
    font-style: italic;
}

/* ==========================================================================
   Color Picker Styles
   ========================================================================== */

.hapd-color-picker {
    display: flex;
    align-items: center;
    gap: 10px;
}

.hapd-color-preview {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .hapd-admin-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .hapd-admin-stats {
        grid-template-columns: 1fr;
    }
    
    .hapd-spec-row,
    .hapd-feature-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .hapd-spec-name {
        max-width: none;
    }
    
    .hapd-gallery-images {
        justify-content: center;
    }
    
    .hapd-settings-section .form-table th,
    .hapd-settings-section .form-table td {
        display: block;
        width: 100%;
        padding: 10px 20px;
    }
    
    .hapd-settings-section .form-table th {
        padding-bottom: 5px;
        border-bottom: none;
    }
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.hapd-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;
}

.hapd-loading::before {
    content: "";
    width: 20px;
    height: 20px;
    border: 2px solid #e1e1e1;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: hapd-spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes hapd-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Notices and Alerts
   ========================================================================== */

.hapd-notice {
    padding: 12px 15px;
    border-left: 4px solid;
    margin: 15px 0;
    border-radius: 0 4px 4px 0;
}

.hapd-notice-success {
    background: #d4edda;
    border-left-color: #28a745;
    color: #155724;
}

.hapd-notice-error {
    background: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
}

.hapd-notice-warning {
    background: #fff3cd;
    border-left-color: #ffc107;
    color: #856404;
}

.hapd-notice-info {
    background: #d1ecf1;
    border-left-color: #17a2b8;
    color: #0c5460;
}

/* ==========================================================================
   Accessibility
   ========================================================================== */

.hapd-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles */
.hapd-add-spec:focus,
.hapd-add-feature:focus,
.hapd-remove-spec:focus,
.hapd-remove-feature:focus,
.hapd-add-gallery-images:focus,
.hapd-remove-image:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .hapd-spec-row,
    .hapd-feature-row,
    .hapd-gallery-images {
        border-color: #000;
    }
    
    .hapd-stat-card {
        border-color: #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .hapd-loading::before {
        animation: none;
    }
}

/* ==========================================================================
   Excel Import Styles
   ========================================================================== */

.hapd-import-export-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
    margin-top: 20px;
}

.hapd-export-section,
.hapd-excel-import-section,
.hapd-csv-import-section {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.hapd-excel-import-section {
    border-left: 4px solid #0073aa;
}

.hapd-csv-import-section {
    border-left: 4px solid #666;
}

.hapd-excel-columns-info {
    margin: 15px 0;
}

.hapd-excel-columns-info table {
    margin-bottom: 20px;
}

.hapd-excel-columns-info th {
    background: #f9f9f9;
    font-weight: 600;
}

.hapd-excel-import-form {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.hapd-excel-import-form h3 {
    margin-top: 0;
    color: #0073aa;
}

.hapd-excel-import-form input[type="file"] {
    margin: 10px 0;
    padding: 8px;
    border: 2px dashed #ddd;
    border-radius: 4px;
    background: #fff;
    width: 100%;
    max-width: 400px;
}

.hapd-excel-import-form input[type="file"]:hover {
    border-color: #0073aa;
}

#hapd-excel-import-btn {
    margin-left: 10px;
}

#hapd-excel-import-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Progress Modal Styles */
.hapd-progress-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hapd-progress-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
}

.hapd-progress-modal-content {
    position: relative;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.hapd-progress-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ddd;
    background: #f9f9f9;
}

.hapd-progress-modal-header h3 {
    margin: 0;
    color: #23282d;
}

.hapd-close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hapd-close-btn:hover {
    color: #000;
}

.hapd-progress-modal-body {
    padding: 20px;
}

.hapd-progress-bar-container {
    margin-bottom: 20px;
}

.hapd-progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.hapd-progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.hapd-progress-text {
    text-align: center;
    margin-top: 10px;
    font-weight: 600;
    color: #0073aa;
}

.hapd-progress-status {
    margin-bottom: 20px;
}

.hapd-import-summary {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.hapd-summary-stats {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.hapd-stat {
    text-align: center;
    flex: 1;
}

.hapd-stat-number {
    display: block;
    font-size: 2em;
    font-weight: bold;
    margin-bottom: 5px;
}

.hapd-stat-success .hapd-stat-number {
    color: #46b450;
}

.hapd-stat-error .hapd-stat-number {
    color: #dc3232;
}

.hapd-stat-warning .hapd-stat-number {
    color: #ffb900;
}

.hapd-stat-label {
    color: #666;
    font-size: 0.9em;
}

.hapd-import-messages {
    margin-top: 20px;
}

.hapd-messages-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.hapd-message {
    display: flex;
    align-items: flex-start;
    padding: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.hapd-message:last-child {
    border-bottom: none;
}

.hapd-message-icon {
    width: 16px;
    height: 16px;
    margin-right: 10px;
    margin-top: 2px;
    flex-shrink: 0;
}

.hapd-message-error .hapd-message-icon {
    background: #dc3232;
    border-radius: 50%;
}

.hapd-message-warning .hapd-message-icon {
    background: #ffb900;
    border-radius: 50%;
}

.hapd-message-info .hapd-message-icon {
    background: #0073aa;
    border-radius: 50%;
}

.hapd-message-text {
    flex: 1;
    font-size: 0.9em;
    line-height: 1.4;
}

.hapd-error-message {
    background: #ffeaea;
    border: 1px solid #dc3232;
    border-radius: 4px;
    padding: 15px;
    color: #dc3232;
}

.hapd-error-message h4 {
    margin-top: 0;
    color: #dc3232;
}

body.hapd-modal-open {
    overflow: hidden;
}

/* Excel Import Responsive Styles */
@media (max-width: 768px) {
    .hapd-summary-stats {
        flex-direction: column;
        gap: 10px;
    }

    .hapd-progress-modal-content {
        width: 95%;
        margin: 20px;
    }

    .hapd-excel-import-form input[type="file"] {
        max-width: 100%;
    }

    .hapd-excel-columns-info table {
        font-size: 0.9em;
    }
}
