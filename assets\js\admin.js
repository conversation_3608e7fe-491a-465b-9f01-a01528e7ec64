/**
 * Hearing Aid Product Display - Admin JavaScript
 * Interactive functionality for the WordPress admin interface
 */

(function($) {
    'use strict';
    
    /**
     * Admin functionality object
     */
    const HearingAidAdmin = {
        
        /**
         * Initialize admin functionality
         */
        init: function() {
            this.initMetaBoxes();
            this.initMediaUploader();
            this.initColorPickers();
            this.initFormValidation();
            this.initSortable();
            
            $(document).ready(() => {
                this.bindEvents();
            });
        },
        
        /**
         * Initialize meta boxes
         */
        initMetaBoxes: function() {
            // Specifications meta box
            this.initSpecifications();
            
            // Features meta box
            this.initFeatures();
            
            // Gallery meta box
            this.initGallery();
        },
        
        /**
         * Initialize specifications functionality
         */
        initSpecifications: function() {
            const $container = $('#hapd-specifications-container');
            if (!$container.length) return;
            
            let specIndex = $container.find('.hapd-spec-row').length;
            
            // Add specification
            $container.on('click', '.hapd-add-spec', function(e) {
                e.preventDefault();
                
                const template = $('#hapd-spec-template').html();
                if (template) {
                    const newRow = template.replace(/\{\{INDEX\}\}/g, specIndex);
                    $container.find('.hapd-specs-list').append(newRow);
                    specIndex++;
                    
                    // Focus on the new name field
                    $container.find('.hapd-spec-row:last .hapd-spec-name').focus();
                }
            });
            
            // Remove specification
            $container.on('click', '.hapd-remove-spec', function(e) {
                e.preventDefault();
                
                const $row = $(this).closest('.hapd-spec-row');
                const specName = $row.find('.hapd-spec-name').val();
                
                if (specName && !confirm('Are you sure you want to remove this specification?')) {
                    return;
                }
                
                $row.fadeOut(300, function() {
                    $(this).remove();
                    HearingAidAdmin.announceToScreenReader('Specification removed');
                });
            });
            
            // Auto-resize text inputs
            $container.on('input', '.hapd-spec-name, .hapd-spec-value', function() {
                this.style.width = Math.max(this.value.length * 8, 100) + 'px';
            });
        },
        
        /**
         * Initialize features functionality
         */
        initFeatures: function() {
            const $container = $('#hapd-features-container');
            if (!$container.length) return;
            
            let featureIndex = $container.find('.hapd-feature-row').length;
            
            // Add feature
            $container.on('click', '.hapd-add-feature', function(e) {
                e.preventDefault();
                
                const template = $('#hapd-feature-template').html();
                if (template) {
                    const newRow = template.replace(/\{\{INDEX\}\}/g, featureIndex);
                    $container.find('.hapd-features-list').append(newRow);
                    featureIndex++;
                    
                    // Focus on the new input field
                    $container.find('.hapd-feature-row:last .hapd-feature-input').focus();
                }
            });
            
            // Remove feature
            $container.on('click', '.hapd-remove-feature', function(e) {
                e.preventDefault();
                
                const $row = $(this).closest('.hapd-feature-row');
                const featureText = $row.find('.hapd-feature-input').val();
                
                if (featureText && !confirm('Are you sure you want to remove this feature?')) {
                    return;
                }
                
                $row.fadeOut(300, function() {
                    $(this).remove();
                    HearingAidAdmin.announceToScreenReader('Feature removed');
                });
            });
        },
        
        /**
         * Initialize gallery functionality
         */
        initGallery: function() {
            const $container = $('#hapd-gallery-container');
            if (!$container.length) return;
            
            let mediaUploader;
            
            // Add images button
            $container.on('click', '.hapd-add-gallery-images', function(e) {
                e.preventDefault();
                
                // If the media uploader already exists, reopen it
                if (mediaUploader) {
                    mediaUploader.open();
                    return;
                }
                
                // Create the media uploader
                mediaUploader = wp.media({
                    title: 'Select Product Images',
                    button: {
                        text: 'Add to Gallery'
                    },
                    multiple: true,
                    library: {
                        type: 'image'
                    }
                });
                
                // When images are selected
                mediaUploader.on('select', function() {
                    const attachments = mediaUploader.state().get('selection').toJSON();
                    const $imagesContainer = $container.find('.hapd-gallery-images');
                    const $hiddenInput = $container.find('input[name="hapd_gallery_ids"]');
                    
                    let currentIds = $hiddenInput.val() ? $hiddenInput.val().split(',') : [];
                    
                    attachments.forEach(function(attachment) {
                        if (currentIds.indexOf(attachment.id.toString()) === -1) {
                            currentIds.push(attachment.id.toString());
                            
                            const imageHtml = `
                                <div class="hapd-gallery-image" data-attachment-id="${attachment.id}">
                                    <img src="${attachment.sizes.thumbnail ? attachment.sizes.thumbnail.url : attachment.url}" alt="${attachment.alt || attachment.title}">
                                    <button type="button" class="hapd-remove-image" aria-label="Remove image">×</button>
                                </div>
                            `;
                            
                            $imagesContainer.append(imageHtml);
                        }
                    });
                    
                    $hiddenInput.val(currentIds.join(','));
                    HearingAidAdmin.announceToScreenReader(`${attachments.length} image(s) added to gallery`);
                });
                
                mediaUploader.open();
            });
            
            // Remove image
            $container.on('click', '.hapd-remove-image', function(e) {
                e.preventDefault();
                
                const $imageContainer = $(this).closest('.hapd-gallery-image');
                const attachmentId = $imageContainer.data('attachment-id');
                const $hiddenInput = $container.find('input[name="hapd_gallery_ids"]');
                
                if (!confirm('Are you sure you want to remove this image?')) {
                    return;
                }
                
                // Remove from hidden input
                let currentIds = $hiddenInput.val() ? $hiddenInput.val().split(',') : [];
                currentIds = currentIds.filter(id => id !== attachmentId.toString());
                $hiddenInput.val(currentIds.join(','));
                
                // Remove from display
                $imageContainer.fadeOut(300, function() {
                    $(this).remove();
                    HearingAidAdmin.announceToScreenReader('Image removed from gallery');
                });
            });
        },
        
        /**
         * Initialize media uploader for other fields
         */
        initMediaUploader: function() {
            // Featured image replacement (if needed)
            $('.hapd-upload-image').on('click', function(e) {
                e.preventDefault();
                
                const $button = $(this);
                const $input = $button.siblings('input[type="hidden"]');
                const $preview = $button.siblings('.hapd-image-preview');
                
                const mediaUploader = wp.media({
                    title: 'Select Image',
                    button: {
                        text: 'Use This Image'
                    },
                    multiple: false,
                    library: {
                        type: 'image'
                    }
                });
                
                mediaUploader.on('select', function() {
                    const attachment = mediaUploader.state().get('selection').first().toJSON();
                    
                    $input.val(attachment.id);
                    $preview.html(`<img src="${attachment.sizes.thumbnail ? attachment.sizes.thumbnail.url : attachment.url}" alt="${attachment.alt || attachment.title}">`);
                    
                    HearingAidAdmin.announceToScreenReader('Image selected');
                });
                
                mediaUploader.open();
            });
        },
        
        /**
         * Initialize color pickers
         */
        initColorPickers: function() {
            if ($.fn.wpColorPicker) {
                $('.hapd-color-picker').wpColorPicker({
                    change: function(event, ui) {
                        const color = ui.color.toString();
                        $(this).trigger('hapd:color-changed', [color]);
                    }
                });
            }
        },
        
        /**
         * Initialize form validation
         */
        initFormValidation: function() {
            // Price validation
            $(document).on('input', 'input[name="hapd_price"]', function() {
                const value = $(this).val();
                const isValid = /^\d*\.?\d*$/.test(value);
                
                if (!isValid && value !== '') {
                    $(this).addClass('hapd-invalid');
                    this.setCustomValidity('Please enter a valid price (numbers and decimal point only)');
                } else {
                    $(this).removeClass('hapd-invalid');
                    this.setCustomValidity('');
                }
            });
            
            // Model number validation
            $(document).on('input', 'input[name="hapd_model_number"]', function() {
                const value = $(this).val();
                if (value.length > 50) {
                    $(this).addClass('hapd-invalid');
                    this.setCustomValidity('Model number must be 50 characters or less');
                } else {
                    $(this).removeClass('hapd-invalid');
                    this.setCustomValidity('');
                }
            });
            
            // Required field validation
            $(document).on('blur', '.hapd-required', function() {
                if (!$(this).val().trim()) {
                    $(this).addClass('hapd-invalid');
                    this.setCustomValidity('This field is required');
                } else {
                    $(this).removeClass('hapd-invalid');
                    this.setCustomValidity('');
                }
            });
        },
        
        /**
         * Initialize sortable functionality
         */
        initSortable: function() {
            if ($.fn.sortable) {
                // Make specifications sortable
                $('.hapd-specs-list').sortable({
                    handle: '.hapd-spec-name',
                    placeholder: 'hapd-sortable-placeholder',
                    helper: 'clone',
                    opacity: 0.8,
                    start: function(e, ui) {
                        ui.placeholder.height(ui.item.height());
                    },
                    stop: function() {
                        HearingAidAdmin.announceToScreenReader('Specifications reordered');
                    }
                });
                
                // Make features sortable
                $('.hapd-features-list').sortable({
                    handle: '.hapd-feature-input',
                    placeholder: 'hapd-sortable-placeholder',
                    helper: 'clone',
                    opacity: 0.8,
                    start: function(e, ui) {
                        ui.placeholder.height(ui.item.height());
                    },
                    stop: function() {
                        HearingAidAdmin.announceToScreenReader('Features reordered');
                    }
                });
                
                // Make gallery images sortable
                $('.hapd-gallery-images').sortable({
                    placeholder: 'hapd-sortable-placeholder',
                    helper: 'clone',
                    opacity: 0.8,
                    stop: function() {
                        // Update hidden input with new order
                        const $container = $(this).closest('#hapd-gallery-container');
                        const $hiddenInput = $container.find('input[name="hapd_gallery_ids"]');
                        const newOrder = [];
                        
                        $(this).find('.hapd-gallery-image').each(function() {
                            newOrder.push($(this).data('attachment-id'));
                        });
                        
                        $hiddenInput.val(newOrder.join(','));
                        HearingAidAdmin.announceToScreenReader('Gallery images reordered');
                    }
                });
            }
        },
        
        /**
         * Bind additional events
         */
        bindEvents: function() {
            // Auto-save functionality
            let autoSaveTimeout;
            $(document).on('input', '.hapd-auto-save', function() {
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(() => {
                    HearingAidAdmin.autoSave();
                }, 2000);
            });
            
            // Keyboard shortcuts
            $(document).on('keydown', function(e) {
                // Ctrl/Cmd + S to save
                if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                    e.preventDefault();
                    $('#publish, #save-post').click();
                    HearingAidAdmin.announceToScreenReader('Saving...');
                }
            });
            
            // Help tooltips
            $('.hapd-help-tooltip').on('mouseenter focus', function() {
                const helpText = $(this).data('help');
                if (helpText) {
                    const $tooltip = $('<div class="hapd-tooltip">' + helpText + '</div>');
                    $('body').append($tooltip);
                    
                    const offset = $(this).offset();
                    $tooltip.css({
                        top: offset.top - $tooltip.outerHeight() - 10,
                        left: offset.left + ($(this).outerWidth() / 2) - ($tooltip.outerWidth() / 2)
                    });
                }
            }).on('mouseleave blur', function() {
                $('.hapd-tooltip').remove();
            });
        },
        
        /**
         * Auto-save functionality
         */
        autoSave: function() {
            if (typeof wp !== 'undefined' && wp.autosave) {
                wp.autosave.server.triggerSave();
                this.showNotice('Draft saved automatically', 'success', 2000);
            }
        },
        
        /**
         * Show admin notice
         */
        showNotice: function(message, type = 'info', duration = 5000) {
            const $notice = $(`
                <div class="hapd-notice hapd-notice-${type}">
                    <p>${message}</p>
                </div>
            `);
            
            $('.wrap h1').after($notice);
            
            if (duration > 0) {
                setTimeout(() => {
                    $notice.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, duration);
            }
        },
        
        /**
         * Announce to screen readers
         */
        announceToScreenReader: function(text) {
            const $announcement = $('<div class="hapd-sr-only" aria-live="polite" aria-atomic="true"></div>');
            $announcement.text(text);
            $('body').append($announcement);
            
            setTimeout(() => {
                $announcement.remove();
            }, 1000);
        },
        
        /**
         * Validate form before submission
         */
        validateForm: function() {
            let isValid = true;
            const errors = [];
            
            // Check required fields
            $('.hapd-required').each(function() {
                if (!$(this).val().trim()) {
                    isValid = false;
                    errors.push(`${$(this).attr('name')} is required`);
                    $(this).addClass('hapd-invalid');
                }
            });
            
            // Check price format
            const price = $('input[name="hapd_price"]').val();
            if (price && !/^\d*\.?\d*$/.test(price)) {
                isValid = false;
                errors.push('Price must be a valid number');
            }
            
            if (!isValid) {
                this.showNotice('Please fix the following errors: ' + errors.join(', '), 'error');
                return false;
            }
            
            return true;
        }
    };
    
    // Initialize admin functionality
    HearingAidAdmin.init();
    
    // Form submission validation
    $('form').on('submit', function(e) {
        if (!HearingAidAdmin.validateForm()) {
            e.preventDefault();
            return false;
        }
    });
    
    // Make it globally available
    window.HearingAidAdmin = HearingAidAdmin;
    
})(jQuery);
