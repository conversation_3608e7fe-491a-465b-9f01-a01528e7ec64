/**
 * Hearing Aid Product Display - Frontend JavaScript
 * Interactive functionality for the frontend display
 */

(function($) {
    'use strict';
    
    /**
     * Main plugin object
     */
    const HearingAidDisplay = {
        
        /**
         * Initialize the plugin
         */
        init: function() {
            this.initCarousel();
            this.initGallery();
            this.initImageZoom();
            this.initContactForms();
            this.initAccessibility();
            
            // Initialize on document ready and after AJAX loads
            $(document).ready(() => {
                this.reinitialize();
            });
            
            // Reinitialize after dynamic content loads
            $(document).on('hapd:content-loaded', () => {
                this.reinitialize();
            });
        },
        
        /**
         * Reinitialize all components
         */
        reinitialize: function() {
            this.initCarousel();
            this.initGallery();
            this.initImageZoom();
            this.initContactForms();
            this.initAccessibility();
        },
        
        /**
         * Initialize carousel functionality
         */
        initCarousel: function() {
            $('.hapd-products-carousel').each(function() {
                const $carousel = $(this);
                const $track = $carousel.find('.hapd-carousel-track');
                const $slides = $track.find('.hapd-carousel-slide');
                const $prevBtn = $carousel.find('.hapd-carousel-prev');
                const $nextBtn = $carousel.find('.hapd-carousel-next');
                const $dotsContainer = $carousel.find('.hapd-carousel-dots');
                
                if ($slides.length === 0) return;
                
                let currentSlide = 0;
                const totalSlides = $slides.length;
                let slidesToShow = HearingAidDisplay.getSlidesToShow($carousel);
                const maxSlide = Math.max(0, totalSlides - slidesToShow);
                
                // Create dots
                $dotsContainer.empty();
                for (let i = 0; i <= maxSlide; i++) {
                    const $dot = $('<button class="hapd-carousel-dot" aria-label="Go to slide ' + (i + 1) + '"></button>');
                    if (i === 0) $dot.addClass('active');
                    $dotsContainer.append($dot);
                }
                
                const $dots = $dotsContainer.find('.hapd-carousel-dot');
                
                // Update carousel position
                function updateCarousel() {
                    const translateX = -(currentSlide * (100 / slidesToShow));
                    $track.css('transform', `translateX(${translateX}%)`);
                    
                    // Update dots
                    $dots.removeClass('active').eq(currentSlide).addClass('active');
                    
                    // Update button states
                    $prevBtn.prop('disabled', currentSlide === 0);
                    $nextBtn.prop('disabled', currentSlide >= maxSlide);
                    
                    // Announce to screen readers
                    const announcement = `Slide ${currentSlide + 1} of ${maxSlide + 1}`;
                    HearingAidDisplay.announceToScreenReader(announcement);
                }
                
                // Previous button
                $prevBtn.on('click', function() {
                    if (currentSlide > 0) {
                        currentSlide--;
                        updateCarousel();
                    }
                });
                
                // Next button
                $nextBtn.on('click', function() {
                    if (currentSlide < maxSlide) {
                        currentSlide++;
                        updateCarousel();
                    }
                });
                
                // Dot navigation
                $dots.on('click', function() {
                    currentSlide = $(this).index();
                    updateCarousel();
                });
                
                // Keyboard navigation
                $carousel.on('keydown', function(e) {
                    if (e.key === 'ArrowLeft') {
                        e.preventDefault();
                        $prevBtn.click();
                    } else if (e.key === 'ArrowRight') {
                        e.preventDefault();
                        $nextBtn.click();
                    }
                });
                
                // Touch/swipe support
                let startX = 0;
                let isDragging = false;
                
                $track.on('touchstart mousedown', function(e) {
                    startX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
                    isDragging = true;
                    $track.css('cursor', 'grabbing');
                });
                
                $(document).on('touchmove mousemove', function(e) {
                    if (!isDragging) return;
                    
                    const currentX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
                    const diffX = startX - currentX;
                    
                    if (Math.abs(diffX) > 50) {
                        if (diffX > 0 && currentSlide < maxSlide) {
                            currentSlide++;
                            updateCarousel();
                        } else if (diffX < 0 && currentSlide > 0) {
                            currentSlide--;
                            updateCarousel();
                        }
                        isDragging = false;
                        $track.css('cursor', '');
                    }
                });
                
                $(document).on('touchend mouseup', function() {
                    isDragging = false;
                    $track.css('cursor', '');
                });
                
                // Handle window resize
                $(window).on('resize', HearingAidDisplay.debounce(function() {
                    const newSlidesToShow = HearingAidDisplay.getSlidesToShow($carousel);
                    if (newSlidesToShow !== slidesToShow) {
                        slidesToShow = newSlidesToShow;
                        const newMaxSlide = Math.max(0, totalSlides - slidesToShow);
                        if (currentSlide > newMaxSlide) {
                            currentSlide = newMaxSlide;
                        }
                        
                        // Recreate dots
                        $dotsContainer.empty();
                        for (let i = 0; i <= newMaxSlide; i++) {
                            const $dot = $('<button class="hapd-carousel-dot" aria-label="Go to slide ' + (i + 1) + '"></button>');
                            if (i === currentSlide) $dot.addClass('active');
                            $dotsContainer.append($dot);
                        }
                        
                        updateCarousel();
                    }
                }, 250));
                
                // Initial update
                updateCarousel();
            });
        },
        
        /**
         * Get number of slides to show based on screen size
         */
        getSlidesToShow: function($carousel) {
            const width = $(window).width();
            if (width <= 576) return 1;
            if (width <= 992) return 2;
            return 3;
        },
        
        /**
         * Initialize gallery functionality
         */
        initGallery: function() {
            $('.hapd-gallery-container').each(function() {
                const $gallery = $(this);
                const $mainImage = $gallery.find('.hapd-main-image');
                const $thumbnails = $gallery.find('.hapd-gallery-thumb');
                
                if ($thumbnails.length === 0) return;
                
                $thumbnails.on('click', function() {
                    const $thumb = $(this);
                    const largeImageSrc = $thumb.data('large-image');
                    const zoomImageSrc = $thumb.data('zoom-image');
                    
                    if (largeImageSrc) {
                        $mainImage.attr('src', largeImageSrc);
                        $mainImage.data('zoom-image', zoomImageSrc);
                        
                        // Update active thumbnail
                        $thumbnails.removeClass('active');
                        $thumb.addClass('active');
                        
                        // Update alt text
                        const altText = $thumb.attr('alt');
                        if (altText) {
                            $mainImage.attr('alt', altText);
                        }
                        
                        // Announce to screen readers
                        HearingAidDisplay.announceToScreenReader('Image changed');
                    }
                });
                
                // Keyboard navigation for thumbnails
                $thumbnails.on('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        $(this).click();
                    }
                });
            });
        },
        
        /**
         * Initialize image zoom functionality
         */
        initImageZoom: function() {
            if (typeof $.fn.zoom === 'function') {
                $('.hapd-main-image[data-zoom-image]').each(function() {
                    const $img = $(this);
                    const zoomImage = $img.data('zoom-image');
                    
                    if (zoomImage) {
                        $img.zoom({
                            url: zoomImage,
                            magnify: 1.5,
                            touch: false // Disable on touch devices to avoid conflicts
                        });
                    }
                });
            }
        },
        
        /**
         * Initialize contact form functionality
         */
        initContactForms: function() {
            // Handle contact button clicks
            $('.hapd-contact-email').on('click', function(e) {
                // Track contact email clicks
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'contact_email_click', {
                        'event_category': 'hearing_aid_products',
                        'event_label': $(this).closest('[data-product-id]').data('product-id')
                    });
                }
            });
            
            $('.hapd-contact-phone').on('click', function(e) {
                // Track contact phone clicks
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'contact_phone_click', {
                        'event_category': 'hearing_aid_products',
                        'event_label': $(this).closest('[data-product-id]').data('product-id')
                    });
                }
            });
            
            // Handle learn more button clicks for modal
            $('.hapd-learn-more-btn').on('click', function(e) {
                e.preventDefault();
                const productId = $(this).data('product-id');

                if (productId) {
                    HearingAidDisplay.openProductModal(productId);

                    // Analytics tracking
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'learn_more_click', {
                            'event_category': 'hearing_aid_products',
                            'event_label': productId
                        });
                    }
                }
            });

            // Handle regular learn more links (non-modal)
            $('.hapd-btn-primary:not(.hapd-learn-more-btn)').on('click', function(e) {
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'learn_more_click', {
                        'event_category': 'hearing_aid_products',
                        'event_label': $(this).closest('[data-product-id]').data('product-id')
                    });
                }
            });
        },
        
        /**
         * Initialize accessibility features
         */
        initAccessibility: function() {
            // Add ARIA labels to interactive elements
            $('.hapd-product-card').each(function() {
                const $card = $(this);
                const productTitle = $card.find('.hapd-product-title').text().trim();
                if (productTitle) {
                    $card.attr('aria-label', `Product: ${productTitle}`);
                }
            });
            
            // Ensure buttons have proper ARIA attributes
            $('.hapd-btn').each(function() {
                const $btn = $(this);
                if (!$btn.attr('aria-label') && !$btn.attr('title')) {
                    const btnText = $btn.text().trim();
                    if (btnText) {
                        $btn.attr('aria-label', btnText);
                    }
                }
            });
            
            // Add skip links for keyboard navigation
            if (!$('.hapd-skip-link').length) {
                const $skipLink = $('<a href="#hapd-main-content" class="hapd-skip-link hapd-sr-only">Skip to main content</a>');
                $skipLink.on('focus', function() {
                    $(this).removeClass('hapd-sr-only');
                }).on('blur', function() {
                    $(this).addClass('hapd-sr-only');
                });
                
                $('.hapd-products-container').first().before($skipLink);
                $('.hapd-products-container').first().attr('id', 'hapd-main-content');
            }
        },
        
        /**
         * Announce text to screen readers
         */
        announceToScreenReader: function(text) {
            const $announcement = $('<div class="hapd-sr-only" aria-live="polite" aria-atomic="true"></div>');
            $announcement.text(text);
            $('body').append($announcement);
            
            setTimeout(() => {
                $announcement.remove();
            }, 1000);
        },
        
        /**
         * Debounce function for performance
         */
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        /**
         * Lazy load images
         */
        initLazyLoading: function() {
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            const src = img.dataset.src;
                            if (src) {
                                img.src = src;
                                img.classList.remove('hapd-lazy');
                                img.classList.add('hapd-loaded');
                                observer.unobserve(img);
                            }
                        }
                    });
                });
                
                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            }
        },
        
        /**
         * Handle AJAX product loading
         */
        loadProducts: function(params, callback) {
            $.ajax({
                url: hapd_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'hapd_get_products',
                    nonce: hapd_ajax.nonce,
                    ...params
                },
                success: function(response) {
                    if (response.success) {
                        callback(response.data);
                    } else {
                        console.error('Error loading products:', response.data);
                        callback(null, response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', error);
                    callback(null, error);
                }
            });
        },

        /**
         * Open product modal
         */
        openProductModal: function(productId) {
            // Debug logging
            console.log('Opening modal for product ID:', productId);
            console.log('AJAX URL:', hapd_ajax ? hapd_ajax.ajax_url : 'hapd_ajax not defined');
            console.log('Nonce:', hapd_ajax ? hapd_ajax.nonce : 'hapd_ajax not defined');

            // Check if hapd_ajax is defined
            if (typeof hapd_ajax === 'undefined') {
                HearingAidDisplay.showModalError('AJAX configuration not found. Please refresh the page.');
                return;
            }

            // Show loading state
            this.showModalLoading();

            $.ajax({
                url: hapd_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'hapd_get_product_modal',
                    product_id: productId,
                    nonce: hapd_ajax.nonce
                },
                success: function(response) {
                    console.log('AJAX Response:', response);
                    if (response.success) {
                        HearingAidDisplay.showModal(response.data.content, response.data.title);
                    } else {
                        HearingAidDisplay.showModalError('Failed to load product details: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.log('AJAX Error:', xhr, status, error);
                    console.log('Response Text:', xhr.responseText);
                    HearingAidDisplay.showModalError('Error loading product details: ' + error);
                }
            });
        },

        /**
         * Show modal loading state
         */
        showModalLoading: function() {
            const modalHtml = `
                <div id="hapd-modal-overlay" class="hapd-modal-overlay">
                    <div class="hapd-modal">
                        <div class="hapd-modal-header">
                            <button class="hapd-modal-close" aria-label="Close">&times;</button>
                        </div>
                        <div class="hapd-modal-body">
                            <div class="hapd-modal-loading">
                                <div class="hapd-spinner"></div>
                                <p>Loading product details...</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);
            $('body').addClass('hapd-modal-open');

            // Close modal handlers
            this.initModalCloseHandlers();
        },

        /**
         * Show modal with content
         */
        showModal: function(content, title) {
            const modalHtml = `
                <div id="hapd-modal-overlay" class="hapd-modal-overlay">
                    <div class="hapd-modal">
                        <div class="hapd-modal-header">
                            <h2 class="hapd-modal-title">${title}</h2>
                            <button class="hapd-modal-close" aria-label="Close">&times;</button>
                        </div>
                        <div class="hapd-modal-body">
                            ${content}
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal
            $('#hapd-modal-overlay').remove();

            $('body').append(modalHtml);
            $('body').addClass('hapd-modal-open');

            // Close modal handlers
            this.initModalCloseHandlers();

            // Focus management for accessibility
            $('#hapd-modal-overlay .hapd-modal-close').focus();
        },

        /**
         * Show modal error
         */
        showModalError: function(message) {
            const modalHtml = `
                <div id="hapd-modal-overlay" class="hapd-modal-overlay">
                    <div class="hapd-modal">
                        <div class="hapd-modal-header">
                            <h2 class="hapd-modal-title">Error</h2>
                            <button class="hapd-modal-close" aria-label="Close">&times;</button>
                        </div>
                        <div class="hapd-modal-body">
                            <div class="hapd-modal-error">
                                <p>${message}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal
            $('#hapd-modal-overlay').remove();

            $('body').append(modalHtml);
            $('body').addClass('hapd-modal-open');

            // Close modal handlers
            this.initModalCloseHandlers();
        },

        /**
         * Initialize modal close handlers
         */
        initModalCloseHandlers: function() {
            // Close button
            $(document).on('click', '.hapd-modal-close', function(e) {
                e.preventDefault();
                HearingAidDisplay.closeModal();
            });

            // Overlay click
            $(document).on('click', '#hapd-modal-overlay', function(e) {
                if (e.target === this) {
                    HearingAidDisplay.closeModal();
                }
            });

            // Escape key
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape' && $('#hapd-modal-overlay').length) {
                    HearingAidDisplay.closeModal();
                }
            });
        },

        /**
         * Close modal
         */
        closeModal: function() {
            $('#hapd-modal-overlay').fadeOut(300, function() {
                $(this).remove();
                $('body').removeClass('hapd-modal-open');
            });

            // Remove event handlers
            $(document).off('click', '.hapd-modal-close');
            $(document).off('click', '#hapd-modal-overlay');
            $(document).off('keydown');
        }
    };
    
    // Initialize the plugin
    HearingAidDisplay.init();
    
    // Make it globally available
    window.HearingAidDisplay = HearingAidDisplay;
    
})(jQuery);
