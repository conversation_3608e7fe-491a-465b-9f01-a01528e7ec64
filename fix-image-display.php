<?php
/**
 * Fix Image Display Issues for Imported Hearing Aid Products
 * 
 * This script fixes common image display issues after Excel import
 * Run this from WordPress admin to fix image problems
 * 
 * @package HearingAidProductDisplay
 * @since 1.1.1
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // If not in WordPress, try to load WordPress
    $wp_load_paths = array(
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php',
    );
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress not found. Please run this from WordPress admin or ensure wp-load.php is accessible.');
    }
}

// Only allow admin users to run this fix
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

/**
 * Image Display Fix Class
 */
class HAPD_Image_Display_Fix {
    
    private $fixed_count = 0;
    private $error_count = 0;
    private $messages = array();
    
    /**
     * Run all fixes
     */
    public function run_fixes() {
        echo "<h1>Hearing Aid Product Display - Image Display Fix</h1>\n";
        echo "<div style='font-family: monospace; background: #f0f0f0; padding: 20px; margin: 20px 0;'>\n";
        
        $this->fix_image_sizes();
        $this->fix_missing_thumbnails();
        $this->fix_broken_image_attachments();
        $this->publish_draft_products();
        $this->regenerate_thumbnails();
        $this->fix_image_metadata();
        
        echo "</div>\n";
        
        $this->show_summary();
    }
    
    /**
     * Fix 1: Ensure image sizes are properly registered
     */
    private function fix_image_sizes() {
        echo "<h3>1. Fixing Image Sizes Registration</h3>\n";
        
        // Re-register image sizes
        add_image_size('hapd-product-thumbnail', 300, 300, true);
        add_image_size('hapd-product-medium', 600, 600, true);
        add_image_size('hapd-product-large', 1200, 1200, true);
        add_image_size('hapd-product-gallery', 800, 800, true);
        
        echo "✅ Re-registered all custom image sizes\n";
        $this->fixed_count++;
        
        // Check if they're now available
        global $_wp_additional_image_sizes;
        $required_sizes = array('hapd-product-thumbnail', 'hapd-product-medium', 'hapd-product-large', 'hapd-product-gallery');
        
        foreach ($required_sizes as $size) {
            if (isset($_wp_additional_image_sizes[$size])) {
                echo "✅ Confirmed: $size is now registered\n";
            } else {
                echo "❌ Warning: $size still not registered\n";
                $this->error_count++;
            }
        }
        
        echo "\n";
    }
    
    /**
     * Fix 2: Find products without thumbnails and try to assign images
     */
    private function fix_missing_thumbnails() {
        echo "<h3>2. Fixing Missing Product Thumbnails</h3>\n";
        
        $products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => array('publish', 'draft', 'private'),
            'numberposts' => -1,
            'meta_query' => array(
                array(
                    'key' => '_thumbnail_id',
                    'compare' => 'NOT EXISTS'
                )
            )
        ));
        
        if (empty($products)) {
            echo "✅ All products have thumbnails assigned\n\n";
            return;
        }
        
        echo "Found " . count($products) . " products without thumbnails\n";
        
        foreach ($products as $product) {
            // Look for attachments uploaded for this product
            $attachments = get_posts(array(
                'post_type' => 'attachment',
                'post_mime_type' => 'image',
                'post_parent' => $product->ID,
                'numberposts' => 1
            ));
            
            if (!empty($attachments)) {
                $attachment_id = $attachments[0]->ID;
                set_post_thumbnail($product->ID, $attachment_id);
                echo "✅ Assigned thumbnail to '{$product->post_title}' (Attachment ID: $attachment_id)\n";
                $this->fixed_count++;
            } else {
                // Look for images with similar names in media library
                $model_no = get_post_meta($product->ID, '_hapd_model_number', true);
                if ($model_no) {
                    $similar_images = get_posts(array(
                        'post_type' => 'attachment',
                        'post_mime_type' => 'image',
                        's' => $model_no,
                        'numberposts' => 1
                    ));
                    
                    if (!empty($similar_images)) {
                        $attachment_id = $similar_images[0]->ID;
                        set_post_thumbnail($product->ID, $attachment_id);
                        echo "✅ Found and assigned similar image to '{$product->post_title}' (Model: $model_no)\n";
                        $this->fixed_count++;
                    } else {
                        echo "⚠️  No image found for '{$product->post_title}' (Model: $model_no)\n";
                    }
                } else {
                    echo "⚠️  No image or model number for '{$product->post_title}'\n";
                }
            }
        }
        
        echo "\n";
    }
    
    /**
     * Fix 3: Fix broken image attachments
     */
    private function fix_broken_image_attachments() {
        echo "<h3>3. Fixing Broken Image Attachments</h3>\n";
        
        $products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => array('publish', 'draft', 'private'),
            'numberposts' => -1
        ));
        
        $broken_count = 0;
        $fixed_broken = 0;
        
        foreach ($products as $product) {
            if (has_post_thumbnail($product->ID)) {
                $thumbnail_id = get_post_thumbnail_id($product->ID);
                $image_path = get_attached_file($thumbnail_id);
                
                if (!$image_path || !file_exists($image_path)) {
                    $broken_count++;
                    echo "❌ Broken image for '{$product->post_title}' (Attachment ID: $thumbnail_id)\n";
                    
                    // Try to find the image in uploads directory
                    $upload_dir = wp_upload_dir();
                    $hapd_upload_dir = $upload_dir['basedir'] . '/hearing-aid-products/';
                    
                    if (file_exists($hapd_upload_dir)) {
                        $model_no = get_post_meta($product->ID, '_hapd_model_number', true);
                        if ($model_no) {
                            // Look for image files with model number in name
                            $possible_files = glob($hapd_upload_dir . '*' . $model_no . '*');
                            if (!empty($possible_files)) {
                                $image_file = $possible_files[0];
                                $new_attachment_id = $this->create_attachment_from_file($image_file, $product->ID);
                                if ($new_attachment_id) {
                                    set_post_thumbnail($product->ID, $new_attachment_id);
                                    echo "✅ Fixed by creating new attachment from local file\n";
                                    $fixed_broken++;
                                }
                            }
                        }
                    }
                }
            }
        }
        
        if ($broken_count === 0) {
            echo "✅ No broken image attachments found\n";
        } else {
            echo "Found $broken_count broken images, fixed $fixed_broken\n";
            $this->fixed_count += $fixed_broken;
            $this->error_count += ($broken_count - $fixed_broken);
        }
        
        echo "\n";
    }
    
    /**
     * Fix 4: Publish draft products
     */
    private function publish_draft_products() {
        echo "<h3>4. Publishing Draft Products</h3>\n";
        
        $draft_products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => 'draft',
            'numberposts' => -1
        ));
        
        if (empty($draft_products)) {
            echo "✅ No draft products found\n\n";
            return;
        }
        
        echo "Found " . count($draft_products) . " draft products\n";
        echo "Publishing them to make visible on frontend...\n";
        
        foreach ($draft_products as $product) {
            wp_update_post(array(
                'ID' => $product->ID,
                'post_status' => 'publish'
            ));
            echo "✅ Published '{$product->post_title}'\n";
            $this->fixed_count++;
        }
        
        echo "\n";
    }
    
    /**
     * Fix 5: Regenerate thumbnails for existing images
     */
    private function regenerate_thumbnails() {
        echo "<h3>5. Regenerating Thumbnails</h3>\n";
        
        $products = get_posts(array(
            'post_type' => 'hearing_aid_product',
            'post_status' => array('publish', 'draft', 'private'),
            'numberposts' => 10 // Limit to avoid timeout
        ));
        
        $regenerated = 0;
        
        foreach ($products as $product) {
            if (has_post_thumbnail($product->ID)) {
                $thumbnail_id = get_post_thumbnail_id($product->ID);
                $image_path = get_attached_file($thumbnail_id);
                
                if ($image_path && file_exists($image_path)) {
                    require_once(ABSPATH . 'wp-admin/includes/image.php');
                    $metadata = wp_generate_attachment_metadata($thumbnail_id, $image_path);
                    wp_update_attachment_metadata($thumbnail_id, $metadata);
                    $regenerated++;
                }
            }
        }
        
        echo "✅ Regenerated thumbnails for $regenerated images\n";
        $this->fixed_count += $regenerated;
        
        echo "\n";
    }
    
    /**
     * Fix 6: Fix image metadata
     */
    private function fix_image_metadata() {
        echo "<h3>6. Fixing Image Metadata</h3>\n";
        
        $attachments = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'numberposts' => 20,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        $fixed_metadata = 0;
        
        foreach ($attachments as $attachment) {
            $metadata = wp_get_attachment_metadata($attachment->ID);
            $file_path = get_attached_file($attachment->ID);
            
            if (!$metadata && $file_path && file_exists($file_path)) {
                require_once(ABSPATH . 'wp-admin/includes/image.php');
                $new_metadata = wp_generate_attachment_metadata($attachment->ID, $file_path);
                wp_update_attachment_metadata($attachment->ID, $new_metadata);
                $fixed_metadata++;
            }
        }
        
        echo "✅ Fixed metadata for $fixed_metadata images\n";
        $this->fixed_count += $fixed_metadata;
        
        echo "\n";
    }
    
    /**
     * Create attachment from local file
     */
    private function create_attachment_from_file($file_path, $parent_id = 0) {
        if (!file_exists($file_path)) {
            return false;
        }
        
        $filename = basename($file_path);
        $upload_dir = wp_upload_dir();
        
        // Copy file to uploads directory
        $new_file_path = $upload_dir['path'] . '/' . $filename;
        if (!copy($file_path, $new_file_path)) {
            return false;
        }
        
        // Create attachment
        $attachment = array(
            'post_mime_type' => wp_check_filetype($new_file_path)['type'],
            'post_title' => sanitize_file_name(pathinfo($filename, PATHINFO_FILENAME)),
            'post_content' => '',
            'post_status' => 'inherit'
        );
        
        $attachment_id = wp_insert_attachment($attachment, $new_file_path, $parent_id);
        
        if (!is_wp_error($attachment_id)) {
            require_once(ABSPATH . 'wp-admin/includes/image.php');
            $attachment_data = wp_generate_attachment_metadata($attachment_id, $new_file_path);
            wp_update_attachment_metadata($attachment_id, $attachment_data);
            return $attachment_id;
        }
        
        return false;
    }
    
    /**
     * Show summary
     */
    private function show_summary() {
        echo "<h2>Fix Summary</h2>\n";
        echo "<div style='background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px;'>\n";
        echo "<p><strong>✅ Issues Fixed:</strong> {$this->fixed_count}</p>\n";
        echo "<p><strong>❌ Issues Remaining:</strong> {$this->error_count}</p>\n";
        
        if ($this->fixed_count > 0) {
            echo "<p style='color: green;'><strong>Success!</strong> Fixed {$this->fixed_count} issues. Your imported products should now display correctly.</p>\n";
        }
        
        if ($this->error_count > 0) {
            echo "<p style='color: orange;'><strong>Note:</strong> {$this->error_count} issues still need manual attention.</p>\n";
        }
        
        echo "<h3>Next Steps:</h3>\n";
        echo "<ol>\n";
        echo "<li>Visit your website frontend to check if products are now displaying correctly</li>\n";
        echo "<li>Test the Gutenberg block: Add 'Hearing Aid Products' block to a page</li>\n";
        echo "<li>Test the shortcode: Add [hearing_aid_products] to a page</li>\n";
        echo "<li>If images still don't show, check the diagnostic script for more details</li>\n";
        echo "<li>Consider using a 'Regenerate Thumbnails' plugin for all images</li>\n";
        echo "</ol>\n";
        
        echo "</div>\n";
    }
}

// Run the fix if accessed directly
if (isset($_GET['run_fix']) || (defined('WP_CLI') && WP_CLI)) {
    $fix = new HAPD_Image_Display_Fix();
    $fix->run_fixes();
} else {
    // Show fix options
    echo "<h1>Hearing Aid Product Display - Image Display Fix</h1>\n";
    echo "<p>This tool will automatically fix common image display issues with imported products.</p>\n";
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<p><strong>⚠️ Important:</strong> This tool will make changes to your database. Please backup your site before proceeding.</p>\n";
    echo "</div>\n";
    echo "<p><a href='?run_fix=1' class='button button-primary'>Run Image Display Fix</a></p>\n";
    echo "<hr>\n";
    echo "<h2>What This Fix Will Do</h2>\n";
    echo "<ul>\n";
    echo "<li><strong>Re-register Image Sizes:</strong> Ensures custom image sizes are properly registered</li>\n";
    echo "<li><strong>Assign Missing Thumbnails:</strong> Finds and assigns images to products without thumbnails</li>\n";
    echo "<li><strong>Fix Broken Attachments:</strong> Repairs broken image attachments</li>\n";
    echo "<li><strong>Publish Draft Products:</strong> Makes imported products visible on frontend</li>\n";
    echo "<li><strong>Regenerate Thumbnails:</strong> Creates missing image sizes</li>\n";
    echo "<li><strong>Fix Image Metadata:</strong> Repairs missing image metadata</li>\n";
    echo "</ul>\n";
    echo "<p><em>Run the <a href='diagnose-imported-products.php'>diagnostic script</a> first to identify specific issues.</em></p>\n";
}
?>
